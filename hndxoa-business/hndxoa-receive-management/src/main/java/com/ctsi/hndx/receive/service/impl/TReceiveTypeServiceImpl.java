package com.ctsi.hndx.receive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.receive.entity.TReceiveType;
import com.ctsi.hndx.receive.entity.dto.TReceiveTypeDTO;
import com.ctsi.hndx.receive.mapper.TReceiveTypeMapper;
import com.ctsi.hndx.receive.service.ITReceiveTypeService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
/**
 * <p>
 * 流程类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */

@Slf4j
@Service
public class TReceiveTypeServiceImpl extends BaseTreeCurdServiceImpl<TReceiveTypeDTO,TReceiveType,TReceiveTypeMapper >
        implements ITReceiveTypeService {

    @Autowired
    private TReceiveTypeMapper tReceiveTypeMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TReceiveTypeDTO> queryListPage(TReceiveTypeDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TReceiveType> queryWrapper = new LambdaQueryWrapper();

        IPage<TReceiveType> pageData = tReceiveTypeMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TReceiveTypeDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TReceiveTypeDTO.class));

        return new PageResult<TReceiveTypeDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TReceiveTypeDTO> queryList(TReceiveTypeDTO entityDTO) {
        LambdaQueryWrapper<TReceiveType> queryWrapper = new LambdaQueryWrapper();
            List<TReceiveType> listData = tReceiveTypeMapper.selectList(queryWrapper);
            List<TReceiveTypeDTO> TReceiveTypeDTOList = ListCopyUtil.copy(listData, TReceiveTypeDTO.class);
        return TReceiveTypeDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TReceiveTypeDTO findOne(Long id) {
        TReceiveType  tReceiveType =  tReceiveTypeMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tReceiveType,TReceiveTypeDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TReceiveTypeDTO create(TReceiveTypeDTO entityDTO) {
        this.updateOrderBy(entityDTO);
       TReceiveType tReceiveType =  BeanConvertUtils.copyProperties(entityDTO,TReceiveType.class);
        save(tReceiveType);
        return  BeanConvertUtils.copyProperties(tReceiveType,TReceiveTypeDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TReceiveTypeDTO entity) {
        this.updateOrderBy(entity);
        TReceiveType tReceiveType = BeanConvertUtils.copyProperties(entity,TReceiveType.class);
        return tReceiveTypeMapper.updateById(tReceiveType);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tReceiveTypeMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TReceiveTypeId
     * @return
     */
    @Override
    public boolean existByTReceiveTypeId(Long TReceiveTypeId) {
        if (TReceiveTypeId != null) {
            LambdaQueryWrapper<TReceiveType> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TReceiveType::getId, TReceiveTypeId);
            List<TReceiveType> result = tReceiveTypeMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TReceiveTypeDTO> dataList) {
        List<TReceiveType> result = ListCopyUtil.copy(dataList, TReceiveType.class);
        return saveBatch(result);
    }


    @Override
    public List<TReceiveTypeDTO> getDataDtOFromDomin(List<TReceiveType> list) {
        return  ListCopyUtil.copy(list,TReceiveTypeDTO.class);
    }

    @Override
    public TReceiveTypeDTO copyDto(TReceiveType tReceiveType, TReceiveTypeDTO tReceiveTypeDTO) {
        tReceiveTypeDTO = new TReceiveTypeDTO();
        BeanUtils.copyProperties(tReceiveType, tReceiveTypeDTO);
        return tReceiveTypeDTO;
    }

    /**
     * 排序号自增
     * @param entity
     * @return
     */
    @Override
    public Boolean updateOrderBy (TReceiveTypeDTO entity){
        LambdaQueryWrapper<TReceiveType> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TReceiveType::getParentId,entity.getParentId());
        lambdaQueryWrapper.eq(TReceiveType::getOrderBy,entity.getOrderBy());
        int count = tReceiveTypeMapper.selectCount(lambdaQueryWrapper);
        if (count > 0){
            tReceiveTypeMapper.updataSort(
                    SortEnum.builder()
                            .sort(entity.getOrderBy())
                            .id(entity.getId())
                            .parentId(entity.getParentId())
                            .tableName("t_receive_type")
                            .sortName("order_by")
                            .additionOrsubtraction("+")
                            .build());
        }
        return true;
    }
}
