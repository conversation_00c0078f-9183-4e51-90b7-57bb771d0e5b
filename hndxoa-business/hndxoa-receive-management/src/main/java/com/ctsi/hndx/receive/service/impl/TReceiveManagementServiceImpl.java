package com.ctsi.hndx.receive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.receive.entity.TReceiveManagement;
import com.ctsi.hndx.receive.entity.dto.TReceiveManagementDTO;
import com.ctsi.hndx.receive.mapper.TReceiveManagementMapper;
import com.ctsi.hndx.receive.service.ITReceiveManagementService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.HaoQianUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */

@Slf4j
@Service
public class TReceiveManagementServiceImpl extends SysBaseServiceImpl<TReceiveManagementMapper, TReceiveManagement> implements ITReceiveManagementService {

    @Autowired
    private TReceiveManagementMapper tReceiveManagementMapper;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    @Autowired
    private ICscpHaoqianVersionService cscpHaoqianVersionService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TReceiveManagementDTO> queryListPage(TReceiveManagementDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TReceiveManagement> queryWrapper = new LambdaQueryWrapper();

        IPage<TReceiveManagement> pageData = tReceiveManagementMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TReceiveManagementDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TReceiveManagementDTO.class));

        return new PageResult<TReceiveManagementDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TReceiveManagementDTO> queryList(TReceiveManagementDTO entityDTO) {
        LambdaQueryWrapper<TReceiveManagement> queryWrapper = new LambdaQueryWrapper();
        List<TReceiveManagement> listData = tReceiveManagementMapper.selectList(queryWrapper);
        List<TReceiveManagementDTO> TReceiveManagementDTOList = ListCopyUtil.copy(listData, TReceiveManagementDTO.class);
        return TReceiveManagementDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TReceiveManagementDTO findOne(Long id) {
        TReceiveManagement tReceiveManagement = tReceiveManagementMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tReceiveManagement, TReceiveManagementDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TReceiveManagementDTO create(TReceiveManagementDTO entityDTO) {
        // 设置部门领导信息
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        List<CscpUserDTO> cscpUserDTOS = cscpUserService.selectDepartmentHead(currentCscpUserDetail.getDepartmentId());
        if (CollectionUtil.isEmpty(cscpUserDTOS)) {
            entityDTO.setDepartmentLeader(currentCscpUserDetail.getDepartmentName() + " " + currentCscpUserDetail.getRealName());
        } else {
            entityDTO.setDepartmentLeader(currentCscpUserDetail.getDepartmentName() + " " + cscpUserDTOS.get(0).getRealName());

        }

        TReceiveManagement tReceiveManagement = BeanConvertUtils.copyProperties(entityDTO, TReceiveManagement.class);
        try{
            save(tReceiveManagement);
        }catch (Exception e){
            throw new BusinessException(e.toString());
        }

        return BeanConvertUtils.copyProperties(tReceiveManagement, TReceiveManagementDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TReceiveManagementDTO entity) {
        TReceiveManagement tReceiveManagement = BeanConvertUtils.copyProperties(entity, TReceiveManagement.class);
        return tReceiveManagementMapper.updateById(tReceiveManagement);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tReceiveManagementMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TReceiveManagementId
     * @return
     */
    @Override
    public boolean existByTReceiveManagementId(Long TReceiveManagementId) {
        if (TReceiveManagementId != null) {
            LambdaQueryWrapper<TReceiveManagement> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TReceiveManagement::getId, TReceiveManagementId);
            List<TReceiveManagement> result = tReceiveManagementMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<TReceiveManagementDTO> dataList) {
        List<TReceiveManagement> result = ListCopyUtil.copy(dataList, TReceiveManagement.class);
        return saveBatch(result);
    }

    /**
     * 生成流水号(用于来文字号字段)，按部门隔离
     *
     * @param entity
     * @return
     */
    @Override
    public String generateCode(TReceiveManagementDTO entity) {
        Long departmentId = SecurityUtils.getCurrentCscpUserDetail().getDepartmentId();
        if (Objects.isNull(departmentId)) {
            throw new BusinessException("未找到该用户的部门ID");
        }

        // 获取该部门所有记录
        LambdaQueryWrapper<TReceiveManagement> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TReceiveManagement::getDepartmentId, departmentId);
        List<TReceiveManagement> receiveManagementList = tReceiveManagementMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(receiveManagementList)) {
            return "001";
        }

        // 获取该部门最大流水号
        List<Long> codeList = receiveManagementList.stream().map(receiveManagement -> {
            if (!StringUtils.hasText(receiveManagement.getCode())) {
                return 0L;
            }
            int len = receiveManagement.getCode().length();
            if (len >= 4) {
                return Long.parseLong(receiveManagement.getCode().substring(len - 4, len - 1));
            }
            return 0L;
        }).distinct().collect(Collectors.toList());
        Long maxCode = Collections.max(codeList);

        String code = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(maxCode + 1), 3);
        return code;
    }

    /**
     * 获取来文字号字段
     *
     * @param entity
     * @return
     */
    @Override
    public String getIncomingDocumentCode(TReceiveManagementDTO entity) {
        // 生成流水号
        String code = this.generateCode(entity);

        // 获取当前年份
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();

        // 拼接
        String DepartmentName = SecurityUtils.getCurrentCscpUserDetail().getDepartmentName();
        if (!StringUtils.hasText(DepartmentName)) {
            throw new BusinessException("未找到该用户的部门名称");
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【").append(currentYear).append("】")
                .append(DepartmentName).append(code).append("号");
        return stringBuilder.toString();
    }

    /**
     * 异步复制正文pdf到好签
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void copyPdfToHaoqian(CscpHaoqianVersionDTO entityDTO) {
        try {
            //获取pdf文件字节流
            MultipartFile file =cscpDocumentFileService.getDocumentMultipartFileSync(entityDTO.getFormDataId(),"2");
            //上传pdf文件到好签服务器
            String signid=haoQianUtils.uploadFileToHaoQian(file,null,null,null);
            entityDTO.setSignId(signid);
            cscpHaoqianVersionService.create(entityDTO);
        }catch (Exception e){
            log.info("发文拟稿复制好签pdf失败"+e.toString());
        }
    }

}
