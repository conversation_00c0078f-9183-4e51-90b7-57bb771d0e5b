package com.ctsi.hndx.receive.controller;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.tree.CascadeNode;
import com.ctsi.hndx.tree.Node;
import com.ctsi.ssdc.model.PageResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.ctsi.hndx.receive.entity.TReceiveType;
import com.ctsi.hndx.receive.entity.dto.TReceiveTypeDTO;
import com.ctsi.hndx.receive.service.ITReceiveTypeService;
import com.ctsi.ssdc.model.ResResult;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tReceiveType")
@Api(value = "收文类型", tags = "收文类型")
public class TReceiveTypeController extends BaseController {

    private static final String ENTITY_NAME = "tReceiveType";

    @Autowired
    private ITReceiveTypeService tReceiveTypeService;



    /**
     *  新增流程类型批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量,按钮编码为cscp.tReceiveType.addbatch", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增收文类型批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tReceiveType.addbatch')")
    public ResultVO createBatch(@RequestBody List<TReceiveTypeDTO> tReceiveTypeList) {
       Boolean  result = tReceiveTypeService.insertBatch(tReceiveTypeList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增按钮编码为cscp.tReceiveType.add", notes = "传入参数")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tReceiveType.add')")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增收文类型数据")
    public ResultVO<TReceiveTypeDTO> create(@RequestBody TReceiveTypeDTO tReceiveTypeDTO)  {
        TReceiveTypeDTO result = tReceiveTypeService.create(tReceiveTypeDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据，按钮编码cscp.tReceiveType.edit", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新收文类型数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tReceiveType.edit')")
    public ResultVO update(@RequestBody TReceiveTypeDTO tReceiveTypeDTO) {
	    Assert.notNull(tReceiveTypeDTO.getId(), "general.IdNotNull");
        int count = tReceiveTypeService.update(tReceiveTypeDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除收文类型数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.tReceiveType.del')")
    @ApiOperation(value = "删除存在数据按钮编码 cscp.tReceiveType.del", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tReceiveTypeService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TReceiveTypeDTO tReceiveTypeDTO = tReceiveTypeService.findOne(id);
        return ResultVO.success(tReceiveTypeDTO);
    }

   /* *//**
     *  分页查询多条数据.
     *//*
    @GetMapping("/queryTReceiveTypePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TReceiveTypeDTO>> queryTReceiveTypePage(TReceiveTypeDTO tReceiveTypeDTO, BasePageForm basePageForm) {
        return ResultVO.success(tReceiveTypeService.queryListPage(tReceiveTypeDTO, basePageForm));
    }

   *//**
     * 查询多条数据.不分页
     *//*
   @GetMapping("/queryTReceiveType")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<ResResult<TReceiveTypeDTO>> queryTReceiveType(TReceiveTypeDTO tReceiveTypeDTO) {
       List<TReceiveTypeDTO> list = tReceiveTypeService.queryList(tReceiveTypeDTO);
       return ResultVO.success(new ResResult<TReceiveTypeDTO>(list));
   }*/

    /**
     * 查询类型的树结构
     */
    @GetMapping("/queryTReceiveTypeTreeNode")
    @ApiOperation(value = "查询类型的树结构", notes = "查询类型的树结构")
    @ApiImplicitParams(
            { @ApiImplicitParam(value ="父节点id，默认为0",name = "parentId"),
                    @ApiImplicitParam(value ="类型表名",name = "tableName")}
    )
    public ResultVO<List<Node<TReceiveTypeDTO>>> queryTReceiveTypeTreeNode(
            @RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
            String tableName) {

        if (StringUtil.isNotEmpty(tableName)){
            LambdaQueryWrapper<TReceiveType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TReceiveType::getTableName,tableName).last(SysConstant.LIMIT_ONE);
            TReceiveType tReceiveType = tReceiveTypeService.getOne(lambdaQueryWrapper);
            if (tReceiveType != null){
                parentId = tReceiveType.getParentId();
            }
        }
        List<Node<TReceiveTypeDTO>> list = tReceiveTypeService.selectChildrenListNodeByParentId(parentId);
        list = list.stream().filter(q->q.getDetailsData().getTableName().equals(tableName)).collect(Collectors.toList());
        return ResultVO.success(list);
    }


    /**
     * 查询类型的树结构
     */
    @GetMapping("/queryTReceiveTypeCascadeNode")
    @ApiOperation(value = "查询类型的树结构", notes = "查询级联的的树结构")
    @ApiImplicitParams(
            { @ApiImplicitParam(value ="父节点id，默认为0",name = "parentId"),
                    @ApiImplicitParam(value ="类型表名",name = "tableName")}
    )
    public ResultVO<List<CascadeNode>> queryTReceiveTypeCascadeNode(
            @RequestParam(value = "parentId", required = true, defaultValue = "0") Long parentId,
            String tableName) {

        if (StringUtil.isNotEmpty(tableName)){
            LambdaQueryWrapper<TReceiveType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TReceiveType::getTableName,tableName).last(SysConstant.LIMIT_ONE);
            TReceiveType tReceiveType = tReceiveTypeService.getOne(lambdaQueryWrapper);
            if (tReceiveType != null){
                parentId = tReceiveType.getId();
            }
        }
        List<Node<TReceiveTypeDTO>> list = tReceiveTypeService.selectChildrenListNodeByParentId(parentId);
        list = list.stream().filter(q->q.getDetailsData().getTableName().equals(tableName)).collect(Collectors.toList());
        List<CascadeNode> cascadeNodeList = new ArrayList<>();
        list.forEach(tReceiveTypeDTONode -> {
            CascadeNode cascadeNode  = buildCascadeNode(tReceiveTypeDTONode);
            cascadeNodeList.add(cascadeNode);
        });

        return ResultVO.success(cascadeNodeList);
    }



    private CascadeNode buildCascadeNode(Node node){
        CascadeNode cascadeNode = new CascadeNode();
        Long id = node.getId();
        String title = node.getTitle();
        cascadeNode.setValue(String.valueOf(id));
        cascadeNode.setLabel(title);
        List<Node> children = node.getChildren();
        List<CascadeNode> cascadeNodeList = new ArrayList<>();
        if (CollectionUtil.isEmpty(children)){
            cascadeNode.setChildren(new ArrayList<>());
            return cascadeNode;
        }
        children.forEach(node1 -> {
            cascadeNodeList.add(buildCascadeNode(node1));
        });
        cascadeNode.setChildren(cascadeNodeList);
        return cascadeNode;
    }
}
