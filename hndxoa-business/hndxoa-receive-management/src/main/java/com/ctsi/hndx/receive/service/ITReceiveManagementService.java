package com.ctsi.hndx.receive.service;

import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.receive.entity.dto.TReceiveManagementDTO;
import com.ctsi.hndx.receive.entity.TReceiveManagement;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
public interface ITReceiveManagementService extends SysBaseServiceI<TReceiveManagement> {


    /**
     * 分页查询
     */
    PageResult<TReceiveManagementDTO> queryListPage(TReceiveManagementDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TReceiveManagementDTO> queryList(TReceiveManagementDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TReceiveManagementDTO findOne(Long id);

    /**
     * 新增
     */
    TReceiveManagementDTO create(TReceiveManagementDTO entity);


    /**
     * 更新
     */
    int update(TReceiveManagementDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTReceiveManagementId
     */
    boolean existByTReceiveManagementId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TReceiveManagementDTO> dataList);

    /**
     * 生成流水号(用于来文字号字段)，按部门隔离
     * @param entity
     * @return
     */
    String generateCode(TReceiveManagementDTO entity);

    /**
     * 获取来文字号字段
     * @param entity
     * @return
     */
    String getIncomingDocumentCode(TReceiveManagementDTO entity);

    /**
     * 异步复制正文pdf到好签
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void copyPdfToHaoqian(CscpHaoqianVersionDTO entityDTO);

}
