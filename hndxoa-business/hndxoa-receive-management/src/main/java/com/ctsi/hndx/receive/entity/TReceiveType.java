package com.ctsi.hndx.receive.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 流程类型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TReceiveType对象", description="流程类型")
public class TReceiveType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 汇报部门名称
     */
    @ApiModelProperty(value = "汇报部门名称")
    private String departmentName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String conpanyName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderBy;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String mark;

    /**
     * 上级类型id，顶级是0
     */
    @ApiModelProperty(value = "上级类型id，顶级是0")
    private Long parentId;

    /**
     * 模型表名，只在最顶级有用，用来区分不同类型
     */
    @ApiModelProperty(value = "模型表名，只在最顶级有用，用来区分不同类型")
    private String tableName;


}
