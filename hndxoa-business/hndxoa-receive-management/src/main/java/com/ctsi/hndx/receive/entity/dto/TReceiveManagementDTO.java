package com.ctsi.hndx.receive.entity.dto;

import java.time.LocalDateTime;

import com.ctsi.hndx.common.ProcessBusinessBaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TReceiveManagementDTO对象", description="")
public class TReceiveManagementDTO extends ProcessBusinessBaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主送单位
     */
    @ApiModelProperty(value = "主送单位")
    private String submitTo;

    /**
     * 主送单位名称
     */
    @ApiModelProperty(value = "主送单位名称")
    private String submitToName;

    /**
     * 行文方向
     */
    @ApiModelProperty(value = "行文方向")
    private String writingDirection;

    /**
     * 行文方向名称
     */
    @ApiModelProperty(value = "行文方向名称")
    private String writingDirectionName;

    /**
     * 收文类型
     */
    @ApiModelProperty(value = "收文类型")
    private String receiveType;

    /**
     * 收文类型名称
     */
    @ApiModelProperty(value = "收文类型名称")
    private String receiveTypeName;

    /**
     * 办理类型
     */
    @ApiModelProperty(value = "办理类型")
    private String handleType;

    /**
     * 办理类型名称
     */
    @ApiModelProperty(value = "办理类型名称")
    private String handleTypeName;

    /**
     * 来文种类
     */
    @ApiModelProperty(value = "来文种类")
    private String incomingType;

    /**
     * 来文种类名称
     */
    @ApiModelProperty(value = "来文种类名称")
    private String incomingTypeName;

    /**
     * 来文单位
     */
    @ApiModelProperty(value = "来文单位")
    private String incomingCompany;

    /**
     * 来文日期
     */
    @ApiModelProperty(value = "来文日期")
    private String incomingTime;

    /**
     * 收文日期
     */
    @ApiModelProperty(value = "收文日期")
    private String receiveTime;

    /**
     * 来文字号
     */
    @ApiModelProperty(value = "来文字号")
    private String incomingNo;

    /**
     * 文件概要
     */
    @ApiModelProperty(value = "文件概要")
    private String documentSummary;

    /**
     * 拟办意见
     */
    @ApiModelProperty(value = "拟办意见")
    private String proposedOpinion;

    /**
     * 收文编号
     */
    @ApiModelProperty(value = "收文编号")
    private String receiveCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 拟稿人手机号码
     */
    @ApiModelProperty(value = "拟稿人手机号码")
    private String mobile;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    @ApiModelProperty(value = "来文范围/收文范围，与收文类型关联到一起")
    private String receiveScope;

    @ApiModelProperty(value = "收文范围名称")
    private String receiveScopeName;

    @ApiModelProperty(value = "来文字号字段")
    private String code;


    @ApiModelProperty(value = "经办人")
    private String agentPeople;


    @ApiModelProperty(value = "经办人及其手机号码")
    private String agentPeopleMobile;


    @ApiModelProperty(value = "科室部门处室负责人")
    private String departmentLeader;

    @ApiModelProperty(value = "收文紧急程度")
    private String urgency;

    @ApiModelProperty(value = "收文紧急程度名称")
    private String urgencyName;

    @ApiModelProperty(value = "处理截止时间")
    private String handDeadline;

    @ApiModelProperty(value = "序号")
    private String number;

    @ApiModelProperty(value = "表单json")
    private String formJson;

    @ApiModelProperty(value = "文件口径")
    private String documentCaliber;

    @ApiModelProperty(value = "文件口径名称")
    private String documentCaliberName;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件类型名称")
    private String fileTypeName;
}
