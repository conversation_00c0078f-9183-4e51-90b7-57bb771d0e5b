package com.ctsi.hndx.receive.service;

import com.ctsi.hndx.receive.entity.dto.TReceiveTypeDTO;
import com.ctsi.hndx.receive.entity.TReceiveType;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.tree.TreeSelectService;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 流程类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface ITReceiveTypeService extends TreeSelectService<TReceiveTypeDTO,TReceiveType> {


    /**
     * 分页查询
     */
    PageResult<TReceiveTypeDTO> queryListPage(TReceiveTypeDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TReceiveTypeDTO> queryList(TReceiveTypeDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TReceiveTypeDTO findOne(Long id);

    /**
     * 新增
     */
    TReceiveTypeDTO create(TReceiveTypeDTO entity);


    /**
     * 更新
     */
    int update(TReceiveTypeDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTReceiveTypeId
     */
    boolean existByTReceiveTypeId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TReceiveTypeDTO> dataList);

    /**
     * 排序号自增
     * @param entity
     * @return
     */
    Boolean updateOrderBy (TReceiveTypeDTO entity);

}
