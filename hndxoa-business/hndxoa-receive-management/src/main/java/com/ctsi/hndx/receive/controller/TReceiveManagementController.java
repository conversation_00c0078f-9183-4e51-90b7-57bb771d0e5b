package com.ctsi.hndx.receive.controller;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.utils.HaoQianUtils;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Optional;

import com.ctsi.hndx.receive.entity.TReceiveManagement;
import com.ctsi.hndx.receive.entity.dto.TReceiveManagementDTO;
import com.ctsi.hndx.receive.service.ITReceiveManagementService;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tReceiveManagement")
@Api(value = "", tags = "收文管理")
public class TReceiveManagementController extends BaseController {

    private static final String ENTITY_NAME = "tReceiveManagement";

    @Autowired
    private ITReceiveManagementService tReceiveManagementService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    @Autowired
    private ICscpHaoqianVersionService cscpHaoqianVersionService;

    /**
     * 新增批量数据.
     */
    @PostMapping("/createBatch")
    @OperationLog(dBOperation = DBOperation.ADD, message = "收文管理新增批量数据")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    public ResultVO createBatch(@RequestBody List<TReceiveManagementDTO> tReceiveManagementList) {
        Boolean result = tReceiveManagementService.insertBatch(tReceiveManagementList);
        if (result) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "收文管理新增数据")
    @ApiOperation(value = "新增", notes = "传入参数")
    public ResultVO<TReceiveManagementDTO> create(@RequestBody TReceiveManagementDTO tReceiveManagementDTO) throws URISyntaxException {

        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        tReceiveManagementDTO.setMobile(currentCscpUserDetail.getMobile());

        if (StringUtil.isEmpty(tReceiveManagementDTO.getDepartmentName())){
            tReceiveManagementDTO.setDepartmentName(currentCscpUserDetail.getDepartmentName());
        }
        if (StringUtil.isEmpty(tReceiveManagementDTO.getCompanyName())){
            tReceiveManagementDTO.setCompanyName(currentCscpUserDetail.getCompanyName());
        }

        tReceiveManagementDTO.setAgentPeople(currentCscpUserDetail.getRealName());
        tReceiveManagementDTO.setAgentPeopleMobile(currentCscpUserDetail.getRealName()+" "+ currentCscpUserDetail.getMobile());

//        String archivesSign =tSenddocMasterService.getElectronArchivesNorm();//获取档案标识
//        if(!archivesSign.isEmpty())
//        {
//            String[] srts = archivesSign.split("-");
//            tReceiveManagementDTO.setSerialNumber(srts[4]);
//            tReceiveManagementDTO.setArchivesSign(archivesSign);
//        }

        TReceiveManagementDTO result = tReceiveManagementService.create(tReceiveManagementDTO);

        //档案流水号
//        bizSerialNumberService.saverUpdateSerialNumbe();

        // TODO: 2023/4/10 wubin 将正文pdf上传到好签服务器，并且保存好签签批版本管理
//        CscpHaoqianVersionDTO cscpHaoqianVersionDTO=new CscpHaoqianVersionDTO();
//        cscpHaoqianVersionDTO.setFormDataId(tReceiveManagementDTO.getId());
//        cscpHaoqianVersionDTO.setProcTypeName("收文");
//        cscpHaoqianVersionDTO.setTaskLink("版本1");
//        cscpHaoqianVersionDTO.setVersionNumber(1);
//        tReceiveManagementService.copyPdfToHaoqian(cscpHaoqianVersionDTO);
//        try {
//            //获取pdf文件字节流
//            MultipartFile file =cscpDocumentFileService.getDocumentMultipartFile(tReceiveManagementDTO.getId(),"2");
//            //上传pdf文件到好签服务器
//            String signid=haoQianUtils.uploadFileToHaoQian(file,null,null,null);
//            CscpHaoqianVersionDTO cscpHaoqianVersionDTO=new CscpHaoqianVersionDTO();
//            cscpHaoqianVersionDTO.setFormDataId(tReceiveManagementDTO.getId());
//            cscpHaoqianVersionDTO.setSignId(signid);
//            cscpHaoqianVersionDTO.setProcTypeName("收文");
//            cscpHaoqianVersionDTO.setTaskLink("拟稿");
//            cscpHaoqianVersionDTO.setVersionNumber(1);
//            cscpHaoqianVersionService.create(cscpHaoqianVersionDTO);
//        }catch (Exception e){
//            log.info("收文拟稿复制好签pdf失败"+e.toString());
//        }
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新收文管理")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO update(@RequestBody TReceiveManagementDTO tReceiveManagementDTO) {
        Assert.notNull(tReceiveManagementDTO.getId(), "general.IdNotNull");
        int count = tReceiveManagementService.update(tReceiveManagementDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除存在的收文管理")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tReceiveManagementService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TReceiveManagementDTO tReceiveManagementDTO = tReceiveManagementService.findOne(id);
        return ResultVO.success(tReceiveManagementDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryTReceiveManagementPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TReceiveManagementDTO>> queryTReceiveManagementPage(TReceiveManagementDTO tReceiveManagementDTO, BasePageForm basePageForm) {
        return ResultVO.success(tReceiveManagementService.queryListPage(tReceiveManagementDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryTReceiveManagement")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    public ResultVO<ResResult<TReceiveManagementDTO>> queryTReceiveManagement(TReceiveManagementDTO tReceiveManagementDTO) {
        List<TReceiveManagementDTO> list = tReceiveManagementService.queryList(tReceiveManagementDTO);
        return ResultVO.success(new ResResult<TReceiveManagementDTO>(list));
    }

    @GetMapping("/getIncomingDocumentCode")
    @ApiOperation(value = "获取来文字号字段", notes = "传入参数")
    public ResultVO<String> getIncomingDocumentCode(TReceiveManagementDTO tReceiveManagementDTO) {
        String code = tReceiveManagementService.getIncomingDocumentCode(tReceiveManagementDTO);
        return ResultVO.success(code);
    }


}
