CREATE TABLE `t_receive_management` (
                                        `id` bigint NOT NULL COMMENT '主键id',
                                        `submit_to` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主送单位',
                                        `writing_direction` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行文方向',
                                        `receive_type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收文类型',
                                        `handle_type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '办理类型',
                                        `incoming_type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来文种类',
                                        `title` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                        `incoming_company` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来文单位',
                                        `incoming_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来文日期',
                                        `receive_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收文日期',
                                        `incoming_no` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来文字号',
                                        `document_summary` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件概要',
                                        `proposed_opinion` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟办意见',
                                        `document` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '正文',
                                        `receive_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收文编号',
                                        `remark` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                        `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                        `create_by` bigint DEFAULT NULL COMMENT '创建人id',
                                        `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` bigint DEFAULT NULL COMMENT '更新人id',
                                        `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新用户名',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `company_id` bigint DEFAULT NULL COMMENT '单位id',
                                        `department_id` bigint DEFAULT NULL COMMENT '部门id',
                                        `tenant_id` bigint DEFAULT NULL COMMENT '租户id',
                                        `bpm_status` int DEFAULT NULL COMMENT '流程状态',
                                        `annex` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                        `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                        `mobile` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人手机号码',
                                        `department_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿部门名称',
                                        `company_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                        `receive_scope` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收文范围(与收文类型关联)',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收文表';

CREATE TABLE `t_receive_type` (
                                  `id` bigint NOT NULL COMMENT '主键ID',
                                  `create_by` bigint DEFAULT NULL COMMENT '拟稿人ID',
                                  `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人汇报人名称',
                                  `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                  `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '汇报部门名称',
                                  `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                  `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                  `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                  `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                  `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                  `conpany_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿单位名称',
                                  `type_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类型名称',
                                  `order_by` int DEFAULT NULL COMMENT '排序号',
                                  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
                                  `parent_id` bigint DEFAULT '0' COMMENT '上级类型id，顶级是0',
                                  `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模型表名，只在最顶级有用，用来区分不同类型',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公文收文类型';

