CREATE TABLE "public"."t_receive_management" (
                                                 "id" int8 NOT NULL,
                                                 "submit_to" varchar(200 char) NULL,
	"writing_direction" varchar(200 char) NULL,
	"receive_type" varchar(200 char) NULL,
	"handle_type" varchar(200 char) NULL,
	"incoming_type" varchar(200 char) NULL,
	"title" varchar(500 char) NULL,
	"incoming_company" varchar(64 char) NULL,
	"incoming_time" varchar(30 char) NULL,
	"receive_time" varchar(30 char) NULL,
	"incoming_no" varchar(128 char) NULL,
	"document_summary" varchar(512 char) NULL,
	"proposed_opinion" varchar(512 char) NULL,
	"document" varchar(1 char) NULL,
	"receive_code" varchar(32 char) NULL,
	"remark" varchar(64 char) NULL,
	"deleted" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"company_id" int8 NULL,
	"department_id" int8 NULL,
	"tenant_id" int8 NULL,
	"bpm_status" int4 NULL,
	"annex" varchar(1 char) NULL,
	"process_instance_id" int8 NULL,
	"mobile" varchar(11 char) NULL,
	"department_name" varchar(255 char) NULL,
	"company_name" varchar(255 char) NULL,
	"receive_scope" varchar(255 char) NULL,
	CONSTRAINT "t_receive_management_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_receive_management"."receive_scope" IS '收文范围(与收文类型关联)';
COMMENT ON COLUMN "public"."t_receive_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."t_receive_management"."department_name" IS '拟稿部门名称';
COMMENT ON COLUMN "public"."t_receive_management"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "public"."t_receive_management"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."t_receive_management"."annex" IS '附件';
COMMENT ON COLUMN "public"."t_receive_management"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."t_receive_management"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."t_receive_management"."department_id" IS '部门id';
COMMENT ON COLUMN "public"."t_receive_management"."company_id" IS '单位id';
COMMENT ON COLUMN "public"."t_receive_management"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."t_receive_management"."update_name" IS '更新用户名';
COMMENT ON COLUMN "public"."t_receive_management"."update_by" IS '更新人id';
COMMENT ON COLUMN "public"."t_receive_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_receive_management"."create_name" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_receive_management"."create_by" IS '创建人id';
COMMENT ON COLUMN "public"."t_receive_management"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_receive_management"."remark" IS '备注';
COMMENT ON COLUMN "public"."t_receive_management"."receive_code" IS '收文编号';
COMMENT ON COLUMN "public"."t_receive_management"."document" IS '正文';
COMMENT ON COLUMN "public"."t_receive_management"."proposed_opinion" IS '拟办意见';
COMMENT ON COLUMN "public"."t_receive_management"."document_summary" IS '文件概要';
COMMENT ON COLUMN "public"."t_receive_management"."incoming_no" IS '来文字号';
COMMENT ON COLUMN "public"."t_receive_management"."receive_time" IS '收文日期';
COMMENT ON COLUMN "public"."t_receive_management"."incoming_time" IS '来文日期';
COMMENT ON COLUMN "public"."t_receive_management"."incoming_company" IS '来文单位';
COMMENT ON COLUMN "public"."t_receive_management"."title" IS '标题';
COMMENT ON COLUMN "public"."t_receive_management"."incoming_type" IS '来文种类';
COMMENT ON COLUMN "public"."t_receive_management"."handle_type" IS '办理类型';
COMMENT ON COLUMN "public"."t_receive_management"."receive_type" IS '收文类型';
COMMENT ON COLUMN "public"."t_receive_management"."writing_direction" IS '行文方向';
COMMENT ON COLUMN "public"."t_receive_management"."submit_to" IS '主送单位';
COMMENT ON COLUMN "public"."t_receive_management"."id" IS '主键id';
COMMENT ON TABLE "public"."t_receive_management" IS '收文表';


CREATE TABLE "public"."t_receive_type" (
                                           "id" int8 NOT NULL,
                                           "create_by" int8 NULL,
                                           "create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	"company_id" int8 NULL,
	"conpany_name" varchar(32 char) NULL,
	"type_name" varchar(255 char) NULL,
	"order_by" int4 NULL,
	"mark" varchar(255 char) NULL,
	"parent_id" int8 NULL DEFAULT 0,
	"table_name" varchar(255 char) NULL,
	CONSTRAINT "t_receive_type_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_receive_type"."table_name" IS '模型表名，只在最顶级有用，用来区分不同类型';
COMMENT ON COLUMN "public"."t_receive_type"."parent_id" IS '上级类型id，顶级是0';
COMMENT ON COLUMN "public"."t_receive_type"."mark" IS '备注';
COMMENT ON COLUMN "public"."t_receive_type"."order_by" IS '排序号';
COMMENT ON COLUMN "public"."t_receive_type"."type_name" IS '类型名称';
COMMENT ON COLUMN "public"."t_receive_type"."conpany_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."t_receive_type"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_receive_type"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_receive_type"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_receive_type"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."t_receive_type"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."t_receive_type"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_receive_type"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."t_receive_type"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."t_receive_type"."department_name" IS '汇报部门名称';
COMMENT ON COLUMN "public"."t_receive_type"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_receive_type"."create_name" IS '拟稿人汇报人名称';
COMMENT ON COLUMN "public"."t_receive_type"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."t_receive_type"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_receive_type" IS '公文收文类型';
