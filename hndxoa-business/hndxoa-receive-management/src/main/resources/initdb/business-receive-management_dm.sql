CREATE TABLE "myapp"."t_receive_management"
(
"id" BIGINT NOT NULL,
"submit_to" VARCHAR(200),
"writing_direction" VARCHAR(200),
"receive_type" VARCHAR(200),
"handle_type" VARCHAR(200),
"incoming_type" VARCHAR(200),
"title" VARCHAR(500),
"incoming_company" VARCHAR(64),
"incoming_time" VARCHAR(30),
"receive_time" VARCHAR(30),
"incoming_no" VARCHAR(128),
"document_summary" VARCHAR(512),
"proposed_opinion" VARCHAR(512),
"document" VARCHAR(1),
"receive_code" VARCHAR(32),
"remark" VAR, CHAR(64),
"deleted" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"company_id" BIGINT,
"department_id" BIGINT,
"tenant_id" BIGINT,
"bpm_status" INT,
"annex" VARCHAR(1),
"process_instance_id" BIGINT,
"mobile" VARCHAR(11),
"department_name" VARCHAR(255),
"company_name" VARCHAR(255),
"receive_scope" VARCHAR(255),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR)   ;

COMMENT ON TABLE "myapp"."t_receive_management" IS '收文表';COMMENT ON COLUMN "myapp"."t_receive_management"."id" IS '主键id';
COMMENT ON COLUMN "myapp"."t_receive_management"."submit_to" IS '主送单位';
COMMENT ON COLUMN "myapp"."t_receive_management"."writing_direction" IS '行文方向';
COMMENT ON COLUMN "myapp"."t_receive_management"."receive_type" IS '收文类型';
COMMENT ON COLUMN "myapp"."t_receive_management"."handle_type" IS '办理类型';
COMMENT ON COLUMN "myapp"."t_receive_management"."incoming_type" IS '来文种类';
COMMENT ON COLUMN "myapp"."t_receive_management"."title" IS '标题';
COMMENT ON COLUMN "myapp"."t_receive_management"."incoming_company" IS '来文单位';
COMMENT ON COLUMN "myapp"."t_receive_management"."incoming_time" IS '来文日期';
COMMENT ON COLUMN "myapp"."t_receive_management"."receive_time" IS '收文日期';
COMMENT ON COLUMN "myapp"."t_receive_management"."incoming_no" IS '来文字号';
COMMENT ON COLUMN "myapp"."t_receive_management"."document_summary" IS '文件概要';
COMMENT ON COLUMN "myapp"."t_receive_management"."proposed_opinion" IS '拟办意见';
COMMENT ON COLUMN "myapp"."t_receive_management"."document" IS '正文';
COMMENT ON COLUMN "myapp"."t_receive_management"."receive_code" IS '收文编号';
COMMENT ON COLUMN "myapp"."t_receive_management"."remark" IS '备注';
COMMENT ON COLUMN "myapp"."t_receive_management"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_receive_management"."create_by" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_receive_management"."create_name" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_receive_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_receive_management"."update_by" IS '更新人id';
COMMENT ON COLUMN "myapp"."t_receive_management"."update_name" IS '更新用户名';
COMMENT ON COLUMN "myapp"."t_receive_management"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."t_receive_management"."company_id" IS '单位id';
COMMENT ON COLUMN "myapp"."t_receive_management"."department_id" IS '部门id';
COMMENT ON COLUMN "myapp"."t_receive_management"."tenant_id" IS '租户id';
COMMENT ON COLUMN "myapp"."t_receive_management"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "myapp"."t_receive_management"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."t_receive_management"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."t_receive_management"."mobile" IS '拟稿人手机号码';
COMMENT ON COLUMN "myapp"."t_receive_management"."department_name" IS '拟稿部门名称';
COMMENT ON COLUMN "myapp"."t_receive_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."t_receive_management"."receive_scope" IS '收文范围(与收文类型关联)';




CREATE TABLE "myapp"."t_receive_type"
(
"id" BIGINT NOT NULL,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"department_id" BIGINT,
"department_name" VARCHAR(32),
"mobile" VARCHAR(32),
"create_time" TIMESTAMP(0),
"deleted" INT,
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"tenant_id" BIGINT,
"company_id" BIGINT,
"conpany_name" VARCHAR(32),
"type_name" VARCHAR(255),
"order_by" INT,
"mark" VARCHAR(255),
"parent_id" BIGINT DEFAULT 0,
"table_name" V, ARCHAR(255),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_receive_type" IS '公文收文类型';COMMENT ON COLUMN "myapp"."t_receive_type"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."create_name" IS '拟稿人汇报人名称';
COMMENT ON COLUMN "myapp"."t_receive_type"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."department_name" IS '汇报部门名称';
COMMENT ON COLUMN "myapp"."t_receive_type"."mobile" IS '联系电话';
COMMENT ON COLUMN "myapp"."t_receive_type"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."t_receive_type"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_receive_type"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."t_receive_type"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_receive_type"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_receive_type"."conpany_name" IS '拟稿单位名称';
COMMENT ON COLUMN "myapp"."t_receive_type"."type_name" IS '类型名称';
COMMENT ON COLUMN "myapp"."t_receive_type"."order_by" IS '排序号';
COMMENT ON COLUMN "myapp"."t_receive_type"."mark" IS '备注';
COMMENT ON COLUMN "myapp"."t_receive_type"."parent_id" IS '上级类型id，顶级是0';
COMMENT ON COLUMN "myapp"."t_receive_type"."table_name" IS '模型表名，只在最顶级有用，用来区分不同类型';




