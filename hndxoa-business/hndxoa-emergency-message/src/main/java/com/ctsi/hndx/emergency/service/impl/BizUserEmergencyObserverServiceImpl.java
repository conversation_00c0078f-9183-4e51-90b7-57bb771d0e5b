package com.ctsi.hndx.emergency.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.service.UserInfoChangeObserverService;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.leadershipEntrustment.mapper.BizLeadershipEntrustmentMapper;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Classname BizUserEmergencyObserverServiceImpl
 * @Description
 * @Date 2022/3/4/0004 17:13
 */
@Service
public class BizUserEmergencyObserverServiceImpl implements UserInfoChangeObserverService {

    @Autowired
    private BizLeadershipEntrustmentMapper bizLeadershipEntrustmentMapper;

    /**
     * 观察者修改领导委托中的用户信息
     *
     * @param userDTO
     * @return
     */
    @Override
    public Boolean update(CscpUserDTO userDTO) {
        //修改联络员
        BizLeadershipEntrustment liaisonMan = new BizLeadershipEntrustment();
        liaisonMan.setLiaisonManName(userDTO.getRealName());
        bizLeadershipEntrustmentMapper.updateTenantId(liaisonMan,
                new LambdaQueryWrapper<BizLeadershipEntrustment>().eq(BizLeadershipEntrustment::getLiaisonManId, userDTO.getId()));

        //修改领导
        BizLeadershipEntrustment leader = new BizLeadershipEntrustment();
        leader.setLiaisonManName(userDTO.getRealName());
        bizLeadershipEntrustmentMapper.updateTenantId(leader,
                new LambdaQueryWrapper<BizLeadershipEntrustment>().eq(BizLeadershipEntrustment::getLeaderId, userDTO.getId()));

        return true;
    }

}
