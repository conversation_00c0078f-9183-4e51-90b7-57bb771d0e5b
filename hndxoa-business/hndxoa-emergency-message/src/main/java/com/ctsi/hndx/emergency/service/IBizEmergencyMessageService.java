package com.ctsi.hndx.emergency.service;

import com.ctsi.hndx.emergency.entity.dto.*;
import com.ctsi.hndx.emergency.entity.BizEmergencyMessage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 紧急信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface IBizEmergencyMessageService extends SysBaseServiceI<BizEmergencyMessage> {

    /**
     * 分页查询
     *
     * @param bizSearchEmergencyMessage
     * @param page
     * @return
     */
    PageResult<BizEmergencyMessageDTO> queryListPage(BizSearchEmergencyMessage bizSearchEmergencyMessage, BasePageForm page);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizEmergencyMessageDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizEmergencyMessageDTO create(BizEmergencyMessageDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 回执用户情况
     *
     * @param id
     * @param basePageForm
     * @return
     */
    BizReceiptDetailsDTO queryReceiptInformationPage(Long id, BasePageForm basePageForm);

    /**
     * 补发
     *
     * @param userId
     * @param id
     */
    Integer reissue(List<BizUserEmergencyDTO> userId, Long id);

    /**
     * 分页查询未阅已阅
     *
     * @param bizQueryUnreadReadPageDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizEmergencyMessageDTO> queryUnreadReadPage(BizQueryUnreadReadPageDTO bizQueryUnreadReadPageDTO, BasePageForm basePageForm);

    /**
     * 回复
     *
     * @param replyContent
     * @return
     */
    Integer reply(ReplyDTO replyContent);

    /**
     * 紧急信息查阅查询单条数据
     *
     * @param id
     * @return
     */
    BizEmergencyMessageDTO QuerInformationAccess(Long id);

    /**
     * 催办
     *
     * @param urgeUserPhone
     * @return
     */
    void urge(List<String> urgeUserPhone);

    /**
     * 查询紧急信息未阅角标
     *
     * @return
     */
    Integer getAngleMark();
}
