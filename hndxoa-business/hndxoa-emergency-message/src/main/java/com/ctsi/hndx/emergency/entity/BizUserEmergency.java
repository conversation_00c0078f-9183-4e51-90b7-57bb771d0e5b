package com.ctsi.hndx.emergency.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 紧急通知和用户中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_user_emergency")
@ApiModel(value = "BizUserEmergency对象", description = "紧急通知和用户中间表")
public class BizUserEmergency extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 紧急信息id
     */
    @ApiModelProperty(value = "紧急信息id")
    private Long emergencyMessageId;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private LocalDateTime replyTime;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 用户所在单位id
     */
    @ApiModelProperty(value = "用户所在单位id")
    private Long unitId;

    /**
     * 用户所在单位名称
     */
    @ApiModelProperty(value = "用户所在单位名称")
    private String unitName;

    /**
     * 用户所在部门id
     */
    @ApiModelProperty(value = "用户所在部门id")
    private Long branchId;

    /**
     * 用户所在部门名称
     */
    @ApiModelProperty(value = "用户所在部门名称")
    private String branchName;

    /**
     * 用户电话号码
     */
    @ApiModelProperty(value = "用户电话号码")
    private String telephone;

    /**
     * 是否预览（0：未阅 1：已阅）
     */
    @ApiModelProperty(value = "是否预览（0：未阅 1：已阅）")
    private Integer previewType;

}
