package com.ctsi.hndx.emergency.service.impl;

import com.ctsi.hndx.service.impl.BaseUserInfoChangeSubjectServiceImpl;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Classname BizUserEmergencySubjectServiceImpl
 * @Description
 * @Date 2022/3/4/0004 16:52
 */
@Service
public class BizUserEmergencySubjectServiceImpl extends BaseUserInfoChangeSubjectServiceImpl {

    @Autowired
    private CscpUserService cscpUserService;

    @Override
    public void update(CscpUserDTO userDTO) {
        // 更新用户信息
        cscpUserService.update(userDTO);

        // 触发观察者信息更新
        this.notifyObservers(userDTO);
    }
}
