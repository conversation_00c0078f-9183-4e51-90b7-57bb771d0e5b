package com.ctsi.hndx.emergency.entity.dto;

import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.hndx.emergency.entity.dto
 * @ClassName: BizReceiptDetailsDTO
 * @Author: json
 * @Description:
 * @Date: 2022/2/9 15:46
 * @Version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "回执情况", description = "回执情况")
public class BizReceiptDetailsDTO {

    /**
     * 发送人数
     */
    @ApiModelProperty(value = "发送人数")
    private Integer sendOutCount;


    /**
     * 回复数量
     */
    @ApiModelProperty(value = "回复数量")
    private Integer replyCount;


    /**
     * 发送的用户信息
     */
    @ApiModelProperty(value = "发送的用户信息")
    private PageResult<BizUserEmergencyDTO> bizUserEmergencyList;
}
