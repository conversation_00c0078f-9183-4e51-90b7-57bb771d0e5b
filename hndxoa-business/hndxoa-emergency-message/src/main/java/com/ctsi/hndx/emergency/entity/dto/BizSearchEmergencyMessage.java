package com.ctsi.hndx.emergency.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.hndx.emergency.entity.dto
 * @ClassName: BizSearchEmergencyMessage
 * @Author: json
 * @Description:
 * @Date: 2022/2/9 15:14
 * @Version: 1.0
 */
@Data
@ApiModel(value = "紧急信息查询条件", description = "紧急信息查询条件")
public class BizSearchEmergencyMessage {

    /**
     * 信息内容
     */
    @ApiModelProperty(value = "信息内容", required = false)
    private String informationContent;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
}
