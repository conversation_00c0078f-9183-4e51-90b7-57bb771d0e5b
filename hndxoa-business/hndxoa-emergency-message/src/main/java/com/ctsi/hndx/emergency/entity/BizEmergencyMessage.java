package com.ctsi.hndx.emergency.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 紧急信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_emergency_message")
@ApiModel(value="BizEmergencyMessage对象", description="紧急信息")
public class BizEmergencyMessage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    /**
     * 信息类型（调用数据字典informationType 紧急会议、紧急通知）
     */
    @ApiModelProperty(value = "信息类型（调用数据字典informationType 紧急会议、紧急通知）")
    private Integer informationType;

    /**
     * 信息内容
     */
    @ApiModelProperty(value = "信息内容")
    private String informationContent;

    /**
     * 回复截止时间
     */
    @ApiModelProperty(value = "回复截止时间")
    private LocalDateTime replyDeadline;


}
