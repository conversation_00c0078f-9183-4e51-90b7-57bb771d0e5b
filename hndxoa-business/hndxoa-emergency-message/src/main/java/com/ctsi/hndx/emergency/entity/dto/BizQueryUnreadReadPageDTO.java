package com.ctsi.hndx.emergency.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.hndx.emergency.entity.dto
 * @ClassName: BizQueryUnreadReadPageDTO
 * @Author: json
 * @Description:
 * @Date: 2022/2/10 10:08
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "BizQueryUnreadReadPageDTO对象", description = "查询已阅未阅")
public class BizQueryUnreadReadPageDTO {

    /**
     * 查询条件
     */
    @ApiModelProperty(value = "查询条件（0：未阅 1：已阅）")
    private Integer queryType;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String informationContent;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题", required = true)
    private String title;

}
