package com.ctsi.hndx.emergency.controller;

import com.ctsi.hndx.emergency.entity.dto.*;
import com.ctsi.hndx.receive.entity.dto.InboxDTO;
import com.ctsi.hndx.utils.AESUtil;
import com.ctsi.hndx.utils.BaseUtils;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.ctsi.hndx.emergency.entity.BizEmergencyMessage;
import com.ctsi.hndx.emergency.service.IBizEmergencyMessageService;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizEmergencyMessage")
@Api(value = "紧急信息", tags = "紧急信息接口")
public class BizEmergencyMessageController extends BaseController {

    private static final String ENTITY_NAME = "bizEmergencyMessage";

    @Autowired
    private IBizEmergencyMessageService bizEmergencyMessageService;


    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizEmergencyMessage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增紧急信息数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizEmergencyMessage.add')")
    public ResultVO<BizEmergencyMessageDTO> create(@RequestBody BizEmergencyMessageDTO bizEmergencyMessageDTO) {
        BizEmergencyMessageDTO result = bizEmergencyMessageService.create(bizEmergencyMessageDTO);
        return ResultVO.success(result);
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除紧急信息数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizEmergencyMessage.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizEmergencyMessage.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizEmergencyMessageService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        BizEmergencyMessageDTO bizEmergencyMessageDTO = bizEmergencyMessageService.findOne(id);
        return ResultVO.success(bizEmergencyMessageDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryBizEmergencyMessagePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<BizEmergencyMessageDTO>> queryBizEmergencyMessagePage(BizSearchEmergencyMessage bizSearchEmergencyMessage, BasePageForm basePageForm) {
        return ResultVO.success(bizEmergencyMessageService.queryListPage(bizSearchEmergencyMessage, basePageForm));
    }

    /**
     * 分页查询回执信息.
     */
    @GetMapping("/queryReceiptInformationPage/{id}")
    @ApiOperation(value = "分页查询回执信息", notes = "传入参数")
    public ResultVO<BizReceiptDetailsDTO> queryReceiptInformationPage(@PathVariable(value = "id") Long id, BasePageForm basePageForm) {
        return ResultVO.success(bizEmergencyMessageService.queryReceiptInformationPage(id, basePageForm));
    }

    /**
     * 补发
     */
    @PostMapping("/reissue/{id}")
    @ApiOperation(value = "补发", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "补发")
    public ResultVO reissue(@RequestBody List<BizUserEmergencyDTO> user, @PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        return bizEmergencyMessageService.reissue(user, id) > 0 ? ResultVO.success() : ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
    }

    /**
     * 分页查询未阅已阅
     */
    @GetMapping("/queryUnreadReadPage")
    @ApiOperation(value = "分页查询未阅已阅", notes = "传入参数")
    public ResultVO<PageResult<BizEmergencyMessageDTO>> queryUnreadReadPage(BizQueryUnreadReadPageDTO bizQueryUnreadReadPageDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizEmergencyMessageService.queryUnreadReadPage(bizQueryUnreadReadPageDTO, basePageForm));
    }

    /**
     * 回复
     */
    @PostMapping("/reply")
    @ApiOperation(value = "回复", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "紧急信息回复")
    public ResultVO reply(@RequestBody ReplyDTO replyContent) {
        return bizEmergencyMessageService.reply(replyContent) > 0 ? ResultVO.success() : ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
    }

    /**
     * 紧急信息查阅查询单条数据.
     */
    @GetMapping("/QuerInformationAccess/{id}")
    @ApiOperation(value = "紧急信息查阅查询单条数据", notes = "传入参数")
    public ResultVO QuerInformationAccess(@PathVariable Long id) {
        BizEmergencyMessageDTO bizEmergencyMessageDTO = bizEmergencyMessageService.QuerInformationAccess(id);
        return ResultVO.success(bizEmergencyMessageDTO);
    }

    /**
     * 催办
     */
    @PostMapping("/urge")
    @ApiOperation(value = "催办", notes = "传入参数")
    public ResultVO urge(@RequestBody List<String> urgeUserPhone) {
        bizEmergencyMessageService.urge(urgeUserPhone);
        return ResultVO.success(true);
    }

    /**
     * 查询紧急信息未阅角标
     */
    @GetMapping("/getAngleMark")
    @ApiOperation(value = "查询紧急信息未阅角标", notes = "传入参数")
    public ResultVO<Integer> getAngleMark() {
        return ResultVO.success(bizEmergencyMessageService.getAngleMark());
    }
}
