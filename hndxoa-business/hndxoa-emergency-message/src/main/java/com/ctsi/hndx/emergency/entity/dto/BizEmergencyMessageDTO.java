package com.ctsi.hndx.emergency.entity.dto;

import com.ctsi.hndx.annotations.Dict;
import com.ctsi.hndx.common.BaseDtoEntity;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 紧急信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizEmergencyMessageDTO对象", description = "紧急信息")
public class BizEmergencyMessageDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题", required = true)
    @NotNull(message = "标题不能为空")
    private String title;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotNull(message = "联系电话不能为空")
    private String contactNumber;

    /**
     * 信息类型（调用数据字典informationType 紧急会议、紧急通知）
     */
    @ApiModelProperty(value = "信息类型（调用数据字典informationType 紧急会议、紧急通知）", required = true)
    @Dict(dicCode = "informationType")
    @NotNull(message = "信息类型不能为空")
    private Integer informationType;

    /**
     * 信息内容
     */
    @ApiModelProperty(value = "信息内容", required = true)
    @NotNull(message = "紧急信息内容不能为空")
    private String informationContent;

    /**
     * 回复截止时间
     */
    @ApiModelProperty(value = "回复截止时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime replyDeadline;

    /**
     * 报送用户
     */
    @ApiModelProperty(value = "报送用户（增加的时候需要传入）")
    private List<BizUserEmergencyDTO> userList;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人")
    private String createName;


    /**
     * 发送部门id
     */
    @ApiModelProperty(value = "发送部门id")
    private Long departmentId;


    /**
     * 发送部门名称
     */
    @ApiModelProperty(value = "发送部门名称")
    private String departmentName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private LocalDateTime replyTime;


}
