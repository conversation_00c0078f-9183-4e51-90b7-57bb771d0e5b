package com.ctsi.hndx.emergency.service;

import com.ctsi.hndx.emergency.entity.dto.BizUserEmergencyDTO;
import com.ctsi.hndx.emergency.entity.BizUserEmergency;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 紧急通知和用户中间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface IBizUserEmergencyService extends SysBaseServiceI<BizUserEmergency> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizUserEmergencyDTO> queryListPage(BizUserEmergencyDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizUserEmergencyDTO> queryList(BizUserEmergencyDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizUserEmergencyDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizUserEmergencyDTO create(BizUserEmergencyDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizUserEmergencyDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizUserEmergencyId
     * @param code
     * @return
     */
    boolean existByBizUserEmergencyId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizUserEmergencyDTO> dataList);

    /**
     * 根据用户ID更新数据
     * @param userDTO
     * @return
     */
    Boolean updateByUserId(CscpUserDTO userDTO);


}
