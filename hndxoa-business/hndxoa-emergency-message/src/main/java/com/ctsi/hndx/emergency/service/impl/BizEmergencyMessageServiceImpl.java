package com.ctsi.hndx.emergency.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.emergency.constant.EmergencyConstant;
import com.ctsi.hndx.emergency.entity.BizUserEmergency;
import com.ctsi.hndx.emergency.entity.dto.*;
import com.ctsi.hndx.emergency.mapper.BizUserEmergencyMapper;
import com.ctsi.hndx.emergency.service.IBizUserEmergencyService;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.leadershipEntrustment.mapper.BizLeadershipEntrustmentMapper;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.emergency.entity.BizEmergencyMessage;
import com.ctsi.hndx.emergency.mapper.BizEmergencyMessageMapper;
import com.ctsi.hndx.emergency.service.IBizEmergencyMessageService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 紧急信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Slf4j
@Service
public class BizEmergencyMessageServiceImpl extends SysBaseServiceImpl<BizEmergencyMessageMapper, BizEmergencyMessage> implements IBizEmergencyMessageService {

    @Autowired
    private BizEmergencyMessageMapper bizEmergencyMessageMapper;

    @Autowired
    private BizLeadershipEntrustmentMapper bizLeadershipEntrustmentMapper;

    @Autowired
    private IBizUserEmergencyService iBizUserEmergencyService;

    @Autowired
    private BizUserEmergencyMapper bizUserEmergencyMapper;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    /**
     * 翻页
     *
     * @param bizSearchEmergencyMessage
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizEmergencyMessageDTO> queryListPage(BizSearchEmergencyMessage bizSearchEmergencyMessage, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizEmergencyMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(bizSearchEmergencyMessage.getInformationContent()), BizEmergencyMessage::getInformationContent, bizSearchEmergencyMessage.getInformationContent());
        queryWrapper.ge(!Objects.isNull(bizSearchEmergencyMessage.getStartTime()), BizEmergencyMessage::getCreateTime, bizSearchEmergencyMessage.getStartTime());
        queryWrapper.le(!Objects.isNull(bizSearchEmergencyMessage.getEndTime()), BizEmergencyMessage::getCreateTime, bizSearchEmergencyMessage.getEndTime());
        queryWrapper.orderByDesc(BizEmergencyMessage::getCreateTime);
        queryWrapper.eq(BizEmergencyMessage::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.like(!Objects.isNull(bizSearchEmergencyMessage.getTitle()), BizEmergencyMessage::getTitle, bizSearchEmergencyMessage.getTitle());

        IPage<BizEmergencyMessage> pageData = bizEmergencyMessageMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizEmergencyMessageDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizEmergencyMessageDTO.class));

        //填充机构
        fill(data.getRecords());

        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizEmergencyMessageDTO findOne(Long id) {
        BizEmergencyMessage bizEmergencyMessage = bizEmergencyMessageMapper.selectById(id);
        BizEmergencyMessageDTO bizEmergencyMessageDTO = BeanConvertUtils.copyProperties(bizEmergencyMessage, BizEmergencyMessageDTO.class);
        //报送领导
        List<BizUserEmergency> bizUserEmergencies = bizUserEmergencyMapper.selectListNoAdd(new LambdaQueryWrapper<BizUserEmergency>().eq(BizUserEmergency::getEmergencyMessageId, id));
        bizEmergencyMessageDTO.setUserList(ListCopyUtil.copy(bizUserEmergencies, BizUserEmergencyDTO.class));
        Map<Long, BizUserEmergencyDTO> bizUserEmergencyMap = bizEmergencyMessageDTO.getUserList().stream().collect(Collectors.toMap(BizUserEmergencyDTO::getUserId, BizUserEmergencyDTO -> BizUserEmergencyDTO));
        BizUserEmergencyDTO bizUserEmergencyDTO = bizUserEmergencyMap.get(SecurityUtils.getCurrentUserId());
        if (!Objects.isNull(bizUserEmergencyDTO)) {
            bizEmergencyMessageDTO.setReplyContent(bizUserEmergencyDTO.getReplyContent());
            bizEmergencyMessageDTO.setReplyTime(bizUserEmergencyDTO.getReplyTime());
        }
        return bizEmergencyMessageDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizEmergencyMessageDTO create(BizEmergencyMessageDTO entityDTO) {
        //用户去重
        List<BizUserEmergencyDTO> bizUserEmergencyList = entityDTO.getUserList();
        Set<Long> set = new HashSet<>();
        List<BizUserEmergencyDTO> duplicateList = new LinkedList<>();
        bizUserEmergencyList.forEach(i -> {
            if (!set.contains(i.getUserId())) {
                duplicateList.add(i);
                set.add(i.getUserId());
            }
        });
        entityDTO.setUserList(new LinkedList<>(duplicateList));
        //新增基础数据
        BizEmergencyMessage bizEmergencyMessage = BeanConvertUtils.copyProperties(entityDTO, BizEmergencyMessage.class);
        save(bizEmergencyMessage);
        //用户和数据关联
        entityDTO.getUserList().forEach(i -> i.setPreviewType(0));
        List<BizUserEmergency> userList = ListCopyUtil.copy(entityDTO.getUserList(), BizUserEmergency.class);
        userList.forEach(i -> i.setEmergencyMessageId(bizEmergencyMessage.getId()));
        iBizUserEmergencyService.saveBatch(userList);


//        //新增成功发送短信后，将给领导、联络人发送短信提醒
//        List<String> userTelephoneList = entityDTO.getUserList().stream().map(i -> i.getTelephone()).collect(Collectors.toList());
//        //查询这些用户委托的用户
//        List<BizLeadershipEntrustment> bizLeadershipEntrustments = bizLeadershipEntrustmentMapper.selectListNoAdd(new LambdaQueryWrapper<BizLeadershipEntrustment>()
//                .select(BizLeadershipEntrustment::getLiaisonManTelephone)
//                .in(BizLeadershipEntrustment::getLeaderId, entityDTO.getUserList().stream().map(i -> i.getUserId()).collect(Collectors.toList())));
//        userTelephoneList.addAll(bizLeadershipEntrustments.stream().map(i -> i.getLiaisonManTelephone()).collect(Collectors.toList()));

        try {
            StringBuffer stringBuffer = new StringBuffer("【协同办公】您有一条紧急信息，请登录协同办公系统进行查收！");
            if (!Objects.isNull(entityDTO.getReplyDeadline())) {
                LocalDateTime replyDeadline = entityDTO.getReplyDeadline();
                stringBuffer
                        .append("回复截止时间为")
                        .append(replyDeadline.getYear())
                        .append("年")
                        .append(replyDeadline.getMonth().getValue())
                        .append("月")
                        .append(replyDeadline.getDayOfMonth())
                        .append("日")
                        .append(replyDeadline.getHour())
                        .append("时")
                        .append(replyDeadline.getMinute())
                        .append("分");
            }
            //调用短信接口
            Map<String, Object> pushMap = new HashMap<>(2);
            pushMap.put("moileList", entityDTO.getUserList().stream().map(i -> i.getTelephone()).collect(Collectors.toList()));
            pushMap.put("titile", stringBuffer.toString());
            String url = SysConstant.sendSmsByPhoneListApi.replace(SysConstant.replaceValue, "tSysSms");
            ResultVO responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + url, pushMap, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new IllegalArgumentException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return entityDTO;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizEmergencyMessageMapper.deleteById(id);
    }

    /**
     * 回执情况
     *
     * @param id
     * @param basePageForm
     * @return
     */
    @Override
    public BizReceiptDetailsDTO queryReceiptInformationPage(Long id, BasePageForm basePageForm) {
        IPage<BizUserEmergency> pageData = bizUserEmergencyMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), new LambdaQueryWrapper<BizUserEmergency>().eq(BizUserEmergency::getEmergencyMessageId, id));
        IPage<BizUserEmergencyDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizUserEmergencyDTO.class));

        //用户对应的联络人
        List<BizLeadershipEntrustment> bizLeadershipEntrustmentList = bizLeadershipEntrustmentMapper
                .selectListNoAdd(new LambdaQueryWrapper<BizLeadershipEntrustment>()
                        .select(BizLeadershipEntrustment::getLeaderId, BizLeadershipEntrustment::getLiaisonManId, BizLeadershipEntrustment::getLiaisonManName, BizLeadershipEntrustment::getLiaisonManTelephone)
                        .in(BizLeadershipEntrustment::getLeaderId,
                                data.getRecords().stream().map(i -> i.getUserId()).collect(Collectors.toSet())));

        //将联络人组装成map
        Map<Long, List<BizLeadershipEntrustment>> map = new HashMap<>();
        bizLeadershipEntrustmentList.forEach(i -> {
            List<BizLeadershipEntrustment> bizLeadershipEntrustments = new LinkedList<>();
            if (map.containsKey(i.getLeaderId())) {
                bizLeadershipEntrustments = map.get(i.getLeaderId());
                bizLeadershipEntrustments.add(i);
                map.put(i.getLeaderId(), bizLeadershipEntrustments);
            } else {
                bizLeadershipEntrustments.add(i);
                map.put(i.getLeaderId(), bizLeadershipEntrustments);
            }
        });

        //通过联络人组装成的map和领导进行匹配
        List<BizUserEmergencyDTO> list = new ArrayList<>();
        data.getRecords().stream().forEach(i -> {
            List<BizLeadershipEntrustment> bizLeadershipEntrustments = map.get(i.getUserId());
            if (!Objects.isNull(bizLeadershipEntrustments)) {
                bizLeadershipEntrustments.stream().forEach(y -> {
                    BizUserEmergencyDTO bizUserEmergencyDTO = new BizUserEmergencyDTO();
                    bizUserEmergencyDTO.setId(i.getId());
                    bizUserEmergencyDTO.setReplyContent(i.getReplyContent());
                    bizUserEmergencyDTO.setReplyTime(i.getReplyTime());
                    bizUserEmergencyDTO.setUserId(i.getUserId());
                    bizUserEmergencyDTO.setUserName(i.getUserName());
                    bizUserEmergencyDTO.setTelephone(i.getTelephone());
                    bizUserEmergencyDTO.setBranchId(i.getBranchId());
                    bizUserEmergencyDTO.setBranchName(i.getBranchName());
                    bizUserEmergencyDTO.setLiaisonManId(y.getLiaisonManId());
                    bizUserEmergencyDTO.setLiaisonManName(y.getLiaisonManName());
                    bizUserEmergencyDTO.setLiaisonManTelephone(y.getLiaisonManTelephone());
                    list.add(bizUserEmergencyDTO);
                });
            } else {
                list.add(i);
            }
        });

        //组装数据
        BizReceiptDetailsDTO build = BizReceiptDetailsDTO.builder()
                //回复数量
                .replyCount(data.getRecords().stream().filter(i -> StringUtils.isNotEmpty(i.getReplyContent())).collect(Collectors.toList()).size())
                //发送人数
                .sendOutCount(bizUserEmergencyMapper.selectListNoAdd(new LambdaQueryWrapper<BizUserEmergency>().select(BizUserEmergency::getId).eq(BizUserEmergency::getEmergencyMessageId, id)).size())
                //发送的用户数据（分页）
                .bizUserEmergencyList(new PageResult(list,
                        data.getTotal(), data.getCurrent())).build();
        return build;
    }

    /**
     * 补发
     *
     * @param userId
     * @param id
     * @return
     */
    @Override
    public Integer reissue(List<BizUserEmergencyDTO> userId, Long id) {
        //查询这条数据对应的用户
        List<BizUserEmergency> bizUserEmergencies = bizUserEmergencyMapper.selectListNoAdd(
                new LambdaQueryWrapper<BizUserEmergency>().select(BizUserEmergency::getUserId).eq(BizUserEmergency::getEmergencyMessageId, id));
        //将这条数据转成set
        Set<Long> UserIdSet = bizUserEmergencies.stream().map(i -> i.getUserId()).collect(Collectors.toSet());
        //进行过滤
        List<BizUserEmergencyDTO> bizUserEmergencyList = userId.stream().filter(i -> !UserIdSet.contains(i.getUserId())).collect(Collectors.toList());

        List<BizUserEmergency> bizUserEmergenctList = ListCopyUtil.copy(bizUserEmergencyList, BizUserEmergency.class);
        bizUserEmergenctList.forEach(i -> {
            i.setEmergencyMessageId(id);
            i.setPreviewType(EmergencyConstant.CIRCULATE_NOT_READ);
        });

        //补发
        boolean saveBoolean = iBizUserEmergencyService.saveBatch(bizUserEmergenctList);
        return saveBoolean ? 1 : 0;
    }

    /**
     * 分页查询未阅已阅
     *
     * @param bizQueryUnreadReadPageDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizEmergencyMessageDTO> queryUnreadReadPage(BizQueryUnreadReadPageDTO bizQueryUnreadReadPageDTO, BasePageForm basePageForm) {
        Integer queryType = bizQueryUnreadReadPageDTO.getQueryType();
        if (0 != queryType && 1 != queryType) {
            throw new BusinessException("传入的查询类型有误！");
        }

        bizQueryUnreadReadPageDTO.setUserId(SecurityUtils.getCurrentUserId());

        IPage<BizEmergencyMessageDTO> bizEmergency = bizUserEmergencyMapper.queryUnreadReadPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizQueryUnreadReadPageDTO);
        //填充机构
        fill(bizEmergency.getRecords());

        return new PageResult<>(bizEmergency.getRecords(),
                bizEmergency.getTotal(), bizEmergency.getCurrent());
    }

    /**
     * 回复
     *
     * @param replyContent
     * @return
     */
    @Override
    public Integer reply(ReplyDTO replyContent) {
        //判断当前用户是否回复过
        BizUserEmergency bizUserEmergencyBoo = bizUserEmergencyMapper.selectOneNoAdd(
                new LambdaQueryWrapper<BizUserEmergency>()
                        .select(BizUserEmergency::getReplyContent)
                        .eq(BizUserEmergency::getUserId, SecurityUtils.getCurrentUserId())
                        .eq(BizUserEmergency::getEmergencyMessageId, replyContent.getEmergencyMessageId()));
        if (null != bizUserEmergencyBoo && !Objects.isNull(bizUserEmergencyBoo.getReplyContent())) {
            throw new BusinessException("你已回复,不能再次回复");
        }

        //回复
        BizUserEmergency bizUserEmergency = new BizUserEmergency();
        bizUserEmergency.setReplyContent(replyContent.getReplyContent());
        bizUserEmergency.setReplyTime(LocalDateTime.now());
        bizUserEmergency.setPreviewType(EmergencyConstant.READ);
        return bizUserEmergencyMapper.update(
                bizUserEmergency,
                new LambdaQueryWrapper<BizUserEmergency>()
                        .eq(BizUserEmergency::getEmergencyMessageId, replyContent.getEmergencyMessageId())
                        .eq(BizUserEmergency::getUserId, SecurityUtils.getCurrentUserId()));
    }

    /**
     * 紧急信息查阅查询单条数据
     *
     * @param id
     * @return
     */
    @Override
    public BizEmergencyMessageDTO QuerInformationAccess(Long id) {
        BizEmergencyMessageDTO one = this.findOne(id);
        BizUserEmergency bizUserEmergency = new BizUserEmergency();
        bizUserEmergency.setPreviewType(1);
        //已阅
        bizUserEmergencyMapper.update(bizUserEmergency,
                new LambdaQueryWrapper<BizUserEmergency>()
                        .eq(BizUserEmergency::getUserId, SecurityUtils.getCurrentUserId())
                        .eq(BizUserEmergency::getEmergencyMessageId, id)
                        .eq(BizUserEmergency::getPreviewType, EmergencyConstant.CIRCULATE_NOT_READ));
        return one;
    }

    /**
     * 催办
     *
     * @param urgeUserPhone
     * @return
     */
    @Override
    public void urge(List<String> urgeUserPhone) {
        try {
            StringBuffer stringBuffer = new StringBuffer("【协同办公】您有一条紧急信息，请登录协同办公系统进行查收！");
            //调用短信接口
            Map<String, Object> pushMap = new HashMap<>(2);
            pushMap.put("moileList", urgeUserPhone);
            pushMap.put("titile", stringBuffer.toString());
            String url = SysConstant.sendSmsByPhoneListApi.replace(SysConstant.replaceValue, "tSysSms");
            ResultVO responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + url, pushMap, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new IllegalArgumentException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询紧急信息未阅角标
     *
     * @return
     */
    @Override
    public Integer getAngleMark() {
        BizQueryUnreadReadPageDTO build = BizQueryUnreadReadPageDTO.builder()
                .queryType(0).userId(SecurityUtils.getCurrentUserId()).build();
        PageResult<BizEmergencyMessageDTO> bizEmergencyMessageDTOPageResult =
                this.queryUnreadReadPage(build, new BasePageForm(1, Integer.MAX_VALUE));
        return bizEmergencyMessageDTOPageResult.getData().size();
    }

    /**
     * 给数据填充机构名称
     *
     * @param bizEmergencyMessageList
     */
    public void fill(List<BizEmergencyMessageDTO> bizEmergencyMessageList) {
        if (!bizEmergencyMessageList.isEmpty()) {
            //填充用户对应的部门名称
            List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getOrgName, CscpOrg::getId).in(CscpOrg::getId, bizEmergencyMessageList.stream().map(i -> i.getDepartmentId()).collect(Collectors.toList())).groupBy(CscpOrg::getId));
            //将机构转为map
            final Map<Long, CscpOrg> orgMap = orgList.stream().collect(Collectors.toMap(CscpOrg::getId, CscpOrg -> CscpOrg));

            //给数据填充对应的部门名称
            bizEmergencyMessageList.forEach(i -> {
                CscpOrg cscpOrg = orgMap.get(i.getDepartmentId());
                i.setDepartmentName(cscpOrg.getOrgName());
            });
        }
    }


}
