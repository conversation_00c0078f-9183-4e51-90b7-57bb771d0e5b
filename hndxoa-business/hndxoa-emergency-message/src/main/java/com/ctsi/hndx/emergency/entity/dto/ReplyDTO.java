package com.ctsi.hndx.emergency.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.hndx.emergency.entity.dto
 * @ClassName: ReplyDTO
 * @Author: json
 * @Description:
 * @Date: 2022/2/14 15:03
 * @Version: 1.0
 */
@Data
@ApiModel(value = "回复", description = "回复")
public class ReplyDTO {

    /**
     * 紧急信息id不能为空
     */
    @ApiModelProperty(value = "紧急信息id不能为空", required = true)
    @NotNull(message = "紧急信息id不能为空不能为空")
    private Long emergencyMessageId;


    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

}
