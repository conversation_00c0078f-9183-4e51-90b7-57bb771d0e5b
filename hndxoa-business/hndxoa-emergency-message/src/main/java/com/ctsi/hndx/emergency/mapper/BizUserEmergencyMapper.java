package com.ctsi.hndx.emergency.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.emergency.entity.BizUserEmergency;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.emergency.entity.dto.BizEmergencyMessageDTO;
import com.ctsi.hndx.emergency.entity.dto.BizQueryUnreadReadPageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 紧急通知和用户中间表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface BizUserEmergencyMapper extends MybatisBaseMapper<BizUserEmergency> {

    @InterceptorIgnore(others = "tenantId@true")
    IPage<BizEmergencyMessageDTO> queryUnreadReadPage(IPage<BizEmergencyMessageDTO> page, @Param("param") BizQueryUnreadReadPageDTO bizQueryUnreadReadPageDTO);
}
