package com.ctsi.hndx.emergency.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 紧急通知和用户中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizUserEmergencyDTO对象", description = "紧急通知和用户中间表")
public class BizUserEmergencyDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 紧急信息id
     */
    @ApiModelProperty(value = "紧急信息id", required = true)
    @NotNull(message = "紧急信息id不能为空")
    private Long emergencyMessageId;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容", required = false)
    private String replyContent;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间", required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime replyTime;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称", required = true)
    @NotNull(message = "用户名称不能为空")
    private String userName;

    /**
     * 用户所在单位id
     */
    @ApiModelProperty(value = "用户所在单位id", required = true)
    @NotNull(message = "用户所在单位id不能为空")
    private Long unitId;

    /**
     * 用户所在单位名称
     */
    @ApiModelProperty(value = "用户所在单位名称", required = true)
    @NotNull(message = "用户所在单位名称不能为空")
    private String unitName;

    /**
     * 用户所在部门id
     */
    @ApiModelProperty(value = "用户所在部门id", required = true)
    @NotNull(message = "用户所在部门id不能为空")
    private Long branchId;

    /**
     * 用户所在部门名称
     */
    @ApiModelProperty(value = "用户所在部门名称", required = true)
    @NotNull(message = "用户所在部门名称不能为空")
    private String branchName;

    /**
     * 用户电话号码
     */
    @ApiModelProperty(value = "用户电话号码", required = true)
    @NotNull(message = "用户电话号码不能为空")
    private String telephone;

    /**
     * 是否预览（0：未阅 1：已阅）
     */
    @ApiModelProperty(value = "是否预览（0：未阅 1：已阅）", required = false)
    private Integer previewType;

    /**
     * 联络员id
     */
    @ApiModelProperty(value = "联络员id", required = false)
    private Long liaisonManId;

    /**
     * 联络员名称
     */
    @ApiModelProperty(value = "联络员名称", required = false)
    private String liaisonManName;

    /**
     * 联络员手机号码
     */
    @ApiModelProperty(value = "联络员手机号码", required = false)
    private String liaisonManTelephone;

}
