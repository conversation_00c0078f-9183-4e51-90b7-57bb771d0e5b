package com.ctsi.hndx.emergency.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.emergency.entity.BizUserEmergency;
import com.ctsi.hndx.emergency.entity.dto.BizUserEmergencyDTO;
import com.ctsi.hndx.emergency.mapper.BizUserEmergencyMapper;
import com.ctsi.hndx.emergency.service.IBizUserEmergencyService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 紧急通知和用户中间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Slf4j
@Service
public class BizUserEmergencyServiceImpl extends SysBaseServiceImpl<BizUserEmergencyMapper, BizUserEmergency> implements IBizUserEmergencyService {

    @Autowired
    private BizUserEmergencyMapper bizUserEmergencyMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizUserEmergencyDTO> queryListPage(BizUserEmergencyDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserEmergency> queryWrapper = new LambdaQueryWrapper();

        IPage<BizUserEmergency> pageData = bizUserEmergencyMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizUserEmergencyDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizUserEmergencyDTO.class));

        return new PageResult<BizUserEmergencyDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizUserEmergencyDTO> queryList(BizUserEmergencyDTO entityDTO) {
        LambdaQueryWrapper<BizUserEmergency> queryWrapper = new LambdaQueryWrapper();
            List<BizUserEmergency> listData = bizUserEmergencyMapper.selectList(queryWrapper);
            List<BizUserEmergencyDTO> BizUserEmergencyDTOList = ListCopyUtil.copy(listData, BizUserEmergencyDTO.class);
        return BizUserEmergencyDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizUserEmergencyDTO findOne(Long id) {
        BizUserEmergency  bizUserEmergency =  bizUserEmergencyMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizUserEmergency,BizUserEmergencyDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUserEmergencyDTO create(BizUserEmergencyDTO entityDTO) {
       BizUserEmergency bizUserEmergency =  BeanConvertUtils.copyProperties(entityDTO,BizUserEmergency.class);
        save(bizUserEmergency);
        return  BeanConvertUtils.copyProperties(bizUserEmergency,BizUserEmergencyDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizUserEmergencyDTO entity) {
        BizUserEmergency bizUserEmergency = BeanConvertUtils.copyProperties(entity,BizUserEmergency.class);
        return bizUserEmergencyMapper.updateById(bizUserEmergency);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizUserEmergencyMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizUserEmergencyId
     * @return
     */
    @Override
    public boolean existByBizUserEmergencyId(Long BizUserEmergencyId) {
        if (BizUserEmergencyId != null) {
            LambdaQueryWrapper<BizUserEmergency> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizUserEmergency::getId, BizUserEmergencyId);
            List<BizUserEmergency> result = bizUserEmergencyMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizUserEmergencyDTO> dataList) {
        List<BizUserEmergency> result = ListCopyUtil.copy(dataList, BizUserEmergency.class);
        return saveBatch(result);
    }


    @Override
    public Boolean
    updateByUserId(CscpUserDTO userDTO) {
        LambdaUpdateWrapper<BizUserEmergency> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(BizUserEmergency::getUserId, userDTO.getId())
                .set(BizUserEmergency::getUserName, userDTO.getLoginName())
                .set(BizUserEmergency::getBranchId, userDTO.getDepartmentId())
                .set(BizUserEmergency::getBranchName, userDTO.getDepartmentName())
                .set(BizUserEmergency::getUnitId, userDTO.getCompanyId())
                .set(BizUserEmergency::getUnitName, userDTO.getCompanyName());
        bizUserEmergencyMapper.update(null, lambdaUpdateWrapper);
        return true;
    }
}
