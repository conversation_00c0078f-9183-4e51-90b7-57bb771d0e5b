<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndx.emergency.mapper.BizUserEmergencyMapper">

    <resultMap id="emergency" type="com.ctsi.hndx.emergency.entity.dto.BizEmergencyMessageDTO">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="informationContent" column="information_content"/>
        <result property="informationType" column="information_type"/>
        <result property="departmentId" column="department_id"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyTime" column="reply_time"/>
    </resultMap>


    <sql id="selectEmergency">
        SELECT bem.id,bem.title,bem.information_type,bem.information_content,bem.department_id,bem.create_name,bem.create_time,bue.reply_content,bue.reply_time
    </sql>

    <select id="queryUnreadReadPage" parameterType="com.ctsi.hndx.emergency.entity.dto.BizQueryUnreadReadPageDTO"
            resultMap="emergency">
        <include refid="selectEmergency"></include>
        FROM biz_emergency_message bem
        INNER JOIN biz_user_emergency bue
        ON bem.id = bue.emergency_message_id
        <where>
            bue.user_id = #{param.userId} AND bue.preview_type = #{param.queryType}
            <if test="param.informationContent != null and param.informationContent != ''">
                AND bem.information_content LIKE CONCAT('%',#{param.informationContent},'%')
            </if>
            <if test="param.startTime != null">
                AND bem.create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND bem.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.title != null and param.title != ''">
                AND bem.title &lt;= #{param.title}
            </if>
        </where>
        ORDER BY bem.create_time DESC
    </select>
</mapper>
