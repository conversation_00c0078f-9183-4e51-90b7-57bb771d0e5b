CREATE TABLE "myapp"."biz_emergency_message"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(100),
"contact_number" VARCHAR(20),
"information_type" INT,
"information_content" VARCHAR(255),
"reply_deadline" TIMESTAMP(0),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(O, N "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_emergency_message" IS '紧急信息';COMMENT ON COLUMN "myapp"."biz_emergency_message"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."title" IS '标题';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."contact_number" IS '联系电话';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."information_type" IS '信息类型（调用数据字典informationType 紧急会议、紧急通知）';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."information_content" IS '信息内容';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."reply_deadline" IS '回复截止时间';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_emergency_message"."deleted" IS '逻辑删除';




CREATE TABLE "myapp"."biz_user_emergency"
(
"id" BIGINT NOT NULL,
"user_id" BIGINT,
"emergency_message_id" BIGINT,
"reply_content" VARCHAR(255),
"reply_time" TIMESTAMP(0),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
"user_name" VARCHAR(100),
"unit_id" BIGINT,
"unit_name" VARCHAR(100),
"branch_, id" BIGINT,
"branch_name" VARCHAR(100),
"telephone" VARCHAR(100),
"preview_type" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_user_emergency" IS '紧急通知和用户中间表';COMMENT ON COLUMN "myapp"."biz_user_emergency"."id" IS '主键';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."user_id" IS '用户id';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."emergency_message_id" IS '紧急信息id';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."reply_content" IS '回复内容';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."reply_time" IS '回复时间';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."update_by" IS '更新人ID';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."update_name" IS '更新人名称';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."update_time" IS '更新时间';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."user_name" IS '用户名称';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."unit_id" IS '用户所在单位id';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."unit_name" IS '用户所在单位名称';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."branch_id" IS '用户所在部门id';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."branch_name" IS '用户所在部门名称';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."telephone" IS '用户电话号码';
COMMENT ON COLUMN "myapp"."biz_user_emergency"."preview_type" IS '是否预览（0：未阅 1：已阅）';




