CREATE TABLE `biz_emergency_message` (
                                         `id` bigint NOT NULL COMMENT '主键',
                                         `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                         `contact_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
                                         `information_type` int DEFAULT NULL COMMENT '信息类型（调用数据字典informationType 紧急会议、紧急通知）',
                                         `information_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '信息内容',
                                         `reply_deadline` datetime DEFAULT NULL COMMENT '回复截止时间',
                                         `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                         `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                         `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                         `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                         `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='紧急信息';

CREATE TABLE `biz_user_emergency` (
                                      `id` bigint NOT NULL COMMENT '主键',
                                      `user_id` bigint DEFAULT NULL COMMENT '用户id',
                                      `emergency_message_id` bigint DEFAULT NULL COMMENT '紧急信息id',
                                      `reply_content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '回复内容',
                                      `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
                                      `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                      `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
                                      `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人名称',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                      `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                      `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                      `deleted` int DEFAULT NULL COMMENT '逻辑删除',
                                      `user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户名称',
                                      `unit_id` bigint DEFAULT NULL COMMENT '用户所在单位id',
                                      `unit_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户所在单位名称',
                                      `branch_id` bigint DEFAULT NULL COMMENT '用户所在部门id',
                                      `branch_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户所在部门名称',
                                      `telephone` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户电话号码',
                                      `preview_type` int DEFAULT NULL COMMENT '是否预览（0：未阅 1：已阅）',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='紧急通知和用户中间表';

