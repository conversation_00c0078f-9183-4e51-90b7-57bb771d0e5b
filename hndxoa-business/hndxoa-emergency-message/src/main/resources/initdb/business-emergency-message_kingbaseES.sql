CREATE TABLE "public"."biz_emergency_message" (
                                                  "id" int8 NOT NULL,
                                                  "title" varchar(100 char) NULL,
	"contact_number" varchar(20 char) NULL,
	"information_type" int4 NULL,
	"information_content" varchar(255 char) NULL,
	"reply_deadline" timestamp(6) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	CONSTRAINT "biz_emergency_message_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_emergency_message"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."biz_emergency_message"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_emergency_message"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_emergency_message"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_emergency_message"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."biz_emergency_message"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."biz_emergency_message"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."biz_emergency_message"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."biz_emergency_message"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_emergency_message"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_emergency_message"."reply_deadline" IS '回复截止时间';
COMMENT ON COLUMN "public"."biz_emergency_message"."information_content" IS '信息内容';
COMMENT ON COLUMN "public"."biz_emergency_message"."information_type" IS '信息类型（调用数据字典informationType 紧急会议、紧急通知）';
COMMENT ON COLUMN "public"."biz_emergency_message"."contact_number" IS '联系电话';
COMMENT ON COLUMN "public"."biz_emergency_message"."title" IS '标题';
COMMENT ON COLUMN "public"."biz_emergency_message"."id" IS '主键';
COMMENT ON TABLE "public"."biz_emergency_message" IS '紧急信息';


CREATE TABLE "public"."biz_user_emergency" (
                                               "id" int8 NOT NULL,
                                               "user_id" int8 NULL,
                                               "emergency_message_id" int8 NULL,
                                               "reply_content" varchar(255 char) NULL,
	"reply_time" timestamp(6) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"user_name" varchar(100 char) NULL,
	"unit_id" int8 NULL,
	"unit_name" varchar(100 char) NULL,
	"branch_id" int8 NULL,
	"branch_name" varchar(100 char) NULL,
	"telephone" varchar(100 char) NULL,
	"preview_type" int4 NULL,
	CONSTRAINT "biz_user_emergency_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_user_emergency"."preview_type" IS '是否预览（0：未阅 1：已阅）';
COMMENT ON COLUMN "public"."biz_user_emergency"."telephone" IS '用户电话号码';
COMMENT ON COLUMN "public"."biz_user_emergency"."branch_name" IS '用户所在部门名称';
COMMENT ON COLUMN "public"."biz_user_emergency"."branch_id" IS '用户所在部门id';
COMMENT ON COLUMN "public"."biz_user_emergency"."unit_name" IS '用户所在单位名称';
COMMENT ON COLUMN "public"."biz_user_emergency"."unit_id" IS '用户所在单位id';
COMMENT ON COLUMN "public"."biz_user_emergency"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."biz_user_emergency"."deleted" IS '逻辑删除';
COMMENT ON COLUMN "public"."biz_user_emergency"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_user_emergency"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_user_emergency"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_user_emergency"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."biz_user_emergency"."update_name" IS '更新人名称';
COMMENT ON COLUMN "public"."biz_user_emergency"."update_by" IS '更新人ID';
COMMENT ON COLUMN "public"."biz_user_emergency"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."biz_user_emergency"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_user_emergency"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_user_emergency"."reply_time" IS '回复时间';
COMMENT ON COLUMN "public"."biz_user_emergency"."reply_content" IS '回复内容';
COMMENT ON COLUMN "public"."biz_user_emergency"."emergency_message_id" IS '紧急信息id';
COMMENT ON COLUMN "public"."biz_user_emergency"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."biz_user_emergency"."id" IS '主键';
COMMENT ON TABLE "public"."biz_user_emergency" IS '紧急通知和用户中间表';
