package com.ctsi.huaihua.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.huaihua.entity.BizEnteringHunanFiling;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.huaihua.entity.dto.BizEnteringHunanFilingDTO;
import com.ctsi.huaihua.entity.dto.BizQueryEnteringHunanFilingDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
public interface BizEnteringHunanFilingMapper extends MybatisBaseMapper<BizEnteringHunanFiling> {

    /**
     * 分页查询收件箱
     *
     * @param page
     * @param enteringHunanCondition
     * @return
     */
    IPage<BizQueryEnteringHunanFilingDTO> queryEnteringHunanFilingInboxPage(IPage<?> page, @Param("enteringHunanCondition") BizEnteringHunanFilingDTO enteringHunanCondition);

}
