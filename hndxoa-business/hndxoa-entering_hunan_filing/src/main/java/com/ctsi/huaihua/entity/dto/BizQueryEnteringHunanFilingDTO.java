package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.receive.entity.dto.InboxDTO;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizQueryEnteringHunanFilingDTO
 * @Author: json
 * @Description:
 * @Date: 2022/12/8 15:02
 * @Version: 1.0
 */
@Data
@ApiModel(value = "查询回显对象", description = "查询回显对象")
public class BizQueryEnteringHunanFilingDTO extends InboxDTO {

    /**
     * 主键id，新增不设置，修改或其它时设值
     */
    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    private Long id;

    /**
     * 来宾单位id
     */
    @ApiModelProperty(value = "来宾单位id")
    private Long guestCompanyId;

    /**
     * 来宾单位名称
     */
    @ApiModelProperty(value = "来宾单位名称")
    private String guestCompanyName;

    /**
     * 来宾职务
     */
    @ApiModelProperty(value = "来宾职务")
    private String guestPost;

    /**
     * 来宾用户名称
     */
    @ApiModelProperty(value = "来宾用户名称")
    private String guestUserName;

    /**
     * 来宾用户id
     */
    @ApiModelProperty(value = "来宾用户id")
    private Long guestUserId;

    /**
     * 宾客级别
     */
    @ApiModelProperty(value = "宾客级别")
    private Integer userLevel;

    /**
     * 同行人数
     */
    @ApiModelProperty(value = "同行人数")
    private Integer peerPeopleCount;

    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    private String placeOfDeparture;

    /**
     * 在湘开始日期
     */
    @ApiModelProperty(value = "在湘开始日期")
    private LocalDate inHunanStartDate;

    /**
     * 在湘结束日期
     */
    @ApiModelProperty(value = "在湘结束日期")
    private LocalDate inHunanEndDate;

    /**
     * 在湘天数
     */
    @ApiModelProperty(value = "在湘天数")
    private Integer inHunanDays;

    /**
     * 工作任务
     */
    @ApiModelProperty(value = "工作任务")
    private Integer workTask;

    /**
     * 具体内容
     */
    @ApiModelProperty(value = "具体内容")
    private String concreteContent;

    /**
     * 联系电话，加密
     */
    @ApiModelProperty(value = "联系电话，加密")
    private String contactNumber;

    /**
     * 联系电话，分段加密
     */
    @ApiModelProperty(value = "联系电话，分段加密")
    private String contactNumberEncryption;

    /**
     * 签收状态 (0:未签收 1:签收)
     */
    @ApiModelProperty(value = "签收状态 (0:未签收 1:签收)")
    private Integer signInState;

    /**
     * 附件文件
     */
    @ApiModelProperty(value = "附件文件")
    private List<CscpEnclosureFile> cscpEnclosureFileList;

}
