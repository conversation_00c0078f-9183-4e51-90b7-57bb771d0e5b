package com.ctsi.huaihua.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.receive.entity.Inbox;
import com.ctsi.hndx.receive.entity.dto.RejectDTO;
import com.ctsi.hndx.receive.mapper.InboxMapper;
import com.ctsi.hndx.receive.service.IInboxService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.huaihua.constant.BizEnteringHunanFilingConstant;
import com.ctsi.huaihua.entity.dto.BizQueryEnteringHunanFilingDTO;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.outingask.entity.dto.BizEnteringHunanFilingConfigDTO;
import com.ctsi.outingask.service.IBizOutingAskConfigService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.huaihua.entity.BizEnteringHunanFiling;
import com.ctsi.huaihua.entity.dto.BizEnteringHunanFilingDTO;
import com.ctsi.huaihua.mapper.BizEnteringHunanFilingMapper;
import com.ctsi.huaihua.service.IBizEnteringHunanFilingService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Slf4j
@Service
public class BizEnteringHunanFilingServiceImpl extends SysBaseServiceImpl<BizEnteringHunanFilingMapper, BizEnteringHunanFiling> implements IBizEnteringHunanFilingService {

    @Autowired
    private BizEnteringHunanFilingMapper bizEnteringHunanFilingMapper;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileRepository;

    @Autowired
    private InboxMapper inboxMapper;

    @Autowired
    private IBizOutingAskConfigService bizOutingAskConfigService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizEnteringHunanFilingDTO> queryListPage(BizEnteringHunanFilingDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizEnteringHunanFiling> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(!Objects.isNull(entityDTO.getSignInState()), BizEnteringHunanFiling::getSignInState, entityDTO.getSignInState());

        IPage<BizEnteringHunanFiling> pageData = bizEnteringHunanFilingMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizEnteringHunanFilingDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizEnteringHunanFilingDTO.class));

        return new PageResult<BizEnteringHunanFilingDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizEnteringHunanFilingDTO> queryList(BizEnteringHunanFilingDTO entityDTO) {
        LambdaQueryWrapper<BizEnteringHunanFiling> queryWrapper = new LambdaQueryWrapper();
        List<BizEnteringHunanFiling> listData = bizEnteringHunanFilingMapper.selectList(queryWrapper);
        List<BizEnteringHunanFilingDTO> BizEnteringHunanFilingDTOList = ListCopyUtil.copy(listData, BizEnteringHunanFilingDTO.class);
        return BizEnteringHunanFilingDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizEnteringHunanFilingDTO findOne(Long id) {
        //根据主键id查询对应的数据
        BizEnteringHunanFiling bizEnteringHunanFiling = bizEnteringHunanFilingMapper.selectById(id);
        BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO = BeanConvertUtils.copyProperties(bizEnteringHunanFiling, BizEnteringHunanFilingDTO.class);

        //不为空查询对应的附件数据
        if (!Objects.isNull(bizEnteringHunanFilingDTO)) {
            //附件文件
            bizEnteringHunanFilingDTO.setCscpEnclosureFileList(cscpEnclosureFileRepository.selectListNoAdd(
                    new LambdaQueryWrapper<CscpEnclosureFile>().eq(CscpEnclosureFile::getFormDataId, bizEnteringHunanFilingDTO.getId())));
        }

        return bizEnteringHunanFilingDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizEnteringHunanFilingDTO create(BizEnteringHunanFilingDTO entityDTO) {
        //查询本单位的上报单位信息
        BizEnteringHunanFilingConfigDTO bizEnteringHunanFilingConfigDTO = bizOutingAskConfigService.queryReportCompanyId();
        if (Objects.isNull(bizEnteringHunanFilingConfigDTO) || Objects.isNull(bizEnteringHunanFilingConfigDTO.getReportCompanyId())) {
            throw new BusinessException("请配置上报单位!");
        }

        BizEnteringHunanFiling bizEnteringHunanFiling = BeanConvertUtils.copyProperties(entityDTO, BizEnteringHunanFiling.class);
        bizEnteringHunanFiling.setSignInState(BizEnteringHunanFilingConstant.notSingedIn);
        save(bizEnteringHunanFiling);

        //获取当前用户数据
        Optional<CscpUserDetail> currentUser = SecurityUtils.getCurrentUser();
        if (Objects.isNull(currentUser) && Objects.isNull(currentUser.get())) {
            throw new BusinessException("无法获取到用户消息,是否已经登录?");
        }
        CscpUserDetail cscpUserDetail = currentUser.get();

        //填充收件箱
        Inbox build = Inbox.builder()
                .sourceCompanyId(cscpUserDetail.getCompanyId())
                .sourceCompanyName(cscpUserDetail.getCompanyName())
                .enclosureId(bizEnteringHunanFiling.getId())
                .processingSheetId(entityDTO.getProcessingSheetId())
                .formDataId(bizEnteringHunanFiling.getId())
                .receiveCompanyId(bizEnteringHunanFilingConfigDTO.getReportCompanyId())
                .receiveCompanyName(bizEnteringHunanFilingConfigDTO.getReportCompanyName())
                .inboxStatus("0")
                .sourceBusinessTable("biz_entering_hunan_filing")
                .sourceBusinessName("enteringHunanFiling").build();
        inboxMapper.insert(build);


        return BeanConvertUtils.copyProperties(bizEnteringHunanFiling, BizEnteringHunanFilingDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizEnteringHunanFilingDTO entity) {
        BizEnteringHunanFiling bizEnteringHunanFiling = BeanConvertUtils.copyProperties(entity, BizEnteringHunanFiling.class);
        return bizEnteringHunanFilingMapper.updateById(bizEnteringHunanFiling);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizEnteringHunanFilingMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizEnteringHunanFilingId
     * @return
     */
    @Override
    public boolean existByBizEnteringHunanFilingId(Long BizEnteringHunanFilingId) {
        if (BizEnteringHunanFilingId != null) {
            LambdaQueryWrapper<BizEnteringHunanFiling> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizEnteringHunanFiling::getId, BizEnteringHunanFilingId);
            List<BizEnteringHunanFiling> result = bizEnteringHunanFilingMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizEnteringHunanFilingDTO> dataList) {
        List<BizEnteringHunanFiling> result = ListCopyUtil.copy(dataList, BizEnteringHunanFiling.class);
        return saveBatch(result);
    }

    /**
     * 分页查询入湘收件箱数据
     *
     * @param bizEnteringHunanFilingDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizQueryEnteringHunanFilingDTO> queryEnteringHunanFilingInboxPage(BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO, BasePageForm basePageForm) {

        IPage<BizQueryEnteringHunanFilingDTO> bizQueryEnteringHunanFilingDTOIPage = bizEnteringHunanFilingMapper.queryEnteringHunanFilingInboxPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizEnteringHunanFilingDTO);

        return new PageResult<BizQueryEnteringHunanFilingDTO>(bizQueryEnteringHunanFilingDTOIPage.getRecords(),
                bizQueryEnteringHunanFilingDTOIPage.getTotal(), bizQueryEnteringHunanFilingDTOIPage.getCurrent());
    }

    /**
     * 签收
     *
     * @param rejectDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer reject(RejectDTO rejectDTO) {
        Inbox inbox = inboxMapper.selectOneNoAdd(
                new LambdaQueryWrapper<Inbox>().eq(Inbox::getId, rejectDTO.getInboxId()).eq(Inbox::getSourceBusinessName, "enteringHunanFiling"));
        if (Objects.isNull(inbox)) {
            throw new BusinessException("没有该数据,无法进行签收!");
        }

        inbox.setInboxStatus(rejectDTO.getInboxStatus());
        inbox.setRejectReason(rejectDTO.getRejectReason());
        inbox.setSigningTime(LocalDateTime.now());
        inboxMapper.updateById(inbox);

        if (!Objects.isNull(inbox.getFormDataId())) {
            BizEnteringHunanFiling bizEnteringHunanFiling = new BizEnteringHunanFiling();
            bizEnteringHunanFiling.setId(inbox.getFormDataId());
            bizEnteringHunanFiling.setSignInState(BizEnteringHunanFilingConstant.signedIn);
            bizEnteringHunanFilingMapper.updateById(bizEnteringHunanFiling);
        }

        return 1;
    }


}
