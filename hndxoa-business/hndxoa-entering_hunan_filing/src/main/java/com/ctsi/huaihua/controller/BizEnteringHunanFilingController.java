package com.ctsi.huaihua.controller;

import com.ctsi.hndx.receive.entity.dto.RejectDTO;
import com.ctsi.huaihua.entity.dto.BizQueryEnteringHunanFilingDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Optional;

import com.ctsi.huaihua.entity.BizEnteringHunanFiling;
import com.ctsi.huaihua.entity.dto.BizEnteringHunanFilingDTO;
import com.ctsi.huaihua.service.IBizEnteringHunanFilingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;

import javax.validation.Valid;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizEnteringHunanFiling")
@Api(value = "入湘报备接口", tags = "入湘报备接口")
public class BizEnteringHunanFilingController extends BaseController {

    private static final String ENTITY_NAME = "bizEnteringHunanFiling";

    @Autowired
    private IBizEnteringHunanFilingService bizEnteringHunanFilingService;


    /**
     * 新增数据
     *
     * @param bizEnteringHunanFilingDTO
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizEnteringHunanFiling.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizEnteringHunanFiling.add')")
    public ResultVO<BizEnteringHunanFilingDTO> create(@RequestBody @Valid BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO) {
        BizEnteringHunanFilingDTO result = bizEnteringHunanFilingService.create(bizEnteringHunanFilingDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据
     *
     * @param bizEnteringHunanFilingDTO
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizEnteringHunanFiling.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizEnteringHunanFiling.update')")
    public ResultVO update(@RequestBody BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO) {
        Assert.notNull(bizEnteringHunanFilingDTO.getId(), "general.IdNotNull");
        int count = bizEnteringHunanFilingService.update(bizEnteringHunanFilingDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizEnteringHunanFiling.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizEnteringHunanFiling.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizEnteringHunanFilingService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据
     *
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO = bizEnteringHunanFilingService.findOne(id);
        return ResultVO.success(bizEnteringHunanFilingDTO);
    }

    /**
     * 分页查询多条数据
     *
     * @param bizEnteringHunanFilingDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryBizEnteringHunanFilingPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizEnteringHunanFilingDTO>> queryBizEnteringHunanFilingPage(BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizEnteringHunanFilingService.queryListPage(bizEnteringHunanFilingDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     *
     * @param bizEnteringHunanFilingDTO
     * @return
     */
    @GetMapping("/queryBizEnteringHunanFiling")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizEnteringHunanFilingDTO>> queryBizEnteringHunanFiling(BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO) {
        List<BizEnteringHunanFilingDTO> list = bizEnteringHunanFilingService.queryList(bizEnteringHunanFilingDTO);
        return ResultVO.success(new ResResult<BizEnteringHunanFilingDTO>(list));
    }

    /**
     * 分页查询入湘收件箱数据
     *
     * @param bizEnteringHunanFilingDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryEnteringHunanFilingInboxPage")
    @ApiOperation(value = "分页查询入湘收件箱数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizQueryEnteringHunanFilingDTO>> queryEnteringHunanFilingInboxPage(BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizEnteringHunanFilingService.queryEnteringHunanFilingInboxPage(bizEnteringHunanFilingDTO, basePageForm));
    }

    /**
     * 签收
     *
     * @param rejectDTO
     * @return
     */
    @PostMapping("/reject")
    @ApiOperation(value = "签收", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "收件箱驳回")
    public ResultVO<Integer> reject(@RequestBody @Valid RejectDTO rejectDTO) {
        Integer inboxCount = bizEnteringHunanFilingService.reject(rejectDTO);
        if (inboxCount > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

}
