package com.ctsi.huaihua.service;

import com.ctsi.hndx.receive.entity.dto.RejectDTO;
import com.ctsi.huaihua.entity.dto.BizEnteringHunanFilingDTO;
import com.ctsi.huaihua.entity.BizEnteringHunanFiling;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.huaihua.entity.dto.BizQueryEnteringHunanFilingDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
public interface IBizEnteringHunanFilingService extends SysBaseServiceI<BizEnteringHunanFiling> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizEnteringHunanFilingDTO> queryListPage(BizEnteringHunanFilingDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizEnteringHunanFilingDTO> queryList(BizEnteringHunanFilingDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizEnteringHunanFilingDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizEnteringHunanFilingDTO create(BizEnteringHunanFilingDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizEnteringHunanFilingDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 是否存在
     * <p>
     * existByBizEnteringHunanFilingId
     *
     * @param code
     * @return
     */
    boolean existByBizEnteringHunanFilingId(Long code);

    /**
     * 批量新增
     * <p>
     * create batch
     *
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<BizEnteringHunanFilingDTO> dataList);


    /**
     * 分页查询入湘收件箱数据
     *
     * @param bizEnteringHunanFilingDTO
     * @param basePageForm
     * @return
     */
    PageResult<BizQueryEnteringHunanFilingDTO> queryEnteringHunanFilingInboxPage(BizEnteringHunanFilingDTO bizEnteringHunanFilingDTO, BasePageForm basePageForm);

    /**
     * 签收
     *
     * @param rejectDTO
     * @return
     */
    Integer reject(RejectDTO rejectDTO);

}
