package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizEnteringHunanFilingDTO对象", description = "")
public class BizEnteringHunanFilingDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 来宾单位id
     */
    @ApiModelProperty(value = "来宾单位id")
    private Long guestCompanyId;

    /**
     * 来宾单位名称
     */
    @ApiModelProperty(value = "来宾单位名称")
    private String guestCompanyName;

    /**
     * 来宾职务
     */
    @ApiModelProperty(value = "来宾职务")
    private String guestPost;

    /**
     * 来宾用户名称
     */
    @ApiModelProperty(value = "来宾用户名称")
    private String guestUserName;

    /**
     * 来宾用户id
     */
    @ApiModelProperty(value = "来宾用户id")
    private Long guestUserId;

    /**
     * 宾客级别
     */
    @ApiModelProperty(value = "宾客级别")
    private Integer userLevel;

    /**
     * 同行人数
     */
    @ApiModelProperty(value = "同行人数")
    private Integer peerPeopleCount;

    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    private String placeOfDeparture;

    /**
     * 在湘开始日期
     */
    @ApiModelProperty(value = "在湘开始日期")
    private LocalDate inHunanStartDate;

    /**
     * 在湘结束日期
     */
    @ApiModelProperty(value = "在湘结束日期")
    private LocalDate inHunanEndDate;

    /**
     * 在湘天数
     */
    @ApiModelProperty(value = "在湘天数")
    private Integer inHunanDays;

    /**
     * 工作任务
     */
    @ApiModelProperty(value = "工作任务")
    private Integer workTask;

    /**
     * 具体内容
     */
    @ApiModelProperty(value = "具体内容")
    private String concreteContent;

    /**
     * 联系电话，加密
     */
    @ApiModelProperty(value = "联系电话，加密")
    private String contactNumber;

    /**
     * 联系电话，分段加密
     */
    @ApiModelProperty(value = "联系电话，分段加密")
    private String contactNumberEncryption;

    /**
     * 签收状态 (0:未签收 1:签收)
     */
    @ApiModelProperty(value = "签收状态 (0:未签收 1:签收)")
    private Integer signInState;

    /**
     * 附件文件
     */
    @ApiModelProperty(value = "附件文件")
    private List<CscpEnclosureFile> cscpEnclosureFileList;

    /**
     * 处理单id
     */
    @ApiModelProperty(value = "处理单id")
    @NotNull(message = "处理单id不能为空")
    private Long processingSheetId;

    /**
     * 接收单位id
     */
    @ApiModelProperty(value = "接收单位id")
    private Long receiveCompanyId;

    /**
     * 创建人（填报人）
     */
    @ApiModelProperty(value = "创建人（填报人）")
    private String createName;

}
