package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务成果反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_feedback")
@ApiModel(value="BizTaskFeedback对象", description="任务成果反馈表")
public class BizTaskFeedback extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 任务成果描述
     */
    @ApiModelProperty(value = "任务成果描述")
    private String achievementDescription;

    /**
     * 完成情况的百分比
     */
    @ApiModelProperty(value = "完成情况的百分比")
    private String completion;

    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "审核人的id")
    private Long reviewerId;

    /**
     * 审核人的部门id
     */
    @ApiModelProperty(value = "审核人的部门id")
    private Long reviewerDepartmentId;

    /**
     * 审核人的单位id
     */
    @ApiModelProperty(value = "审核人的单位id")
    private Long reviewerCompanyId;

    /**
     * 0：未审核 1 审核通过
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过")
    private Integer hasReviewer;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewerTime;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;


    /**
     * 反馈人,操作人)部门名称
     */
    @ApiModelProperty(value = "(反馈人,操作人)部门名称")
    @TableField(fill = FieldFill.INSERT)
    private String departmentName;

    /**
     * 反馈人,操作人)手机号码
     */
    @ApiModelProperty(value = "(反馈人,操作人)手机号码")
    @TableField(fill = FieldFill.INSERT)
    private String mobile;

    /**
     * (反馈人,操作人)单位名称
     */
    @ApiModelProperty(value = "(反馈人,操作人)单位名称")
    @TableField(fill = FieldFill.INSERT)
    private String companyName;





}
