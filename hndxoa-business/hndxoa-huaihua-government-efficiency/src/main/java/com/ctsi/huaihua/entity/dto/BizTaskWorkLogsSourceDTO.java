package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 工作日志来源
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@Data
@ApiModel(value="BizTaskWorkLogsSourceDTO对象", description="工作日志来源")
public class BizTaskWorkLogsSourceDTO  {

    private static final long serialVersionUID = 1L;

    /**
     * 待办任务的id
     */
    @ApiModelProperty(value = "待办任务的id")
    private Long id;

    /**
     * 待办任务的value
     */
    @ApiModelProperty(value = "待办任务的value")
    private Long value;

    /**
     * 是否为待办任务，1：表示任务转入，0：表示新增日志
     */
    @ApiModelProperty(value = "是否为待办任务，1：表示任务转入，0：表示新增日志务")
    private Integer isTodoTask;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 姓名（创建人）
     */
    @ApiModelProperty(value = "姓名（创建人）")
    private String createName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String content;

    /**
     * 工作内容label
     */
    @ApiModelProperty(value = "工作内容label")
    private String label;

    /**
     * 工作类型
     */
    @ApiModelProperty(value = "工作类型")
    private String workType;

    /**
     * 工作来源
     */
    @ApiModelProperty(value = "工作来源")
    private String workSource;
}
