package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizTaskInformationDTO
 * @Author: json
 * @Description:
 * @Date: 2022/10/8 15:51
 * @Version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizTaskInformationDTO {

    @ApiModelProperty(value = "单位ID")
    private Long companyId;

    @ApiModelProperty(value = "单位名称")
    private String companyName;

    @ApiModelProperty(value = "登录时所使用的部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "登录时所使用的部门名称")
    private String departmentName;

    @ApiModelProperty(value = "负责人id")
    private Long id;

    @ApiModelProperty(value = "负责人名称")
    private String realName;

    @ApiModelProperty(value = "任务数")
    private int taskCount;

    @ApiModelProperty(value = "总分")
    private double totalScore;

    @ApiModelProperty(value = "平均分")
    private double average;
}
