package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 领导委托信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_score_scope")
@ApiModel(value="BizScoreScope对象", description="领导打分范围表")
public class BizScoreScope extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被打分的用户ID
     */
    @ApiModelProperty(value = "被打分的用户ID，用\",\"隔开")
    private String markUserIds;

    /**
     * 领导ID(日常打分领导表主键)
     */
    @ApiModelProperty(value = "领导ID")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    @ApiModelProperty(value = "前端用户选人范围: 1单位, 2租户")
    private Integer userAddScope;

    @ApiModelProperty(value = "最后时间")
    private LocalDateTime lastUpdateTime;

    @ApiModelProperty(value = "最后修改人ID")
    private Long lastUpdateBy;

    @ApiModelProperty(value = "最后修改人姓名")
    private String lastUpdateName;


}
