package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 子任务详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-13
 */
@Data
@ApiModel(value = "BizTaskDetailsDecomposeDTO对象", description = "子任务详情表")
public class BizTaskDetailsDecomposeDTO extends BaseDtoEntity {


    /**
     * 任务编号，中间：隔开，：之前为父编号，转办的以zb开头
     */
    @ApiModelProperty(value = "任务编号，中间：隔开，：之前为父编号，转办的以zb开头")
    private String taskNumber;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 任务标签，对应数据字典taskTag
     */
    @ApiModelProperty(value = "任务标签，对应数据字典taskTag")
    private String taskTag;

    /**
     * 任务紧急程度，对应数据字典taskDegreeUrgency
     */
    @ApiModelProperty(value = "任务紧急程度，对应数据字典taskDegreeUrgency")
    private String degreeUrgency;

    /**
     * 任务来源，对应数据字典taskSource
     */
    @ApiModelProperty(value = "任务来源，对应数据字典taskSource")
    private String taskSource;

    /**
     * 交办时间，子表decompose的创建时间
     */
    @ApiModelProperty(value = "交办时间")
    private LocalDateTime createTime;

    /**
     * 考核方式，对应数据字典ExamineMethod
     */
    @ApiModelProperty(value = "考核方式，对应数据字典ExamineMethod")
    private String examineMethod;

    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     * 任务概叙
     */
    @ApiModelProperty(value = "任务概叙")
    private String taskDescription;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;


    // 以下字段为：子任务字段

    /**
     * 子任务截止时间，yyyy-mm-dd
     */
    @ApiModelProperty(value = "子任务截止时间，yyyy-mm-dd")
    private LocalDate endDate;

    /**
     * 难度系数
     */
    @ApiModelProperty(value = "难度系数")
    private String degreeDifficulty;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;


    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String dutyPeople;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long dutyPeopleId;

    /**
     * 责任人的姓名
     */
    @ApiModelProperty(value = "责任人的姓名")
    private String dutyPeopleName;

    /**
     * 责任人的单位名称
     */
    @ApiModelProperty(value = "责任人的单位名称")
    private String dutyPeopleCompanyName;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;

    @ApiModelProperty(value = "反馈结果集合")
    private List<BizTaskFeedbackDTO> BizTaskFeedbackDTOList;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private LocalDateTime hasFinishTime;


    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人的职务")
    private String dutyPeoplePostName;

    /**
     * 接收时间，yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "接收时间，yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    private LocalDate startDate;

    /**
     * 是否是当前用户的任务 1:是 0:否
     */
    @ApiModelProperty(value = "是否是当前用户的任务 1:是 0:否")
    private int oneselfTask;

    @ApiModelProperty(value = "签收人的id")
    private Long signPeopleId;

    @ApiModelProperty(value = "签收人的姓名")
    private String signPeopleName;

    @ApiModelProperty(value = "签收人的电话")
    private String signPeoplePhone;

    @ApiModelProperty(value = "签收人单位名称")
    private String signPeopleCompanyName;


    @ApiModelProperty(value = "牵头人单位ID")
    private Long leadPeopleCompanyId;

    @ApiModelProperty(value = "责任人单位ID")
    private Long dutyPeopleCompanyId;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "短信提醒方式")
    private String smsReminder;
}
