package com.ctsi.huaihua.entity.dto;

import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname BizTaskScoreAddDetailDTO
 * @Description
 * @Date 2022/5/31/0031 15:03
 */
@Data
@ApiModel(value = "BizTaskScoreAddDetailDTO对象", description = "任务加分详情")
public class BizTaskScoreAddDetailDTO implements Serializable {

    @ApiModelProperty(value = "任务分解表对象")
    private BizTaskDecomposeDTO bizTaskDecomposeDTO;

    @ApiModelProperty(value = "任务加分对象")
    private BizTaskScoreAddDTO bizTaskScoreAddDTO;
}
