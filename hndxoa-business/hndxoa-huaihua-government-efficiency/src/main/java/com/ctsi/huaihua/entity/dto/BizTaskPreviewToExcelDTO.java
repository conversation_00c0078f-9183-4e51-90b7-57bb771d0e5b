package com.ctsi.huaihua.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname TaskPreviewDTO
 * @Description
 * @Date 2022/9/26/0026 16:38
 */

@Data
@HeadStyle(verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(verticalAlignment = VerticalAlignmentEnum.CENTER, wrapped = BooleanEnum.TRUE)
@ContentRowHeight(45)
@HeadRowHeight(38)
@ColumnWidth(18)
public class BizTaskPreviewToExcelDTO implements Serializable {

    private static final long serialVersionUID = -4256377695600588193L;

    @ExcelProperty({"市政府政务效能-任务一览表", "序号"})
    @ApiModelProperty(value = "序号")
    private Integer taskNum;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    @ExcelProperty({"市政府政务效能-任务一览表", "任务名称"})
    private String title;

    @ApiModelProperty(value = "督查任务等级对应biz_task_level")
    @ExcelProperty({"市政府政务效能-任务一览表", "重要性"})
    private String inspectorTaskLevel;

    @ApiModelProperty(value = "督查任务具体的事项对应biz_task_levell")
    @ExcelProperty({"市政府政务效能-任务一览表", "交办事项"})
    private String inspectorItems;

    @ApiModelProperty(value = "任务概述")
    @ExcelProperty({"市政府政务效能-任务一览表", "任务概述"})
    private String taskDescription;

    /**
     * 主任务 截止时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "主任务截止时间，yyyy-MM-dd")
    @ExcelProperty({"市政府政务效能-任务一览表", "截止时间"})
    private String supDueTime;


    @ApiModelProperty(value = "任务内容")
    @ExcelProperty({"市政府政务效能-任务一览表", "分解任务"})
    private String content;


    @ApiModelProperty(value = "分解任务责任人")
    @ExcelProperty({"市政府政务效能-任务一览表", "责任人"})
    private String dutyPeopleInfo;

    /**
     * 分解任务 开始时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "分解任务开始时间，yyyy-MM-dd")
    @ExcelProperty({"市政府政务效能-任务一览表", "开始时间"})
    private String startDate;

    /**
     * 分解任务 截止时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "分解任务截止时间，yyyy-MM-dd")
    @ExcelProperty({"市政府政务效能-任务一览表", "结束时间"})
    private String endDate;


    @ApiModelProperty(value = "分解表办理状态：0 未办结 1 已办结")
    @ExcelProperty({"市政府政务效能-任务一览表", "办理状态"})
    private String hasFinish;

    @ApiModelProperty(value = "成果反馈")
    @ExcelProperty({"市政府政务效能-任务一览表", "成果反馈"})
    private String feedbackSign;

    @ApiModelProperty(value = "成果反馈")
    @ExcelProperty({"市政府政务效能-任务一览表", "牵头人"})
    private String leadPeopleName;


}
