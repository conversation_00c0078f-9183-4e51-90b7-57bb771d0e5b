package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_level")
@ApiModel(value="BizTaskLevel对象", description="")
public class BizTaskLevel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 上级类型id，顶层为0
     */
    @ApiModelProperty(value = "上级类型id，顶层为0")
    private Long parentId;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderBy;

//    /**
//     * 系数
//     */
//    @ApiModelProperty(value = "系数")
//    private Integer factor;
//
//    /**
//     * 权重
//     */
//    @ApiModelProperty(value = "权重")
//    private Double weight;


}
