package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 子任务办结的得分
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskDecomposeScoreDTO对象", description="子任务办结的得分")
public class BizTaskDecomposeScoreDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应该得分
     */
    @ApiModelProperty(value = "应该得分")
    private Double shouldScore;

    /**
     * 实际得分
     */
    @ApiModelProperty(value = "实际得分")
    private Double actualScore;

    /**
     * 调分原因
     */
    @ApiModelProperty(value = "调分原因")
    private String adjustableReason;

    /**
     * 调分原因
     */
    @ApiModelProperty(value = "1表示办结，0表示未办结")
    private Integer hasFinish;

}
