package com.ctsi.huaihua.entity.dto;

import com.ctsi.huaihua.entity.BizTaskScoreAdd;
import com.ctsi.huaihua.entity.BizTaskScoreSub;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BizTaskScoreDetailDTO implements Serializable {

    @ApiModelProperty(value = "加分任务对象")
    private List<BizTaskScoreAdd> bizTaskScoreAddList;

    @ApiModelProperty(value = "减分任务对象")
    private List<BizTaskScoreSubDTO> bizTaskScoreSubList;

}
