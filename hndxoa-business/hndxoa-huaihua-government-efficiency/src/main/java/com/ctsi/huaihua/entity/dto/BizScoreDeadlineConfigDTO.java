package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * <p>
 * 日常工作打分截止时间设置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizScoreDeadlineConfigDTO对象", description = "日常工作打分截止时间设置")
public class BizScoreDeadlineConfigDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 日常工作打分截止时间(日)
     */
    @ApiModelProperty(value = "日常工作打分截止时间(日)1~31日之间")
    @Range(min = 1, max = 31, message = "日期范围仅限设置在1~31日之间")
    private Integer day;

    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间")
    private LocalDateTime updateTime;

    /**
     * 最后修改人
     */
    @ApiModelProperty(value = "最后修改人")
    private String updateName;

}
