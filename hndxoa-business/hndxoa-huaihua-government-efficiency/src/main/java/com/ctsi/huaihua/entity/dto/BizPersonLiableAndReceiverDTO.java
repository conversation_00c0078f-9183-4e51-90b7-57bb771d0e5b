package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizPersonLiableAndReceiverDTO
 * @Author: json
 * @Description: 查询责任人和接收人
 * @Date: 2022/3/15 14:26
 * @Version: 1.0
 */
@Data
@ApiModel(value = "BizPersonLiableAndReceiverDTO", description = "查询责任人和接收人")
public class BizPersonLiableAndReceiverDTO {

    @ApiModelProperty(value = "群组名称")
    private String groupName;

    @ApiModelProperty(value = "群组对应的责任人")
    private List<BizTaskDecomposeDTO> bizTaskDecomposeList;

}
