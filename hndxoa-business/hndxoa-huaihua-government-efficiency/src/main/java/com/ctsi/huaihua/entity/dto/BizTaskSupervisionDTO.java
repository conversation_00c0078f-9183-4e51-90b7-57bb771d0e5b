package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务督查的主表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizTaskSupervisionDTO对象", description = "任务督查的主表")
public class BizTaskSupervisionDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人")
    private String createName;


    @ApiModelProperty(value = "创建时间(交办时间)")
    private LocalDateTime createTime;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 任务类型，对应数据字典taskType
     */
    @ApiModelProperty(value = "任务类型，对应数据字典taskType")
    private String taskType;

    /**
     * 任务紧急程度，对应数据字典taskDegreeUrgency
     */
    @ApiModelProperty(value = "任务紧急程度，对应数据字典taskDegreeUrgency")
    private String degreeUrgency;

    /**
     * 任务来源，对应数据字典taskSource
     */
    @ApiModelProperty(value = "任务来源，对应数据字典taskSource")
    private String taskSource;

    /**
     * 任务标签，对应数据字典taskTag
     */
    @ApiModelProperty(value = "任务标签，对应数据字典taskTag")
    private String taskTag;

    /**
     * 考核方式，对应数据字典ExamineMethod
     */
    @ApiModelProperty(value = "考核方式，对应数据字典ExamineMethod")
    private String examineMethod;

    /**
     * 截止时间，yyyy-mm-dd
     */
    @ApiModelProperty(value = "主任务截止时间，yyyy-mm-dd")
    private LocalDate dueTime;

    /**
     * 任务概叙
     */
    @ApiModelProperty(value = "任务概叙")
    private String taskDescription;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;

    /**
     * 0 未发布暂存，1：发布  2：撤回
     */
    @ApiModelProperty(value = "0 未发布暂存 1：发布  2：撤回 3:待审批 4:驳回")
    private Integer hasPublish;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;

    @ApiModelProperty(value = "主任务创建人部门ID")
    private Long departmentId;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "拟稿人部门名称")
    private String departmentName;

    /**
     * 拟稿人手机号码
     */
    @ApiModelProperty(value = "拟稿人手机号码")
    private String mobile;

    /**
     * 拟稿人单位名称
     */
    @ApiModelProperty(value = "拟稿人单位名称")
    private String companyName;

    /**
     * 任务编号，中间：隔开，：之前为父编号，转办的以zb开头
     */
    @ApiModelProperty(value = "任务编号，中间：隔开，：之前为父编号，转办的以zb开头")
    private String taskNumber;

    /**
     * 任务层级，转办了多少级
     */
    @ApiModelProperty(value = "任务层级，转办了多少级")
    private Integer taskLevel;

    /**
     * 任务新增的方式0表示新增 1表示转办
     */
    @ApiModelProperty(value = "任务新增的方式0表示新增 1表示转办")
    private Integer taskAddType;

    /**
     * 任务来自转办，转办的id
     */
    @ApiModelProperty(value = "任务来自转办，转办的id")
    private Long taskTransferId;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String dutyPeople;

    /**
     * 任务转办过来后的分解的任务id
     */
    @ApiModelProperty(value = "任务转办过来后的分解的任务id")
    private Long taskTransferDecomposeId;

    @ApiModelProperty(value = "表单项的id")
    private Long formId;

    @ApiModelProperty(value = "查询开始时间")
    private String startTime;

    @ApiModelProperty(value = "查询结束时间")
    private String endTime;

    @ApiModelProperty(value = "任务分解表集合")
    private List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList;

    @ApiModelProperty(value = "主键ID集合")
    private List<Long> BizTaskSupervisionIdList;

    @ApiModelProperty(value = "申请延期的主键ID")
    private Long taskDelayId;

    @ApiModelProperty(value = "申请延期的主键ID集合")
    private List<Long> taskDelayIdList;

    @ApiModelProperty(value = "申请延期 0：未阅 1 已阅")
    private Integer taskDelayHasRead;

    @ApiModelProperty(value = "申请单位(分解表责任人的单位名称)")
    private String dutyPeopleCompanyName;

    @ApiModelProperty(value = "分解任务结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "申请延期之前的截至时间")
    private LocalDate delayBeforeEndDate;

    @ApiModelProperty(value = "申请延期的截至时间(申请延期)")
    private LocalDate delayEndDate;

    @ApiModelProperty(value = "申请延期表 0：未审核 1 审核通过 2 通过并调整延期时间")
    private Integer hasReviewer;

    @ApiModelProperty(value = "申请延期表集合 0：未审核 1 审核通过 2 通过并调整延期时间")
    private List<Integer> hasReviewerList;

    @ApiModelProperty(value = "申请延期接收人ID")
    private Long delayReviewerId;

    @ApiModelProperty(value = "申请延期创建人ID")
    private Long delayCreateId;

    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecomposeId;

    @ApiModelProperty(value = "分解表责任人的姓名")
    private String dutyPeopleName;

    /**
     * 对于最新反馈的成果标记 0：没有 , 1 有
     * 对于最新反馈的成果，页面列表以小红点标识最新数据，用户点击成果审核后恢复默认样式。
     */
    @ApiModelProperty(value = " 对于最新反馈的成果，页面列表以小红点标识最新数据,用户点击成果审核后恢复默认样式  0：没有标识, 1 最新反馈成果,有小红点标识")
    private Integer newSupFeedbackSign;


    @ApiModelProperty(value = " 成果收件箱,主任务列表是否刷新标识; 1 刷新主任务列表,0 不需要刷新主任务列表")
    private String refreshFlag;


    @ApiModelProperty(value = "单位对应的分解任务  责任人公司id==分解任务列表")
    private Map<Long, List<BizTaskDecomposeDTO>> bizTaskDecomposeDTOListMap;

    @ApiModelProperty(value = "延期申请表 手动调整的延期截至时间")
    private LocalDate manualDelayEndDate;

    @ApiModelProperty(value = "当前页数")
    private Integer currentPage;

    @ApiModelProperty(value = "每页请求条数")
    private Integer pageSize;

    /**
     * 移动端搜索key
     */
    @ApiModelProperty(value = "移动端搜索key")
    private String searchKey;

    /**
     * 是否有反馈记录 0：没有 , 1 有
     */
    @ApiModelProperty(value = " 是否有反馈记录 0：没有, 1 有")
    private Integer supHasFeedback;


    @ApiModelProperty(value = "督查任务等级对应biz_task_level")
    private String inspectorTaskLevel;


    @ApiModelProperty(value = "督查任务具体的事项对应biz_task_levell")
    private String inspectorItems;

    @ApiModelProperty(value = "主任务存在加分申请待审标识(审核人专用)")
    private Integer auditNotReviewedFlag;

    @ApiModelProperty(value = "主任务存在加分申请已审标识(审核人专用)")
    private Integer auditHasReviewedFlag;

    @ApiModelProperty(value = "主任务对应的加分申请是否审核集合")
    private List<Integer> scoreReviewedStatusList;

    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "难易性，对应数据数据字段task_difficulty")
    private String difficulty;

    @ApiModelProperty(value = "牵头人名称")
    private String leadPeopleName;

    @ApiModelProperty(value = "牵头人id")
    private Long leadPeopleId;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;

    @ApiModelProperty(value = "驳回时间")
    private LocalDateTime difficultyTime;

    @ApiModelProperty(value = "审核人名称")
    private String approvalName;

    @ApiModelProperty(value = "审核人id")
    private Long approvalId;

    @ApiModelProperty(value = "驳回名称")
    private String difficultyName;

    @ApiModelProperty(value = "驳回id")
    private Long difficultyId;

    @ApiModelProperty(value = "重要性")
    private BizTaskLevelDTO inspectorTaskLevelDTO;

    @ApiModelProperty(value = "交办事项")
    private BizTaskLevelDTO inspectorItemsDTO;

    @ApiModelProperty(value = "单位id")
    private Long companyId;


    @ApiModelProperty(value = "任务分值")
    private String taskScore;

    @ApiModelProperty(value = "任务的得分")
    private Double actualScore;

    @ApiModelProperty(value = "分管领导ID")
    private String branchLeaderId;

    @ApiModelProperty(value = "分管领导姓名")
    private String branchLeaderName;

    @ApiModelProperty(value = "分管领导姓名的表单json字符串")
    private String branchLeaderNameJson;

    @ApiModelProperty(value = "牵头人单位ID")
    private Long leadPeopleCompanyId;

    @ApiModelProperty(value = "牵头人单位名称")
    private String leadPeopleCompanyName;

    @ApiModelProperty(value = "责任人单位ID")
    private Long dutyPeopleCompanyId;

}
