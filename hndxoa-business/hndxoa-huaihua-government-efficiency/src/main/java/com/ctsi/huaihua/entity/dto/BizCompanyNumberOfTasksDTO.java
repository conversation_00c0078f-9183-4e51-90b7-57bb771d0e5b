package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: CompanyNumberOfTasksDTO
 * @Author: json
 * @Description:
 * @Date: 2022/10/10 17:40
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizCompanyNumberOfTasksDTO {

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "单位id")
    private Long companyId;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "总数")
    private Integer count;
}
