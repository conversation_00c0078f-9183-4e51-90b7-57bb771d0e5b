package com.ctsi.huaihua.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 封装后的承办管理任务查看表
 * 反馈任务列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
@Data
@ApiModel(value = "BizHomeTaskPackagingDTO对象", description = "封装过后的任务属性")
public class BizHomeTaskPackagingDTO {

    @ApiModelProperty(value = "任务主表的id")
    private Long taskSupervisionId;

    @ApiModelProperty(value = "行标题")
    private String rowTitle;

    @ApiModelProperty(value = "行数据字段名")
    private String rowKey;

    //发布任务就返回taskProgress，承办任务就返回no-workflow
    @ApiModelProperty(value = "详情地址")
    private String detailPath;


    @ApiModelProperty(value = "承办,反馈任务列表")
    private BizHomeTaskDTO data;

}
