package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务延期表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskDelayDTO对象", description="任务延期表")
public class BizTaskDelayDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 申请延期的内容
     */
    @ApiModelProperty(value = "申请延期的内容")
    private String delayContent;

    /**
     * 申请延期的理由
     */
    @ApiModelProperty(value = "申请延期的理由")
    private String delayReason;

    /**
     * 申请延期的截至时间
     */
    @ApiModelProperty(value = "申请延期的截至时间")
    private LocalDate delayEndDate;

    /**
     * 申请延期之前的内容
     */
    @ApiModelProperty(value = "申请延期之前的内容")
    private String delayBeforeContent;

    /**
     * 申请延期之前的截至时间
     */
    @ApiModelProperty(value = "申请延期之前的截至时间")
    private LocalDate delayBeforeEndDate;


    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "接收审核人的id")
    private String reviewerId;

    /**
     * 审核人的部门id
     */
    @ApiModelProperty(value = "接收审核人的部门id")
    private Long reviewerDepartmentId;

    /**
     * 审核人的单位id
     */
    @ApiModelProperty(value = "接收审核人的单位id")
    private Long reviewerCompanyId;

    /**
     * 0：未审核 1 审核通过
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 通过并调整延期时间")
    private Integer hasReviewer;

    @ApiModelProperty(value = "申请延期表集合 0：未审核 1 审核通过 2 通过并调整延期时间")
    private List<Integer> hasReviewerList;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    @ApiModelProperty(value = "审核人的id")
    private Long auditId;

    /**
     * 审核人的单位id
     */
    @ApiModelProperty(value = "审核人的姓名")
    private String auditName;


    /**
     * 审核人的单位id
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "0：未阅 1 已阅")
    private Integer hasRead;


    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "手动调整的延期截至时间")
    private LocalDate manualDelayEndDate;

    @ApiModelProperty(value = "延期申请主键ID集合")
    private List<Long> idList;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;
}
