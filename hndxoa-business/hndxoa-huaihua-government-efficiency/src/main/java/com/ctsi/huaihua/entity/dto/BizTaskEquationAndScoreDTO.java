package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="BizTaskEquationAndScoreDTO对象", description="任务公式与计算分数对象")
public class BizTaskEquationAndScoreDTO {

    @ApiModelProperty(value = "难度系数公式")
    private String equation;

    @ApiModelProperty(value = "难度系数分")
    private Double score;
}
