package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizTaskStatisticsDTO
 * @Author: json
 * @Description:
 * @Date: 2022/11/21 10:24
 * @Version: 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizTaskStatisticsDTO {

    /**
     * 主任务发布状态 0 未发布暂存 1：发布  2：撤回 3:待审批 4:驳回
     */
    @ApiModelProperty(value = "主任务发布状态 0 未发布暂存 1：发布  2：撤回 3:待审批 4:驳回")
    private Integer hasPublish;

    /**
     * 主任务办结状态 0：未办结 1 办结
     */
    @ApiModelProperty(value = "主任务发布状态 0 未发布暂存 1：发布  2：撤回 3:待审批 4:驳回")
    private Integer supHasFinish;

    /**
     * 子任务办结状态 0：未办结 1 办结
     */
    @ApiModelProperty(value = "子任务办结状态 0：未办结 1 办结")
    private Integer decHasFinish;

    /**
     * 统计人id集合
     */
    @ApiModelProperty(value = "统计人id集合")
    private Set<Long> userIds;

    /**
     * 统计子任务单位id集合
     */
    @ApiModelProperty(value = "统计子任务单位id集合")
    private Set<Long> userCompanyId;

    /**
     * 统计主任务单位id集合
     */
    @ApiModelProperty(value = "统计主任务单位id集合")
    private Set<Long> subTaskCompanyIds;

    /**
     * 过滤单位id集合
     */
    @ApiModelProperty(value = "过滤单位id集合")
    private Set<Long> filterCompanyId;

    /**
     * 任务单位id集合
     */
    @ApiModelProperty(value = "任务单位id集合")
    private Set<Long> taskCompanyIds;

    /**
     * 主任务id集合
     */
    @ApiModelProperty(value = "主任务id集合")
    private Set<Long> subTaskIds;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    private LocalDateTime taskStartTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    private LocalDateTime taskEndTime;

    /**
     * 主任务办结开始时间
     */
    @ApiModelProperty(value = "主任务办结开始时间")
    private LocalDateTime hasFinishStartTime;

    /**
     * 主任务办结结束时间
     */
    @ApiModelProperty(value = "主任务办结结束时间")
    private LocalDateTime hasFinishEndTime;

    /**
     * 是否已签收
     */
    @ApiModelProperty(value = "是否已签收")
    private Integer hasSign;

    /**
     * 分组字段（需要该字段分组时填充属性名）
     */
    @ApiModelProperty(value = "分组字段（需要该字段分组时填充属性名）")
    private List<String> groupingFields;

    /**
     * 动态放回字段
     */
    @ApiModelProperty(value = "动态放回字段")
    private List<String> returnFields;
}
