package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import java.time.LocalDateTime;

/**
 * <p>
 * 工作日志 前端查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskWorkLogsDTO对象", description="工作日志 前端查询参数")
public class BizTaskWorkLogsPageDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 是否为待办任务，1：表示任务转入，0：表示新增日志
     */
    @ApiModelProperty(value = "是否为待办任务，1：表示任务转入，0：表示新增日志")
    private Integer isTodoTask;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 姓名（创建人）
     */
    @ApiModelProperty(value = "姓名（创建人）")
    private String createName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd")
    private String endTime;

    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    @DecimalMin(message="时长不能小于0", value = "0.0")
    private Float costTime;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String content;

    /**
     * 工作类型
     */
    @ApiModelProperty(value = "工作类型")
    private String workType;

    /**
     * 工作来源
     */
    @ApiModelProperty(value = "工作来源")
    private String workSource;

    /**
     * 附件：1表示有附件，0表示无附件
     */
    @ApiModelProperty(value = "附件：1表示有附件，0表示无附件")
    private Integer annex;


    @ApiModelProperty(value = "表单项的id")
    private Long formId;
}
