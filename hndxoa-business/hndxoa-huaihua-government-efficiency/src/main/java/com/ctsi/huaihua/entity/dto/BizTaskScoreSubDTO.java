package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务减分表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizTaskScoreSubDTO对象", description = "任务减分表")
public class BizTaskScoreSubDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 申请减分的内容
     */
    @ApiModelProperty(value = "申请减分的内容")
    private String soreSubContent;

    /**
     * 申请减分的理由
     */
    @ApiModelProperty(value = "申请减分的理由")
    private String soreSubReason;

    /**
     * 申请减分的分数
     */
    @ApiModelProperty(value = "申请减分的分数")
    private Double sores;

    /**
     * 减回理由
     */
    @ApiModelProperty(value = "减回理由")
    private String refuseReason;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id集合")
    private List<Long> bizTaskDecompose;

    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecomposeId;

    /**
     * 减分时间
     */
    @ApiModelProperty(value = "减分时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否预览(0:未预览 1:预览)")
    private Integer preview;

    @ApiModelProperty(value = "申请人")
    private String createName;

    @ApiModelProperty(value = "附件id")
    private Long enclosureFileId;

}
