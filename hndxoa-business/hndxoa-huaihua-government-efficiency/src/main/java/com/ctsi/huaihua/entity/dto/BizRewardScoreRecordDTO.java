package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 奖分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizRewardScoreRecordDTO对象", description="奖分记录表")
public class BizRewardScoreRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被奖励加分的用户ID
     */
    @ApiModelProperty(value = "被奖励加分的用户ID")
    private Long userId;

    /**
     * 被奖励加分的用户姓名
     */
    @ApiModelProperty(value = "被奖励加分的用户姓名")
    private String userName;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 加分年份
     */
    @ApiModelProperty(value = "加分年份")
    private Integer scoreYear;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 加分原因
     */
    @ApiModelProperty(value = "加分原因")
    private String scoreReason;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String reviewerName;

    /**
     * 审核人部门ID
     */
    @ApiModelProperty(value = "审核人部门ID")
    private Long reviewerDepartmentId;

    /**
     * 审核人单位ID
     */
    @ApiModelProperty(value = "审核人单位ID")
    private Long reviewerCompanyId;

    /**
     * 审核人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "审核人是否未读0: 未查看  1:已查看")
    private Integer reviewerHasRead;

    /**
     * 0：未审核 1 审核通过 2 驳回
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewerTime;

    /**
     * 驳回理由
     */
    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;


    @ApiModelProperty(value = "被奖励人部门名称")
    private String rewardDepartmentName;

    @ApiModelProperty(value = "被奖励人单位名称")
    private String rewardCompanyName;

    @ApiModelProperty(value = "被奖励人职务")
    private String post;

    /**
     * 被奖励人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "被奖励人是否未读0: 未查看  1:已查看")
    private Integer rewardHasRead;

    /**
     * 申请加分来源：1市直单位 2督查室
     */
    @ApiModelProperty(value = "申请加分来源：1市直单位 2督查室")
    private Integer rewardResource;

    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "奖励分申请时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "用户奖励分关系数据集合")
    private List<BizUserRewardScoreRelDTO> userRewardScoreRelList;

    @ApiModelProperty(value = "是否审核集合")
    private List<Integer> hasReviewerList;

    @ApiModelProperty(value = "加分原因集合")
    private List<String> scoreReasonList;

    @ApiModelProperty(value = "创建人单位名称")
    private String createCompanyName;

    @ApiModelProperty(value = "当前页数")
    private Integer currentPage;

    @ApiModelProperty(value = "每页请求条数")
    private Integer pageSize;

}
