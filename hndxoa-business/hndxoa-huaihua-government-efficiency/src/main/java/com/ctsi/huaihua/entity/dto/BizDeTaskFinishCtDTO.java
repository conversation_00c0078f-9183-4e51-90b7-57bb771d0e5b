package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *  排名前5的接收单位 责任人姓名 职务
 * </p>
 *  <AUTHOR>
 */
@Data
@ApiModel(value = "BizDeTaskFinishCtDTO", description = "排名前5办结责任人")
public class BizDeTaskFinishCtDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 责任人的id
     */
    @ApiModelProperty(value = "责任人的id")
    private Long dutyPeopleId;

    /**
     * 责任人的部门id
     */
    @ApiModelProperty(value = "责任人的部门id")
    private Long dutyPeopleDepartmentId;



    /**
     * 责任人的单位id
     */
    @ApiModelProperty(value = "责任人的单位id")
    private Long dutyPeopleCompanyId;


    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人的职务")
    private String dutyPeoplePostName;

    /**
     * 责任人的单位名称
     */
    @ApiModelProperty(value = "责任人的单位名称")
    private String dutyPeopleCompanyName;

    /**
     * 责任人的部门名称
     */
    @ApiModelProperty(value = "责任人的部门名称")
    private String dutyPeopleDepartmentName;

    /**
     * 责任人的姓名
     */
    @ApiModelProperty(value = "责任人的姓名")
    private String dutyPeopleName;


    /**
     * 分解任务办结数
     */
    @ApiModelProperty(value = "分解任务办结数")
    private Integer finishCount;



}
