package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizScoreByIdDTO
 * @Author: json
 * @Description:
 * @Date: 2022/9/23 15:11
 * @Version: 1.0
 */
@Data
public class BizScoreByIdDTO {

    @ApiModelProperty(value = "分数")
    private Double score;

    @ApiModelProperty("是否是牵头单位 0:否 1:是")
    private Integer whetherLead;
}
