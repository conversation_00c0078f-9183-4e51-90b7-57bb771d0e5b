package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务难度系数表对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskCoefficientDTO对象", description="任务难度系数表对象")
public class BizTaskCoefficientDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 系数id(任务级别表id或数据字典主表id)
     */
    @ApiModelProperty(value = "系数id(任务级别表id或数据字典主表id)")
    private Long coefficientId;

    /**
     * 字典副表code值
     */
    @ApiModelProperty(value = "字典副表code值")
    private String dictCode;

    /**
     * 属性名
     */
    @ApiModelProperty(value = "属性名")
    private String attributeName;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Double weight;

    /**
     * 系数
     */
    @ApiModelProperty(value = "系数")
    private Integer factor;

    /**
     * 类别 0:重要性 1:紧急性 2:难易性
     */
    @ApiModelProperty(value = "类别 0:重要性 1:紧急性 2:难易性")
    private Integer type;


}
