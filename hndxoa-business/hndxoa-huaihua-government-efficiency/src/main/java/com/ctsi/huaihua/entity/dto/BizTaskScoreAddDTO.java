package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务加分表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskScoreAddDTO对象", description="任务加分表")
public class BizTaskScoreAddDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 申请加分的内容
     */
    @ApiModelProperty(value = "申请加分的内容")
    private String scoreAddContent;

    /**
     * 申请加分的理由
     */
    @ApiModelProperty(value = "申请加分的理由")
    private String scoreAddReason;

    /**
     * 申请加分的分数
     */
    @ApiModelProperty(value = "申请加分的分数")
    private Double score;

    /**
     * 接收审核人的的id
     */
    @ApiModelProperty(value = "接收审核人的的id")
    private Long reviewerId;

    /**
     * 接收审核人的部门id
     */
    @ApiModelProperty(value = "接收审核人的部门id")
    private Long reviewerDepartmentId;

    /**
     * 接收审核人的单位id
     */
    @ApiModelProperty(value = "接收审核人的单位id")
    private Long reviewerCompanyId;

    /**
     * 0：未审核 1 审核通过 2 通过并调整延期时间
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    @ApiModelProperty(value = "申请加分表集合 0：未审核 1 审核通过 2 驳回")
    private List<Integer> hasReviewerList;

    /**
     * 申请人是否查看 0：未查看 1:已查看
     */
    @ApiModelProperty(value = "0：未查看 1:已查看")
    private Integer hasRead;
    /**
     * 审核人是否查看 0：未查看 1:已查看
     */
    @ApiModelProperty(value = "0：未查看 1:已查看")
    private Integer auditHasRead;

    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "审核人的id")
    private Long auditId;

    /**
     * 审核人的姓名
     */
    @ApiModelProperty(value = "审核人的姓名")
    private String auditName;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务来源
     */
    @ApiModelProperty(value = "任务来源")
    private String taskSource;

    /**
     * 考核方式
     */
    @ApiModelProperty(value = "任务类型")
    private String taskType;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "交办时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "加分申请创建人id")
    private Long createBy;

    @ApiModelProperty(value = "加分申请创建人名称")
    private String createName;

    @ApiModelProperty(value = "加分申请主键ID集合")
    private List<Long> idList;


}
