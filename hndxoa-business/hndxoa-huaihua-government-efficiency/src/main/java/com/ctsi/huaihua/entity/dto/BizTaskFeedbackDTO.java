package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务成果反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskFeedbackDTO对象", description="任务成果反馈表")
public class BizTaskFeedbackDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 任务成果描述
     */
    @ApiModelProperty(value = "任务成果描述")
    private String achievementDescription;

    /**
     * 完成情况的百分比
     */
    @ApiModelProperty(value = "完成情况的百分比")
    private String completion;

    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "审核人的id")
    private Long reviewerId;

    /**
     * 审核人的部门id
     */
    @ApiModelProperty(value = "审核人的部门id")
    private Long reviewerDepartmentId;

    /**
     * 审核人的单位id
     */
    @ApiModelProperty(value = "审核人的单位id")
    private Long reviewerCompanyId;
    /**
     * 审核人的单位
     */
    @ApiModelProperty(value = "审核人的单位")
    private Long reviewerCompanyName;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Integer reviewerTime;


    /**
     * 0：未审核 1 审核通过
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过")
    private Integer hasReviewer;



    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;

    /**
     * 反馈时间，yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "反馈时间，yyyy-MM-dd HH:mm")
    private LocalDateTime createTime;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "(反馈人,操作人)部门名称")
    private String departmentName;

    /**
     * 拟稿人手机号码
     */
    @ApiModelProperty(value = "(反馈人,操作人)手机号码")
    private String mobile;

    /**
     * 拟稿人单位名称
     */
    @ApiModelProperty(value = "(反馈人,操作人)单位名称")
    private String companyName;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;

    @ApiModelProperty(value = "(反馈人,操作人)姓名")
    private String createName;

}
