package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 通用承办管理任务查看表
 * 反馈任务列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
@Data
@ApiModel(value = "BizTaskDTO对象", description = "通用承办管理任务表")
public class BizHomeTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "任务主表的id")
    private Long   taskSupervisionId;


    @ApiModelProperty(value = "任务分解表的id")
    private Long   taskDecomposeId;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     * 截止时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "截止时间，yyyy-MM-dd")
    private LocalDate dueTime;

    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型,1正常，2逾期 3 临期")
    private String queryType;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long dutyPeopleId;


    @ApiModelProperty(value = "任务表单id")
    private Long formId;


}
