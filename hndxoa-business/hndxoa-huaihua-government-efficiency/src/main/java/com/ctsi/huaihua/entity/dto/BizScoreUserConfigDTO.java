package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 免考核人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizScoreUserConfigDTO对象", description="免考核人员表")
public class BizScoreUserConfigDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 免考核用户部门
     */
    @ApiModelProperty(value = "免考核用户部门")
    private String departmentName;

    /**
     * 是否需要考核，0：不考核 1：考核
     */
    @ApiModelProperty(value = "是否需要考核，0：不考核 1：考核")
    private Integer hasChecked;


}
