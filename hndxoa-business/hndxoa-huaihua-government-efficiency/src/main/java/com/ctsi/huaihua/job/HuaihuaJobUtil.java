package com.ctsi.huaihua.job;


import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.leadershipEntrustment.entity.dto.BizLeadershipEntrustmentDTO;
import com.ctsi.hndx.receive.service.IBizLeaderLiaisonService;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.huaihua.constant.TaskStatus;
import com.ctsi.huaihua.entity.BizTaskDecompose;
import com.ctsi.huaihua.entity.BizTaskFeedback;
import com.ctsi.huaihua.entity.dto.BizTaskDTO;
import com.ctsi.huaihua.mapper.BizTaskDecomposeMapper;
import com.ctsi.huaihua.service.IBizTaskFeedbackService;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.sms.syssms.entity.dto.SmsSendInfoDto;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.workrestdate.service.ITSysWorkRestDateService;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;


/**
 * @description
 * @author: TanJie
 * @create: 2022-04-06
 **/

public class HuaihuaJobUtil {



    public static  ITSysWorkRestDateService tSysWorkRestDateService = SpringUtil.getBean(ITSysWorkRestDateService.class);
    public static  BizTaskDecomposeMapper bizTaskDecomposeMapper = SpringUtil.getBean(BizTaskDecomposeMapper.class);
    public static  IBizTaskFeedbackService bizTaskFeedbackService = SpringUtil.getBean(IBizTaskFeedbackService.class);
    public static  CscpUserService cscpUserService = SpringUtil.getBean(CscpUserService.class);
    public static  IBizLeaderLiaisonService bizLeaderLiaisonService =
            SpringUtil.getBean(IBizLeaderLiaisonService.class);


    /**
     * 间隔2天
     */
    public static Integer INTERVAL_2DAYS=2;
    /**
     * 间隔3天
     */
    public static Integer INTERVAL_3DAYS=3;


    public static void autoTaskRemind(LocalDate now) {
        /*
         开始时间 < 现在
         结束时间 > 现在
         1 需要判断今天 是 周4
         2 需要判断今天 是不是月底
        */
        Boolean workDay = tSysWorkRestDateService.isWorkDay(now);
        // 工作日
        if (workDay) {
            Week week = LocalDateTimeUtil.dayOfWeek(now);
            // 1 周四
            if (week == Week.THURSDAY) {
                sendMss(now , TaskStatus.AUTO_TASK_REMIND_WEEK);
            }
            // 周三发短信 则周四是休息日
            if(week == Week.WEDNESDAY ){
                // 并且 明天 周四是 休息日
                Boolean workDayTwo = tSysWorkRestDateService.isWorkDay(now.plusDays(1));
                // 非工作日
                if(!workDayTwo){
                    sendMss(now , TaskStatus.AUTO_TASK_REMIND_WEEK);
                }
            }
            LocalDate lastDayOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());
            LocalDate beforeWorkDayLastDayOfMonth = getBeforeWorkDay(lastDayOfMonth);
            // 2 今天是 月底的前一个工作日
            if (now.getDayOfMonth() == beforeWorkDayLastDayOfMonth.getDayOfMonth()) {
                sendMss(now,TaskStatus.AUTO_TASK_REMIND_MONTH);
            }
        }
    }


    /**
     * 排除暂存的任务, 已办结,未撤回的任务 短信提醒
     * @param now
     * @param type 2 代表每周;3 代表每月
     */
    public static void sendMss(LocalDate now , int type) {
        BizTaskDTO bizTask = BizTaskDTO.builder()
                /* 未办结 */
                .hasFinish(0)
                /* 签收 催反馈 */
                .hasSign(1)
                /* 已发布 */
                .hasPublish(1)
                .smsReminder(String.valueOf(type))
                .atStartDate2EndDate(now.toString())
                .build();

        // 1 取分页总数 可以多
        LambdaQueryWrapper<BizTaskDecompose> eq = new LambdaQueryWrapper<BizTaskDecompose>()
                .eq(BizTaskDecompose::getHasSign , 1)
                .eq(BizTaskDecompose::getHasFinish , 0)
                .eq(BizTaskDecompose::getSmsReminder , String.valueOf(type));
        List<BizTaskDecompose> decomposeList = bizTaskDecomposeMapper.selectListNoAdd(eq);
        int totalCounts=decomposeList.size();

        // 2 分页 取数据
        for (int currentPage=0 ; currentPage <= totalCounts/BasePageForm.MAx_PAGE_SIZE ;currentPage++){
            BasePageForm basePageForm = new BasePageForm(currentPage + 1, BasePageForm.MAx_PAGE_SIZE);
            IPage<BizTaskDTO> page = bizTaskDecomposeMapper.selectCommonTaskList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizTask);
            List<BizTaskDTO> records = page.getRecords();
            // 组装数据
            if(!records.isEmpty()){
                for (BizTaskDTO bizTaskDTO : records) {
                    //责任人的
                    String dutyMobile= cscpUserService.getMobilePhoneById(bizTaskDTO.getDutyPeopleId());
                    List<BizLeadershipEntrustmentDTO> contactPeoples =
                            bizLeaderLiaisonService.queryContactPeoplesByDutyPeopleId(bizTaskDTO.getDutyPeopleId());
                    Long dutyPeopleCompanyId = bizTaskDTO.getDutyPeopleCompanyId();
                    Integer hasSign = bizTaskDTO.getHasSign();
                    SmsSendEnum smsSendEnum=null;
                    switch (hasSign.intValue()) {
                        //  0：未签收 催签收
                        case 0:
                            smsSendEnum= SmsSendEnum.HH_TASK_SMS_SIGN;
                            break;
                        //  1 已签收 催反馈
                        case 1:
                            smsSendEnum= SmsSendEnum.HH_TASK_SMS;
                            break;
                        default: smsSendEnum= SmsSendEnum.HH_TASK_SMS;
                            break;
                    }

                    if(!sendVerification(bizTaskDTO,type,now)){
                        continue;
                    }
                    if(!contactPeoples.isEmpty()){
                        String contactMobile = contactPeoples.get(0).getLiaisonManTelephone();
                        startSendMss(contactMobile , bizTaskDTO , smsSendEnum , dutyPeopleCompanyId);
                    }

                    startSendMss(dutyMobile, bizTaskDTO,smsSendEnum,dutyPeopleCompanyId);

                }
            }

        }






    }


    public static void startSendMss(String mobile , BizTaskDTO bizTaskDTO , SmsSendEnum hhTaskSms , Long dutyPeopleCompanyId) {
        SmsSendInfoDto smsSendInfoDto = SmsSendInfoDto.builder().dutyPeopleCompanyId(dutyPeopleCompanyId).build();
        SmsSendUtil.sendSms(mobile, bizTaskDTO.getContent(), hhTaskSms,smsSendInfoDto);
        // 更新发送日期为今天s
        LambdaUpdateWrapper<BizTaskDecompose> lambdaUpdateWrapper1 = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper1.eq(BizTaskDecompose::getId, bizTaskDTO.getTaskDecomposeId())
                .set(BizTaskDecompose::getSmsSendTime, LocalDateTimeUtil.now());
        bizTaskDecomposeMapper.updateTenantId(null,lambdaUpdateWrapper1);
    }

    /**
     * 获得指定日期上一个工作日
     * @param day 指定日
     * @return 上一个工作日
     */
    public static LocalDate getBeforeWorkDay(LocalDate day) {
        LocalDate localDate = day.plusDays(-1);
        Boolean workDay = tSysWorkRestDateService.isWorkDay(localDate);
        if (!workDay) {
            return getBeforeWorkDay(localDate);
        }
        return localDate;
    }


    /**
     * 短信发送校验
     * @param bizTaskDTO
     * @param type 3 每月;2 每周
     * @param now 年月日
     * @return
     */
    public static boolean sendVerification(BizTaskDTO bizTaskDTO , int type , LocalDate now) {
        LocalDate startDate = bizTaskDTO.getStartDate();
        String startFormat = LocalDateTimeUtil.format(startDate, "yyyy-MM-dd");
        String nowStr = LocalDateTimeUtil.format(now, "yyyy-MM-dd");
        // 开始时间是今天，则不发送短信。
        if (StrUtil.equals(startFormat, nowStr)) {
            return false;
        }
        // 当天已经发送的不发送短信
        LocalDateTime smsSendTime = bizTaskDTO.getSmsSendTime();
        if(smsSendTime != null){
            String sendDate = LocalDateTimeUtil.format(smsSendTime, "yyyy-MM-dd");
            String nowDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd");
            if(StrUtil.equals(sendDate,nowDate)){
                return false;
            }
        }
        LambdaQueryWrapper<BizTaskFeedback> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizTaskFeedback::getBizTaskDecompose, bizTaskDTO.getTaskDecomposeId()).orderByDesc(BizTaskFeedback::getCreateTime)
                .select(BizTaskFeedback::getCreateTime);
        List<BizTaskFeedback> bizTaskFeedbacks = bizTaskFeedbackService.selectListNoAdd(queryWrapper);
        int hasFeedback = bizTaskDTO.getHasFeedback().intValue();
        // 有最新反馈
        if(hasFeedback == 1 ){
            BizTaskFeedback bizTaskFeedback = bizTaskFeedbacks.get(0);
            LocalDateTime feedbackTime = bizTaskFeedback.getCreateTime();
            switch (type) {
                case 2:
                    // 本周已经反馈不发
                    LocalDateTime endOfDayNow = LocalDateTime.of(now , LocalTime.MAX);
                    Duration duration = Duration.between(feedbackTime, endOfDayNow);
                    // 相差的天数
                    long days = duration.toDays();
                    // 周四 发
                    if (days >=0 && days <= INTERVAL_3DAYS) {
                        return false;
                    }
                    // 周三 发
                    if (days >=0 && days <= INTERVAL_2DAYS) {
                        return false;
                    }
                    break;
                case 3:
                    // 本月已经反馈不发
                    int month = feedbackTime.getMonth().getValue();
                    int year = feedbackTime.getYear();
                    // 同年同月
                    if (month == now.getMonth().getValue() && year == now.getYear()) {
                        return false;
                    }
                    break;
                default:
                    break;
            }
        }
        return true;
    }


}
