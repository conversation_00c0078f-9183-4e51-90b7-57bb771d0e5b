package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 日常打分领导管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizScoreLeaderDTO对象", description="日常打分领导管理表")
public class BizScoreLeaderDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 关联用户表的用户ID
     */
    @ApiModelProperty(value = "关联用户表的用户ID")
    private Long userId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    /**
     * 排序(不能录入同一排序号)
     */
    @ApiModelProperty(value = "排序(暂时没有使用)")
    private Integer sort;

    /**
     * 状态(暂时没有使用)
     */
    @ApiModelProperty(value = "状态(暂时没有使用)")
    private Integer status;

}
