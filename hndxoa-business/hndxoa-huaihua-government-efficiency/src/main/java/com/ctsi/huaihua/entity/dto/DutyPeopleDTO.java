package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname DutyPeopleDTO
 * @Description
 * @Date 2022/3/21/0021 16:55
 */
@Data
@ApiModel(value="DutyPeopleDTO对象", description="DutyPeopleDTO对象")
public class DutyPeopleDTO implements Serializable {

    private static final long serialVersionUID = 7930796566393272616L;

    /**
     * 本单位群组id
     */
    @ApiModelProperty(value = "本单位群组id")
    private Long groupId;

    /**
     * 本单位群组名称
     */
    @ApiModelProperty(value = "本单位群组名称")
    private String groupName;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private String companyId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String departmentId;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String post;

    /**
     * 委托人
     */
    @ApiModelProperty(value = "委托人")
    private List<DutyPeopleDTO> groupPopulationDTO;

}
