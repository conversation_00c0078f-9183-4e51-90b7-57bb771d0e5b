package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 日常打分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizScoreRecordDTO对象", description="日常打分记录表")
public class BizScoreRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被打分的用户ID
     */
    @NotNull(message = "被打分的用户ID不允许为空")
    @ApiModelProperty(value = "被打分的用户ID")
    private Long userId;

    /**
     * 被打分的用户姓名
     */
    @NotBlank(message = "被打分的用户姓名不允许为空")
    @ApiModelProperty(value = "被打分的用户姓名")
    private String userName;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    @NotNull(message = "打分分数不允许为空")
    @Range(min = 0, max = 60, message = "分数范围必须在0~60分之前")
    private Integer score;

    @ApiModelProperty(value = "打分年份")
    private Integer scoreYear;

    /**
     * 打分月份
     */
    @ApiModelProperty(value = "打分月份")
    private Integer scoreMonth;

    /**
     * 打分理由
     */
    @ApiModelProperty(value = "打分理由")
    private String remarks;

    @ApiModelProperty(value = "是否修改")
    private Integer hasModify;

    @ApiModelProperty(value = "打分用户ID")
    private Long createBy;

    @ApiModelProperty(value = "打分用户名称")
    private String createName;

    @ApiModelProperty(value = "打分时间")
    private LocalDateTime createTime;


    @ApiModelProperty(value = "职务")
    private String post;

    @ApiModelProperty(value = "部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "单位ID")
    private Long companyId;

    @ApiModelProperty(value = "单位名称")
    private String companyName;

}
