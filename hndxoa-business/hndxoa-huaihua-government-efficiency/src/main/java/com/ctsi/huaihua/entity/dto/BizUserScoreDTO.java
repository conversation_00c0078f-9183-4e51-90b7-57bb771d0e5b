package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizUserScoreDTO", description = "")
public class BizUserScoreDTO extends BaseDtoEntity {

    @ApiModelProperty("平均分数")
    private Double score;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long templateBusinessId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String templateBusinessName;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private String unitId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String branchId;


    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}
