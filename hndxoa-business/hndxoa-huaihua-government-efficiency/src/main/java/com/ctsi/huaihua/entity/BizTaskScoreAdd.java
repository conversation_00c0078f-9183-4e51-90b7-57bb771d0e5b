package com.ctsi.huaihua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务加分表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_task_score_add")
@ApiModel(value="BizTaskScoreAdd对象", description="任务加分表")
public class BizTaskScoreAdd extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 申请加分的内容
     */
    @ApiModelProperty(value = "申请加分的内容")
    private String scoreAddContent;

    /**
     * 申请加分的理由
     */
    @ApiModelProperty(value = "申请加分的理由")
    private String scoreAddReason;

    /**
     * 申请加分的分数
     */
    @ApiModelProperty(value = "申请加分的分数")
    private Double score;

    /**
     * 接收审核人的的id
     */
    @ApiModelProperty(value = "接收审核人的的id")
    private Long reviewerId;

    /**
     * 接收审核人的部门id
     */
    @ApiModelProperty(value = "接收审核人的部门id")
    private Long reviewerDepartmentId;

    /**
     * 接收审核人的单位id
     */
    @ApiModelProperty(value = "接收审核人的单位id")
    private Long reviewerCompanyId;

    /**
     * 0：未审核 1 审核通过 2 通过并调整延期时间
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    /**
     * 申请人是否查看 0：未查看 1:已查看
     */
    @ApiModelProperty(value = "0：未查看 1:已查看")
    private Integer hasRead;

    /**
     * 审核人是否查看 0：未查看 1:已查看
     */
    @ApiModelProperty(value = "0：未查看 1:已查看")
    private Integer auditHasRead;


    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "审核人的id")
    private Long auditId;

    /**
     * 审核人的姓名
     */
    @ApiModelProperty(value = "审核人的姓名")
    private String auditName;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;



}
