package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 通用承办管理任务查看表
 * 反馈任务列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "BizTaskDTO对象", description = "通用承办管理任务表")
public class BizTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "任务主表的id")
    private Long taskSupervisionId;


    @ApiModelProperty(value = "任务分解表的id")
    private Long taskDecomposeId;

    /**
     * 任务编号，中间：隔开，：之前为父编号，转办的以zb开头
     */
    @ApiModelProperty(value = "任务编号，中间：隔开，：之前为父编号，转办的以zb开头")
    private String taskNumber;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    @ApiModelProperty(value = "主任务，任务概叙")
    private String taskDescription;

    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "创建部门")
    private String departmentName;

    /**
     * 拟稿人单位名称
     */
    @ApiModelProperty(value = "创建单位")
    private String companyName;

    /**
     * 交办时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "交办时间")
    private LocalDateTime createTime;


    /**
     * 主任务交办时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "主任务交办时间")
    private LocalDateTime supCreateTime;


    /**
     * 分解任务 截止时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "截止时间，yyyy-MM-dd")
    private LocalDate dueTime;


    /**
     * 主任务 截止时间，yyyy-MM-dd
     */
    @ApiModelProperty(value = "截止时间，yyyy-MM-dd")
    private LocalDate supDueTime;

    /**
     * 接收时间，yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "签收时间，接收时间，yyyy-MM-dd HH:mm")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "交办，开始时间")
    private String startTime;

    @ApiModelProperty(value = "交办，结束时间")
    private String endTime;

    /**
     * 事项类型
     * 1正常，2逾期 3 临期
     */
    @ApiModelProperty(value = "事项类型 1正常，2逾期 3 临期")
    private String queryType;

    /**
     * 任务标签，对应数据字典taskTag
     */
    @ApiModelProperty(value = "任务标签，对应数据字典taskTag")
    private String taskTag;

    /**
     * 任务来源，对应数据字典taskSource
     */
    @ApiModelProperty(value = "任务来源，对应数据字典taskSource")
    private String taskSource;

    /**
     * 任务类型，对应数据字典taskType
     */
    @ApiModelProperty(value = "任务类型，对应数据字典taskType")
    private String taskType;

    /**
     * 0 未发布暂存，1：发布  2：撤回
     */
    @ApiModelProperty(value = "0 未发布暂存，1：发布  2：撤回")
    private Integer hasPublish;

    /**
     * 主表办理状态
     * 办理状态：0 未办结 1 已办结
     */
    @ApiModelProperty(value = "主表办理状态：0 未办结 1 已办结")
    private Integer supHasFinish;
    /**
     * 子表办理状态
     * 办理状态：0 未办结 1 已办结
     */
    @ApiModelProperty(value = "分解表办理状态：0 未办结 1 已办结")
    private Integer hasFinish;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private LocalDateTime hasFinishTime;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private String hasFinishTimeStrig;

    /**
     * 任务新增的方式0表示新增 1表示转办
     */
    @ApiModelProperty(value = "任务新增的方式：0表示新增 1表示转办")
    private Integer taskAddType;

    /**
     * 任务来自转办，转办的id
     */
    @ApiModelProperty(value = "任务来自转办，转办的id")
    private Long taskTransferId;

    /**
     * 0：未签收 1 已签收
     */
    @ApiModelProperty(value = "0：未签收 1 查询已签收，可能包含已经撤回 2 撤回，99 表示只取已经签收的数据")
    private Integer hasSign;

    /**
     * 0：未转办 1 已转办
     */
    @ApiModelProperty(value = "0：未转办 1 已转办")
    private Integer hasTransfer;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String dutyPeople;

    /**
     * 责任人姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    private String dutyPeopleName;


    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long dutyPeopleId;

    /**
     * 责任人单位id
     */
    @ApiModelProperty(value = "责任人单位id")
    private Long dutyPeopleCompanyId;


    @ApiModelProperty(value = "单位id")
    private Long companyId;

    /**
     * 联络员id
     */
    @ApiModelProperty(value = "联络员id")
    private Long contactPeopleId;

    /**
     * 联络员名称
     */
    @ApiModelProperty(value = "联络员名称")
    private String contactPeopleName;


    /**
     * 0：未审核 1 审核通过
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过")
    private Integer hasReviewer;

    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人的职务")
    private String dutyPeoplePostName;


    @ApiModelProperty(value = "任务表单id")
    private Long formId;

    /**
     * 预警颜色状态码  1 : 红 ;2 黄; 3 绿;
     */
    @ApiModelProperty(value = "预警颜色状态码,  1 : 红 ;2 黄; 3 绿;")
    private String warningColorCode;


    /**
     * 是否有反馈记录 0：没有 , 1 有
     */
    @ApiModelProperty(value = " 是否有反馈记录 0：没有, 1 有")
    private Integer hasFeedback;


    /**
     * 对于最新反馈的成果标记 0：没有 , 1 有
     * 对于最新反馈的成果，页面列表以小红点标识最新数据，用户点击成果审核后恢复默认样式。
     */
    @ApiModelProperty(value = " 对于最新反馈的成果，页面列表以小红点标识最新数据,用户点击成果审核后恢复默认样式  0：没有标识, 1 最新反馈成果,有小红点标识")
    private Integer newFeedbackSign;

    /**
     * 最新反馈时间
     */
    @ApiModelProperty(value = "最新反馈时间,成果上报时间")
    private LocalDateTime newFeedbackTime;


    /**
     * 小红点标识最新数据  0：没有 , 1 有
     * 对于最新办结的任务，页面列表以小红点标识最新数据，用户浏览后 后恢复默认样式。
     */
    @ApiModelProperty(value = " 最新办结的任务,小红点标识最新数据,用户点击任务详情或办结详情 后恢复默认样式， 0：没有标识, 1 最新办结,有小红点标识")
    private Integer newFinishSign;

    /**
     * 主任务单位id
     */
    @ApiModelProperty(value = "主任务单位id")
    private Long supCompanyId;


    /**
     *
     */
    @ApiModelProperty(value = "主任务创建人id")
    private Long supCreateById;


    @ApiModelProperty(value = "延期申请状态 0 无申请, 1 有申请(暂未通过), 2 申请通过, 3 通过并手动调整延期时间")
    private Integer taskDelayStatus;


    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;
    /**
     * 难度系数
     */
    @ApiModelProperty(value = "难度系数")
    private String degreeDifficulty;

    /**
     * 移动端搜索key
     */
    @ApiModelProperty(value = "移动端搜索key")
    private String searchKey;


    /**
     * 动态排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String sortField;


    /**
     * 类型
     */
    @ApiModelProperty(value = "排序类型")
    private String sortType;


    /**
     * 定时任务搜索条件
     */
    @ApiModelProperty(value = "在开始日期到到结束日期范围内")
    private String atStartDate2EndDate;


    /**
     * 短信提醒方式，对应数据字典sms_reminder
     * 1 -> 无,2->每周（提前一天提醒）,3->每月（提前1天提醒）
     */
    @ApiModelProperty(value = "短信提醒方式，对应数据字典sms_reminder")
    private String smsReminder;


    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    private LocalDate startDate;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @NotBlank(message = "任务结束时间不能为空")
    private LocalDate endDate;

    @ApiModelProperty(value = "(已发)短信提醒日期")
    private LocalDateTime smsSendTime;


    /**
     * 责任人的部门名称
     */
    @ApiModelProperty(value = "责任人的部门名称")
    private String dutyPeopleDepartmentName;

    /**
     * 责任人的单位名称
     */
    @ApiModelProperty(value = "责任人的单位名称")
    private String dutyPeopleCompanyName;

    /**
     * 任务来源单位 1 本单位,2外单位
     * TASK_SOURCE_OWMER_DEPT
     * TASK_SOURCE_OTHER_DEPT
     */
    @ApiModelProperty(value = "任务来源单位 1 本单位,2外单位 ")
    private Integer taskSourceDept;


    @ApiModelProperty(value = "是否有减分（1：有 0：没有）")
    private Integer whetherPoints;

    @ApiModelProperty(value = "加分申请数量")
    private Integer scoreAddCount;

    @ApiModelProperty(value = "加分申请总分数")
    private double score;


    @ApiModelProperty(value = "申请方是否未读(1:已读 0:未读)")
    private Integer hasRead;

    @ApiModelProperty(value = "审核方是否未读(1:已读 0:未读)")
    private Integer auditHasRead;

    @ApiModelProperty(value = "加分申请是否待审核(1:已审核 0:未审核)")
    private List<Integer> hasScoreAddReviewerList;

    @ApiModelProperty(value = "申请加分的分解表ID集合")
    private List<Long> bizTaskDecomposeIdList;

    @ApiModelProperty(value = "当前页数", position = 1)
    private Integer currentPage;

    @ApiModelProperty(value = "每页请求条数", position = 2)
    private Integer pageSize;

    @ApiModelProperty(value = "是否预览(0:未预览 1:预览)")
    private Integer preview;

    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "审核人的id")
    private Long auditId;

    /**
     * 减分列表是否需要排序
     */
    @ApiModelProperty(value = "减分列表是否需要排序")
    private Integer sortSub;

    @ApiModelProperty(value = "子任务是否存在加分申请和减分标识(申请人专用)")
    private Integer hasScoreFlag;

    @ApiModelProperty(value = "分解表下最后一条加分申请创建时间")
    private LocalDateTime endCreateTime;


    @ApiModelProperty(value = "任务紧急程度，对应数据字典taskDegreeUrgency")
    private String degreeUrgency;

    @ApiModelProperty(value = "签收人的id")
    private Long signPeopleId;


    @ApiModelProperty(value = "任务的得分")
    private Double actualScore;

    @ApiModelProperty(value = "督查任务等级对应biz_task_level")
    private String inspectorTaskLevel;

    @ApiModelProperty(value = "督查任务具体的事项对应biz_task_levell")
    private String inspectorItems;

    @ApiModelProperty(value = "重要性")
    private BizTaskLevelDTO inspectorTaskLevelDTO;

    @ApiModelProperty(value = "交办事项")
    private BizTaskLevelDTO inspectorItemsDTO;

    @ApiModelProperty(value = "任务难易性，对应数据字典task_difficulty")
    private String difficulty;

    @ApiModelProperty(value = "分解任务分值")
    private String decomposeTaskSource;
    /**
     * 审核人的id
     */
    @ApiModelProperty(value = "牵头单位人的id")
    private Long leadPeopleId;

    /**
     * 审核人名称
     */
    @ApiModelProperty(value = "牵头单位人的id")
    private String leadPeopleName;


    /**
     * 是否显示反馈按钮(0-显示  1-不显示)
     */
    @ApiModelProperty(value = "是否显示反馈按钮")
    private int isFeedbackBtn;

    /**
     * 是否显示反馈按钮(0-是  1-不是)
     */
    @ApiModelProperty(value = "是否牵头人")
    private int isLead;

    @ApiModelProperty(value = "主任务创建人部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "任务层级，转办了多少级")
    private Integer taskLevel;

    @ApiModelProperty(value = "未转办时的单位id")
    private Long topCompanyId;

}
