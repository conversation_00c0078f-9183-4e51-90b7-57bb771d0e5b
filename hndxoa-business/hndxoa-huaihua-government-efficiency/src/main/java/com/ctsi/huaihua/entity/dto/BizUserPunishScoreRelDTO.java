package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户惩罚分记录关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizUserPunishScoreRelDTO对象", description="用户惩罚分记录关系表")
public class BizUserPunishScoreRelDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被处罚减分的用户ID
     */
    @ApiModelProperty(value = "被处罚减分的用户ID")
    private Long userId;

    /**
     * 被处罚减分的用户姓名
     */
    @ApiModelProperty(value = "被处罚减分的用户姓名")
    private String userName;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Integer hasRead;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String post;

    /**
     * 处罚分数表主键id
     */
    @ApiModelProperty(value = "处罚分数表主键id")
    private Long punishScoreRecordId;

    @ApiModelProperty(value = "被处罚公司名称")
    private String punishCompanyName;

    @ApiModelProperty(value = "被处罚公司id")
    private Long punishCompanyId;

    @ApiModelProperty(value = "被处罚部门名称")
    private String punishDepartmentName;

    @ApiModelProperty(value = "被处罚部门id")
    private Long punishDepartmentId;


}
