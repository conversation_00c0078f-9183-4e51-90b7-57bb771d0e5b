package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 奖分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizPunishScoreRecordDTO对象", description = "奖分记录表")
public class BizPunishScoreRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户集合
     */
    @ApiModelProperty(value = "用户集合")
    private List<BizUserPunishScoreRelDTO> bizUserPunishScoreRelDTOS;

    /**
     * 姓名(科室、职务)
     */
    @ApiModelProperty(value = "姓名(科室、职务)")
    private String name;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 展示分数
     */
    @ApiModelProperty(value = "展示分数")
    private String scoreStr;

    /**
     * 减分年份
     */
    @ApiModelProperty(value = "减分年份")
    private Integer scoreYear;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 减分原因
     */
    @ApiModelProperty(value = "减分原因")
    private String reductionReason;

    /**
     * 减分原因集合
     */
    @ApiModelProperty(value = "减分原因集合")
    private List<String> reductionReasonList;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private Long reviewerId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String reviewerName;

    /**
     * 审核人部门ID
     */
    @ApiModelProperty(value = "审核人部门ID")
    private Long reviewerDepartmentId;

    /**
     * 审核人单位ID
     */
    @ApiModelProperty(value = "审核人单位ID")
    private Long reviewerCompanyId;

    /**
     * 市政府办是否未读 0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "市政府办是否未读0: 未查看  1:已查看")
    private Integer bizMunicipalUnitHasRead;

    /**
     * 0：未审核 1 审核通过 2 驳回
     */
    @ApiModelProperty(value = "0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    @ApiModelProperty(value = "审核状态集合")
    private List<Integer> hasReviewerList;

    /**
     * 驳回理由
     */
    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;

    /**
     * 市直单位是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "市直单位是否未读0: 未查看  1:已查看")
    private Integer straightUnitHasRead;

    /**
     * 申请减分来源：1市政府办业务科室 2督查室 3市直单位
     */
    @ApiModelProperty(value = "申请减分来源：1市政府办业务科室 2督查室 3市直单位")
    private Integer punishResource;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 个人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "个人是否未读0: 未查看  1:已查看")
    private Integer hasRead;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewerTime;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 通报日期
     */
    @ApiModelProperty(value = "通报日期")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "当前页数")
    private Integer currentPage;

    @ApiModelProperty(value = "每页请求条数")
    private Integer pageSize;

    @ApiModelProperty(value = "创建人单位名称")
    private String createCompanyName;
}
