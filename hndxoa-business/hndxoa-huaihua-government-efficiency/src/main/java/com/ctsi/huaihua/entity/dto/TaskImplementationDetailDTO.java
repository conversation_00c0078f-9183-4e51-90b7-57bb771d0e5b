package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 通用承办管理任务查看表
 * 反馈任务列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "TaskImplementationDetailDTO", description = "报表执行详情")
public class TaskImplementationDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;




    // -----------------------------------

    /**
     * 交办时间，yyyy-MM-dd    2021-11-15 09:00
     */
    @ApiModelProperty(value = "主任务交办时间")
    private LocalDateTime supCreateTime;

    /**
     * 主任务创建人
     */
    @ApiModelProperty(value = "主任务创建人")
    private String supCreateName;


    /**
     * 主任务创建部门名称
     */
    @ApiModelProperty(value = "主任务创建部门名称")
    private String supDepartmentName;


    /**
     * 主任务创建单位名称
     */
    @ApiModelProperty(value = "主任务创建单位名称")
    private String supCompanyName;


    /**
     * 责任人的姓名
     */
    @ApiModelProperty(value = "责任人的姓名")
    private String dutyPeopleName;

    /**
     * 责任人的部门名称
     */
    @ApiModelProperty(value = "责任人的部门名称")
    private String dutyPeopleDepartmentName;

    /**
     * 责任人的单位名称
     */
    @ApiModelProperty(value = "责任人的单位名称")
    private String dutyPeopleCompanyName;


    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人的职务")
    private String dutyPeoplePostName;

    /**
     *  签收时间接收时间，yyyy-MM-dd HH:mm
     */
    @ApiModelProperty(value = "接收时间，yyyy-MM-dd HH:mm")
    private LocalDateTime receiveTime;



    /**
     * 子表办理状态
     * 办理状态：0 未办结 1 已办结
     */
    @ApiModelProperty(value = "分解表办理状态：0 未办结 1 已办结")
    private Integer hasFinish;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private LocalDateTime hasFinishTime;


    /**
     * 该分解任务反馈信息
     */
    @ApiModelProperty(value = "该分解任务成果反馈信息")
    private List<BizTaskFeedbackDTO> feedBackList;


    /**
     * 转办任务集合
     */
    @ApiModelProperty(value = "转办任务集合")
    private List<BizTaskDecomposeDTO> transformTaskList;


    // ----------------------------------- end


    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 0：未转办 1 已转办
     */
    @ApiModelProperty(value = "0：未转办 1 已转办")
    private Integer hasTransfer;
    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;


    /**
     * 主任务单位id
     */
    @ApiModelProperty(value = "主任务单位id")
    private Long supCompanyId;


    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private Long dutyPeopleId;
    /**
     * 责任人单位id
     */
    @ApiModelProperty(value = "责任人单位id")
    private Long dutyPeopleCompanyId;
}
