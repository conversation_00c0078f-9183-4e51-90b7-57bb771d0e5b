/*
package com.ctsi.huaihua.job;

import com.ctsi.huaihua.entity.dto.BizScoreRecordDTO;
import com.ctsi.huaihua.service.IBizScoreRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

*/
/**
 * <AUTHOR>
 * @Classname ScheduleToLogJob
 * @Description
 * @Date 2021/12/16/0016 9:56
 *//*

@Component
public class BizScoreRecordJob {

    private static Logger logger = LoggerFactory.getLogger(BizScoreRecordJob.class);

    @Autowired
    private IBizScoreRecordService bizScoreRecordService;

    @XxlJob("autoGenerateScoreRecord")
    public void autoGenerateScoreRecord() throws Exception {
        logger.info("定时任务：(日常打分)自动生成默认打分记录开始");
        XxlJobHelper.log("定时任务：(日常打分)自动生成默认打分记录开始");

        // 日程记录转换为日志记录
        bizScoreRecordService.autoGenerateScoreRecord(new BizScoreRecordDTO());

        logger.info("-------------(日常打分)自动生成默认打分记录---------------end");
    }
}
*/
