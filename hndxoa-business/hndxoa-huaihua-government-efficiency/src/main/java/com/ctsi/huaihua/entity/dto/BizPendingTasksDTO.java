package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizPendingTasksDTO
 * @Author: json
 * @Description:
 * @Date: 2022/8/9 14:47
 * @Version: 1.0
 */
@Data
public class BizPendingTasksDTO {

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "紧急程度")
    private String degreeUrgency;

    @ApiModelProperty(value = "任务状态(3:待审 1:已发布 4:已驳回)", required = true)
    @NotNull(message = "任务状态不能为空")
    private Integer taskState;

    @ApiModelProperty(value = "牵头人的id")
    private Long LeaderId;

}
