package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务得分
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskScoreDTO对象", description="任务得分")
public class BizTaskScoreDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务表biz_task_supervision的主键
     */
    @ApiModelProperty(value = "任务表biz_task_supervision的主键")
    private Long bizTaskSupervisionId;

    /**
     * 应该得分
     */
    @ApiModelProperty(value = "应该得分")
    private Double shouldScore;

    /**
     * 实际得分
     */
    @ApiModelProperty(value = "实际得分")
    private Double actualScore;

    /**
     * 调分原因
     */
    @ApiModelProperty(value = "调分原因")
    private String adjustableReason;

    /**
     * 得分公式
     */
    @ApiModelProperty(value = "得分公式")
    private String scoreFormulas;

    /**
     * 分解表的主键id
     */
    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecompose;

    /**
     * 申请人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "申请人是否未读0: 未查看  1:已查看")
    private Integer hasRead;

    /**
     * 审核人是否未读0: 未查看  1:已查看
     */
    @ApiModelProperty(value = "审核人是否未读0: 未查看  1:已查看")
    private Integer auditHasRead;


}
