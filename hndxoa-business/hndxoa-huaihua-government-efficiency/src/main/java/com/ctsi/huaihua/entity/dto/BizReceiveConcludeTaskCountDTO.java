package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.huaihua.entity.dto
 * @ClassName: BizReceiveConcludeTaskCountDTO
 * @Author: json
 * @Description:
 * @Date: 2022/11/3 16:18
 * @Version: 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizReceiveConcludeTaskCountDTO {

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "单位id")
    private Set<Long> companyId;

    @ApiModelProperty(value = "用户id")
    private Set<Long> userId;

    @ApiModelProperty(value = "用户所属单位，用于过滤")
    private Set<Long> userCompanyId;

    @ApiModelProperty(value = "查询类型（1:签收 2:已办结）")
    private Integer queryType;

}
