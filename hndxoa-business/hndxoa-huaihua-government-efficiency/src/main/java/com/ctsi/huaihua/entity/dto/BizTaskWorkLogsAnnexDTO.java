package com.ctsi.huaihua.entity.dto;


import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 工作日志及附件
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BizTaskWorkLogsAnnexDTO对象", description="工作日志及附件")
public class BizTaskWorkLogsAnnexDTO extends BizTaskWorkLogsDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 附件集合
     */
    @ApiModelProperty(value = "附件集合")
    private List<CscpEnclosureFile> EnclosureFileList;


}
