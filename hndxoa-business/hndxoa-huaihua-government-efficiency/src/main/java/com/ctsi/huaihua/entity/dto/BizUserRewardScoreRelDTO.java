package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户奖励分记录关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizUserRewardScoreRelDTO对象", description="用户奖励分记录关系表")
public class BizUserRewardScoreRelDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被奖励加分的用户ID
     */
    @ApiModelProperty(value = "被奖励加分的用户ID")
    private Long userId;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Integer hasRead;

    /**
     * 被奖励加分的用户姓名
     */
    @ApiModelProperty(value = "被奖励加分的用户姓名")
    private String userName;

    @ApiModelProperty(value = "职务")
    private String post;

    @ApiModelProperty(value = "部门ID")
    private Long rewardDepartmentId;

    @ApiModelProperty(value = "部门名称")
    private String rewardDepartmentName;

    @ApiModelProperty(value = "单位ID")
    private Long rewardCompanyId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String rewardCompanyName;

    /**
     * 奖励分数表主键id
     */
    @ApiModelProperty(value = "奖励分数表主键id")
    private Long rewardScoreRecordId;


}
