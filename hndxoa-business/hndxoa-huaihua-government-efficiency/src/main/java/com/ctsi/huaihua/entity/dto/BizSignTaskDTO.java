package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 签收详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizSignTaskDTO", description="任务签收详情")
public class BizSignTaskDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")

    private String createName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 任务类型，对应数据字典taskType
     */
    @ApiModelProperty(value = "任务类型，对应数据字典taskType")
    private String taskType;


    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;

    /**
     * 拟稿人部门名称
     */
    @ApiModelProperty(value = "拟稿人部门名称")
    private String departmentName;

    /**
     * 拟稿人手机号码
     */
    @ApiModelProperty(value = "拟稿人手机号码")
    private String mobile;

    /**
     * 拟稿人单位名称
     */
    @ApiModelProperty(value = "拟稿人单位名称")
    private String companyName;

    /**
     * 任务编号，中间：隔开，：之前为父编号，转办的以zb开头
     */
    @ApiModelProperty(value = "任务编号，中间：隔开，：之前为父编号，转办的以zb开头")
    private String taskNumber;


    @ApiModelProperty(value = "承办单位数")
    private Integer undertakeUnitNumber;


    @ApiModelProperty(value = "承办单位数")
    private Integer signUnitNumber;
}
