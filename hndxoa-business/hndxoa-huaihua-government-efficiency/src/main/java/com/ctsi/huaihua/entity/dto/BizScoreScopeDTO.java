package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 领导委托信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizScoreScopeDTO对象", description="领导打分范围表")
public class BizScoreScopeDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 被打分的用户ID
     */
    @ApiModelProperty(value = "被打分的用户ID，用\",\"隔开")
    private String markUserIds;

    /**
     * 领导ID(日常打分领导表主键)
     */
    @ApiModelProperty(value = "领导ID")
    private Long leaderId;

    /**
     * 领导姓名
     */
    @ApiModelProperty(value = "领导姓名")
    private String leaderName;

    @ApiModelProperty(value = "前端用户选人范围: 1单位, 2租户")
    private Integer userAddScope;

    @ApiModelProperty(value = "打分范围")
    private List<BizBaseScoreLeaderScopeDTO> bizBaseScoreLeaderScopeDTOList;

    @ApiModelProperty(value = "最后时间")
    private LocalDateTime lastUpdateTime;

    @ApiModelProperty(value = "最后修改人ID")
    private Long lastUpdateBy;

    @ApiModelProperty(value = "最后修改人姓名")
    private String lastUpdateName;


}
