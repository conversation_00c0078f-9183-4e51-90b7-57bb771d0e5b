package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 日常打分领导管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@ApiModel(value="BizScoreLeaderDTO对象", description="日常打分领导管理表")
public class BizBaseScoreLeaderScopeDTO implements Serializable {

    private static final long serialVersionUID = -899014388527745653L;

    /**
     * 被打分的用户ID
     */
    @ApiModelProperty(value = "被打分的用户ID")
    private Long userId;

    /**
     * 被打分的用户名称
     */
    @ApiModelProperty(value = "被打分的用户名称")
    private String userName;


}
