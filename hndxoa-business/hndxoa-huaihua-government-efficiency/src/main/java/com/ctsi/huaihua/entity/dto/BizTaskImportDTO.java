package com.ctsi.huaihua.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @Classname BizTaskImportAndExportService
 * @Description
 * @Date 2022/3/15/0015 14:56
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskImportDTO对象", description="任务录入导入表")
public class BizTaskImportDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 责任人的姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    @ExcelProperty("责任人姓名")
    private String dutyPeopleName;

    /**
     * 责任人的职务
     */
    @ApiModelProperty(value = "责任人职务")
    @ExcelProperty("责任人职务")
    private String dutyPeoplePostName;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    @ExcelProperty("任务内容")
    private String content;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    @ExcelProperty("开始时间")
    private String startDateStr;

    @ApiModelProperty(value = "开始时间")
    @ExcelIgnore
    private LocalDate startDate;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @ExcelProperty("截止时间")
    private String endDateStr;

    @ApiModelProperty(value = "截止时间")
    @ExcelIgnore
    private LocalDate endDate;

    /**
     * 难度系数
     */
    @ApiModelProperty(value = "难度系数")
//    @ExcelProperty("难度系数")
    private String degreeDifficulty;

    @ApiModelProperty(value = "牵头单位")
    @ExcelProperty("牵头单位")
    private String leadingUnit;


    /**
     * 短信提醒方式，对应数据字典sms_reminder
     */
    @ApiModelProperty(value = "短信提醒方式，对应数据字典sms_reminder")
    @ExcelProperty("短信提醒")
    private String smsReminder;



}
