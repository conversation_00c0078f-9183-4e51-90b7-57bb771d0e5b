package com.ctsi.huaihua.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;

import com.ctsi.hndx.tree.TreePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskLevelDTO对象", description="")
public class BizTaskLevelDTO extends BaseDtoEntity implements TreePO {

    private static final long serialVersionUID = 1L;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "级别名称")
    private String typeName;

    /**
     * 上级类型id，顶层为0
     */
    @ApiModelProperty(value = "上级类型id，顶层为0")
    private Long parentId;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderBy;

//    /**
//     * 系数
//     */
//    @ApiModelProperty(value = "系数")
//    private Integer factor;
//
//    /**
//     * 权重
//     */
//    @ApiModelProperty(value = "权重")
//    private Double weight;


    @Override
    public String getTitle() {
        return typeName;
    }
}
