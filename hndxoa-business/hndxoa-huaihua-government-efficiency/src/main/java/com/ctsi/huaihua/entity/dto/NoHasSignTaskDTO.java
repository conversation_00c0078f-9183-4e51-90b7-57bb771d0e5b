package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 没有签收的任务的dto
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "NoHasSignTaskDTO对象", description = "没有签收的任务的dto")
public class NoHasSignTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;



    @ApiModelProperty(value = "交办，开始时间")
    private String startTime;

    @ApiModelProperty(value = "交办，结束时间")
    private String endTime;

    @ApiModelProperty(value = "任务紧急程度，对应数据字典taskDegreeUrgency")
    private String degreeUrgency;


    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String content;


    @ApiModelProperty(value = "查询条件 1 表示只按照 责任过滤 2 表示即按照责任人过滤，又按照市政府交办给单位的任务")
    private Integer queryType;



    @ApiModelProperty(value = "责任人id")
    private Long dutyPeopleId;


    @ApiModelProperty(value = "单位id")
    private Long companyId;



}
