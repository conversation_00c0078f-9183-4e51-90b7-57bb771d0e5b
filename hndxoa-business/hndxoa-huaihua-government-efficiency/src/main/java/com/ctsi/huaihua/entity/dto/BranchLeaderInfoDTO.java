package com.ctsi.huaihua.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname BranchLeaderInfoDTO
 * @Description
 * @Date 2022/9/23/0023 15:05
 */
@Data
@ApiModel(value = "BranchLeaderInfoDTO对象", description = "分管领导信息")
public class BranchLeaderInfoDTO implements Serializable {

    private static final long serialVersionUID = -2985490678670286738L;

    @ApiModelProperty(value = "分管领导ID")
    private Long userId;

    @ApiModelProperty(value = "分管领导姓名")
    private String userName;

    @ApiModelProperty(value = "分管领导部门ID")
    private Long departmentId;

    @ApiModelProperty(value = "分管领导部门名称")
    private String departmentName;

    @ApiModelProperty(value = "分管领导单位ID")
    private Long companyId;

    @ApiModelProperty(value = "分管领导单位名称")
    private String companyName;

    @ApiModelProperty(value = "分管领导手机号码")
    private String mobile;
}
