/*
package com.ctsi.huaihua.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

*/
/**
 * @description
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2022-03-10
 **//*

@Component
public class HuaihuaJob {
    private static Logger logger = LoggerFactory.getLogger(HuaihuaJob.class);

    */
/**
     * 短信提醒
     * 无、每周（提前1天）、每月（提前1天）。
     * <p>
     * **每周（提前****1天****）：**根据任务的开始时间-截止时间范围里的自然周，周四的09:00对责任人及联络员发送短信提醒；如果开始时间在周四或者周五，则从第二周才开始短信提醒。
     * <p>
     * **每月（提前****1天****）：**根据任务的开始时间-截止时间范围里的自然月，月底的前一天且属于工作日的09:00对责任人及联络员发送短信提醒；如果开始时间在月底最后两天范围内，则从第二月才开始短信提醒。
     * <p>
     * **短信提醒内容：**您有一个任务名称为“XX”待反馈，请及时登录政务效能系统进行处理。
     * * 每月短信提醒 任务是3.15-5.15 则3月,4月发送提醒 , 月底工作日的09:00提醒（提前1天）
     * * 如果开始时间在月底最后两天范围内，则从第二月才开始短信提醒
     * <p>
     * 逻辑: 1 先判断今天是不是 发送短信日
     * 2 发每周短信
     * 3 发每月短信
     * 4 若是 筛选每周提醒的任务数据 发送短信
     *  规则: 1 发送短信日期是 创建日期的不发送
     *        2 发送日期是休息日不发送 提前一个工作日( 每月 )发送短信; 发送日期是休息日不发送 提前到周三( 每周 )发送短信, 若周三是休息日 则当周不发送短信
     *         3 每周 本周已反馈的任务 不发送短信; 每月 本月已经反馈的任务 不发送短信
     *//*

    @XxlJob("autoTaskRemind")
    public void autoTaskRemind() {
        logger.info("-------------定时任务：(分解任务)短信自动提醒开始-------------start");
        XxlJobHelper.log("定时任务：短信自动提醒开始");
        LocalDate now = LocalDate.now();
        //now = LocalDate.of(2022 , 4 , 29);
        HuaihuaJobUtil.autoTaskRemind(now);
        logger.info("-------------定时任务：(分解任务)短信自动提醒结束---------------end");

    }


}
*/
