package com.ctsi.huaihua.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 任务督查的主表(加分申请用)
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTaskSupervisionAndScoreAddDTO对象", description="任务督查的主表(加分申请用)")
public class BizTaskSupervisionAndScoreAddDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "主表创建人")
    private String supCreateName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "主表创建时间(交办时间)")
    private LocalDateTime supCreateTime;

    /**
     * 任务名称，任务标题
     */
    @ApiModelProperty(value = "任务名称，任务标题")
    private String title;

    /**
     * 任务来源，对应数据字典taskSource
     */
    @ApiModelProperty(value = "任务来源，对应数据字典taskSource")
    private String taskSource;

    /**
     * 考核方式，对应数据字典ExamineMethod
     */
    @ApiModelProperty(value = "考核方式，对应数据字典ExamineMethod")
    private String examineMethod;

    /**
     * 截止时间，yyyy-mm-dd
     */
    @ApiModelProperty(value = "主任务截止时间，yyyy-mm-dd")
    private LocalDate dueTime;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String dutyPeople;

    /**
     * 任务转办过来后的分解的任务id
     */
    @ApiModelProperty(value = "任务转办过来后的分解的任务id")
    private Long taskTransferDecomposeId;

    @ApiModelProperty(value = "任务分解表集合")
    private List<BizTaskDecomposeDTO> bizTaskDecomposeDTOList;

    @ApiModelProperty(value = "主键ID集合")
    private List<Long> bizTaskSupervisionIdList;

    @ApiModelProperty(value = "申请加分的主键ID")
    private Long taskScoreAddId;

    @ApiModelProperty(value = "申请加分的主键ID集合")
    private List<Long> taskScoreAddIdList;

    @ApiModelProperty(value = "申请单位(分解表责任人的单位名称)")
    private String dutyPeopleCompanyName;

    @ApiModelProperty(value = "分解任务结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "申请加分的截至时间(申请加分)")
    private LocalDate ScoreAddEndDate;

    @ApiModelProperty(value = "申请加分表 0：未审核 1 审核通过 2 驳回")
    private Integer hasReviewer;

    @ApiModelProperty(value = "申请加分表集合 0：未审核 1 审核通过 2 驳回")
    private List<Integer> hasReviewerList;

    @ApiModelProperty(value = "申请加分接收人ID")
    private Long scoreAddReviewerId;

    @ApiModelProperty(value = "申请加分创建人ID")
    private Long scoreAddCreateId;

    @ApiModelProperty(value = "分解表的主键id")
    private Long bizTaskDecomposeId;

    @ApiModelProperty(value = "分解表责任人的姓名")
    private String dutyPeopleName;

    @ApiModelProperty(value = "当前页数")
    private Integer currentPage;

    @ApiModelProperty(value = "每页请求条数")
    private Integer pageSize;

    @ApiModelProperty(value = "0：未查看 1:已查看")
    private Integer taskScoreAddHasRead;

    @ApiModelProperty(value = "任务类型")
    private String taskType;

    @ApiModelProperty(value = "分数")
    private Double score;

    /**
     * 申请加分的内容
     */
    @ApiModelProperty(value = "申请加分的内容")
    private String scoreAddContent;

    @ApiModelProperty(value = "申请加分的理由")
    private String scoreAddReason;

    @ApiModelProperty(value = "驳回理由")
    private String refuseReason;

    @ApiModelProperty(value = "加分申请创建人")
    private String scoreAddCreateName;

    @ApiModelProperty(value = "加分申请创建时间")
    private String scoreAddCreateTime;

    /**
     * 0：未办结 1 办结
     */
    @ApiModelProperty(value = "0：未办结 1 办结")
    private Integer hasFinish;

}
