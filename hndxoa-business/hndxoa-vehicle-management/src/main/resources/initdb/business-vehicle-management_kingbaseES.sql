CREATE TABLE "public"."biz_vehicle_management" (
                                                   "id" int8 NOT NULL,
                                                   "vehicle_info" varchar(50 char) NOT NULL,
	"plate_number" varchar(32 char) NOT NULL,
	"driver_name" varchar(32 char) NOT NULL,
	"driver_mobile" varchar(32 char) NOT NULL,
	"limit_number" int4 NOT NULL,
	"vehicle_status" int4 NOT NULL,
	"create_time" timestamp(6) NULL,
	"deleted" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	CONSTRAINT "biz_vehicle_management_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_vehicle_management"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_vehicle_management"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_vehicle_management"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_vehicle_management"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_vehicle_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "public"."biz_vehicle_management"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_vehicle_management"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."biz_vehicle_management"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_vehicle_management"."create_name" IS '创建人名称';
COMMENT ON COLUMN "public"."biz_vehicle_management"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."biz_vehicle_management"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_vehicle_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."biz_vehicle_management"."vehicle_status" IS '车辆状态：未安排是0 已安排是1';
COMMENT ON COLUMN "public"."biz_vehicle_management"."limit_number" IS '可乘人数';
COMMENT ON COLUMN "public"."biz_vehicle_management"."driver_mobile" IS '联系电话';
COMMENT ON COLUMN "public"."biz_vehicle_management"."driver_name" IS '司机姓名';
COMMENT ON COLUMN "public"."biz_vehicle_management"."plate_number" IS '车牌信息';
COMMENT ON COLUMN "public"."biz_vehicle_management"."vehicle_info" IS '车俩信息';
COMMENT ON COLUMN "public"."biz_vehicle_management"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_vehicle_management" IS '车辆信息';
