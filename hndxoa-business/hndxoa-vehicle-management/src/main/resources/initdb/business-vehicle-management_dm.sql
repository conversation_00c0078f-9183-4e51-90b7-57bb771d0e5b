CREATE TABLE "myapp"."biz_vehicle_management"
(
"id" BIGINT NOT NULL,
"vehicle_info" VARCHAR(50) NOT NULL,
"plate_number" VARCHAR(32) NOT NULL,
"driver_name" VARCHAR(32) NOT NULL,
"driver_mobile" VARCHAR(32) NOT NULL,
"limit_number" INT NOT NULL,
"vehicle_status" INT NOT NULL,
"create_time" TIMESTAMP(0),
"deleted" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"department_id" BIGINT,
"department_name" VARCHAR(32),
"company_id" BIGINT,
"company_name" VARCHAR(32),
"update_by",  BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"tenant_id" BIGINT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_vehicle_management" IS '车辆信息';COMMENT ON COLUMN "myapp"."biz_vehicle_management"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."vehicle_info" IS '车俩信息';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."plate_number" IS '车牌信息';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."driver_name" IS '司机姓名';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."driver_mobile" IS '联系电话';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."limit_number" IS '可乘人数';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."vehicle_status" IS '车辆状态：未安排是0 已安排是1';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."department_name" IS '部门名称';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."company_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_vehicle_management"."tenant_id" IS '租户ID';




