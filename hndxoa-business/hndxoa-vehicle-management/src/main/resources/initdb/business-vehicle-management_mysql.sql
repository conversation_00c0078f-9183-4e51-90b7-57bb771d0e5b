CREATE TABLE `biz_vehicle_management` (
                                          `id` bigint NOT NULL COMMENT '主键ID',
                                          `vehicle_info` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车俩信息',
                                          `plate_number` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车牌信息',
                                          `driver_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '司机姓名',
                                          `driver_mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '联系电话',
                                          `limit_number` int NOT NULL COMMENT '可乘人数',
                                          `vehicle_status` int NOT NULL COMMENT '车辆状态：未安排是0 已安排是1',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                          `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                          `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                          `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                          `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
                                          `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                          `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                          `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                          `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='车辆信息';

