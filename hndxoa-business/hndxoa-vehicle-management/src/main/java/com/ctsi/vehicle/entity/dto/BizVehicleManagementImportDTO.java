package com.ctsi.vehicle.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Data
@ColumnWidth(14)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel(value = "BizVehicleManagementExcelxDTO对象", description = "车辆信息导入解析Excel")
public class BizVehicleManagementImportDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    @ExcelIgnore
    private Long id;

    /**
     * 车俩信息
     */
    @ApiModelProperty(value = "车俩信息")
    @ExcelProperty(value = "车俩信息",index = 0)
    private String vehicleInfo;

    /**
     * 车牌信息
     */
    @ApiModelProperty(value = "车牌信息")
    @ExcelProperty(value = "车牌信息",index = 1)
    private String plateNumber;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @ExcelProperty(value = "司机姓名",index =2)
    private String driverName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "司机联系电话",index =3)
    @ColumnWidth(17)
    private String driverMobile;

    /**
     * 可乘人数
     */
    @ApiModelProperty(value = "可乘人数")
    @ExcelProperty(value = "可乘人数",index =4)
    private Integer limitNumber;

    /**
     * 批量导入车辆信息失败的原因
     */
    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    @ColumnWidth(22)
    private String failedReason;

}
