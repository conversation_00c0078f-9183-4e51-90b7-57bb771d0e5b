package com.ctsi.vehicle.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.vehicle.entity.BizVehicleManagement;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementDTO;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementImportDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 车辆信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
public interface IBizVehicleManagementService extends SysBaseServiceI<BizVehicleManagement> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizVehicleManagementDTO> queryListPage(BizVehicleManagementDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizVehicleManagementDTO> queryList(BizVehicleManagementDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizVehicleManagementDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizVehicleManagementDTO create(BizVehicleManagementDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizVehicleManagementDTO entity);

    /**
     * 还车
     *
     * @param id
     * @return
     */
    int revert(Long id);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(List<Long> id);

    /**
     * 是否存在
     * <p>
     * existByBizVehicleManagementId
     *
     * @param code
     * @return
     */
    boolean existByBizVehicleManagementId(Long code);

    /**
     * 批量新增
     * <p>
     * create batch
     *
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<BizVehicleManagementImportDTO> dataList);

    /**
     * 查询部门数据，并导出到EXCEL
     *
     * @param id
     * @param response
     * @return
     */
    Boolean exportVehicle(List<Long> id, HttpServletResponse response);


}
