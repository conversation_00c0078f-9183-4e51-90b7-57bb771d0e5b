package com.ctsi.vehicle.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementDTO;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementImportDTO;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementIdsDTO;
import com.ctsi.vehicle.service.IBizVehicleManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizVehicleManagement")
@Api(value = "车辆信息", tags = "车辆信息接口")
public class BizVehicleManagementController extends BaseController {

    private static final String ENTITY_NAME = "bizVehicleManagement";

    @Autowired
    private IBizVehicleManagementService bizVehicleManagementService;


    /**
     * 批量导入车辆信息.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "批量导入车辆信息", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "批量导入车辆信息")
    // @PreAuthorize("@permissionService.hasPermi('cscp.tSysWorkRestDate.add')")
    public ResultVO createBatch(MultipartFile file) throws IOException {
        ArrayList<Boolean> results = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), BizVehicleManagementImportDTO.class, new PageReadListener<BizVehicleManagementImportDTO>(dataList -> {
            Boolean result = bizVehicleManagementService.insertBatch(dataList);
            results.add(result);
        })).sheet().doRead();
        if (results.contains(false)){
            return ResultVO.error(ResultCode.DATA_IS_WRONG);
        }else {
            return ResultVO.success(true);
        }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizVehicleManagement.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增车辆信息数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizVehicleManagement.add')")
    public ResultVO<BizVehicleManagementDTO> create(@Validated @RequestBody BizVehicleManagementDTO bizVehicleManagementDTO) throws IllegalAccessException {
        BizVehicleManagementDTO result = bizVehicleManagementService.create(bizVehicleManagementDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizVehicleManagement.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新车辆信息数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizVehicleManagement.update')")
    public ResultVO update(@Validated @RequestBody BizVehicleManagementDTO bizVehicleManagementDTO) {
	    Assert.notNull(bizVehicleManagementDTO.getId(), "general.IdNotNull");
        int count = bizVehicleManagementService.update(bizVehicleManagementDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  还车.
     */
    @PostMapping("/revert/{id}")
    @ApiOperation(value = "还车", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新车辆信息数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizVehicleManagement.update')")
    public ResultVO revert(@PathVariable @ApiParam(required = true) Long id) {
        Assert.notNull(id, "还车状态不能为空");
        int count = bizVehicleManagementService.revert(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除车辆信息数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizVehicleManagement.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizVehicleManagement.delete')")
    public ResultVO delete(@NotEmpty @RequestBody List<Long> id) {
        int count = bizVehicleManagementService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@NotBlank @PathVariable Long id) {
        BizVehicleManagementDTO bizVehicleManagementDTO = bizVehicleManagementService.findOne(id);
        return ResultVO.success(bizVehicleManagementDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizVehicleManagementPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizVehicleManagementDTO>> queryBizVehicleManagementPage(BizVehicleManagementDTO bizVehicleManagementDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizVehicleManagementService.queryListPage(bizVehicleManagementDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizVehicleManagement")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizVehicleManagementDTO>> queryBizVehicleManagement(BizVehicleManagementDTO bizVehicleManagementDTO) {
       List<BizVehicleManagementDTO> list = bizVehicleManagementService.queryList(bizVehicleManagementDTO);
       return ResultVO.success(new ResResult<BizVehicleManagementDTO>(list));
   }


    /**
     * 批量导出车辆信息
     * */
    @PostMapping("/exportVehicle")
    @ApiOperation(value = "车辆信息导出接口", notes = "传入参数")
    public ResultVO exportVehicle(@Validated @RequestBody BizVehicleManagementIdsDTO exportEntity, HttpServletResponse response) {
        if (Objects.isNull(exportEntity.getIds()) || exportEntity.getIds().size()==0) {
            throw new BusinessException("id不能为空");
        }
        Boolean bool = bizVehicleManagementService.exportVehicle(exportEntity.getIds(), response);
        return ResultVO.success(bool);
    }

}
