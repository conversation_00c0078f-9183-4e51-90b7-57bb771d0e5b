package com.ctsi.vehicle.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.constant.SysRegEx;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <p>
 * 车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_vehicle_management")
@ApiModel(value="BizVehicleManagement对象", description="车辆信息")
public class BizVehicleManagement extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 车俩信息
     */
    @ApiModelProperty(value = "车俩信息")
    @NotBlank(message = "车俩信息不能为空")
    private String vehicleInfo;


    /**
     * 车牌信息
     */
    @ApiModelProperty(value = "车牌信息")
    @Pattern(regexp = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$", message = "请输入正确的车牌信息")
    private String plateNumber;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @NotBlank(message = "司机姓名不能为空")
    private String driverName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @Pattern(regexp = SysRegEx.MOBILE_PATTERN, message = "手机号格式有误")
    private String driverMobile;

    /**
     * 可乘人数
     */
    @ApiModelProperty(value = "可乘人数")
    @NotBlank(message = "可乘人数不能为空")
    private Integer limitNumber;

    /**
     * 车辆状态
     */
    @ApiModelProperty(value = "车辆状态")
    @NotBlank(message = "车辆状态不能为空")
    private Integer vehicleStatus;


}
