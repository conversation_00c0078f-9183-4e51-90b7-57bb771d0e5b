package com.ctsi.vehicle.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysRegEx;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.ExcelCustomize;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import com.ctsi.vehicle.entity.BizVehicleManagement;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementDTO;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementExportDTO;
import com.ctsi.vehicle.entity.dto.BizVehicleManagementImportDTO;
import com.ctsi.vehicle.mapper.BizVehicleManagementMapper;
import com.ctsi.vehicle.service.IBizVehicleManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <p>
 * 车辆信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Slf4j
@Service
public class BizVehicleManagementServiceImpl extends SysBaseServiceImpl<BizVehicleManagementMapper, BizVehicleManagement> implements IBizVehicleManagementService {

    @Autowired
    private BizVehicleManagementMapper bizVehicleManagementMapper;


    @Autowired
    private IBizVehicleManagementService bizVehicleManagementService;

    @Autowired
    private ISysImportService iSysImportService;


    @Autowired
    private ExportToExcelService exportToExcelService;

    /**
     * 车辆状态，0：未安排，1：已安排
     */
    final private static Integer NOT_ARRANGE = 0;
    final private static Integer ARRANGED = 1;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizVehicleManagementDTO> queryListPage(BizVehicleManagementDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizVehicleManagement> queryWrapper = new LambdaQueryWrapper();

        IPage<BizVehicleManagement> pageData = bizVehicleManagementMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizVehicleManagementDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizVehicleManagementDTO.class));

        return new PageResult<BizVehicleManagementDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizVehicleManagementDTO> queryList(BizVehicleManagementDTO entityDTO) {
        LambdaQueryWrapper<BizVehicleManagement> queryWrapper = new LambdaQueryWrapper();
        List<BizVehicleManagement> listData = bizVehicleManagementMapper.selectList(queryWrapper);
        List<BizVehicleManagementDTO> BizVehicleManagementDTOList = ListCopyUtil.copy(listData, BizVehicleManagementDTO.class);
        return BizVehicleManagementDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizVehicleManagementDTO findOne(Long id) {
        BizVehicleManagement bizVehicleManagement = bizVehicleManagementMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizVehicleManagement, BizVehicleManagementDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizVehicleManagementDTO create(@Validated BizVehicleManagementDTO entityDTO) {

        // 验证导入的车牌号信息与数据库中是否重复
        LambdaQueryWrapper<BizVehicleManagement> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizVehicleManagement::getPlateNumber, entityDTO.getPlateNumber());
        BizVehicleManagement VehiclePlateNumber = bizVehicleManagementMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(VehiclePlateNumber)) {
            throw new BusinessException("车牌号信息重复");
        }
        // 验证导入的司机姓名与数据库中是否重复
        LambdaQueryWrapper<BizVehicleManagement> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(BizVehicleManagement::getDriverName, entityDTO.getDriverName());
        BizVehicleManagement VehicleDriverName = bizVehicleManagementMapper.selectOne(lambdaQueryWrapper1);
        if (ObjectUtil.isNotNull(VehicleDriverName)) {
            throw new BusinessException("司机姓名重复");
        }

        BizVehicleManagement bizVehicleManagement = BeanConvertUtils.copyProperties(entityDTO, BizVehicleManagement.class);
        save(bizVehicleManagement);
        return BeanConvertUtils.copyProperties(bizVehicleManagement, BizVehicleManagementDTO.class);
    }


    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(@Validated BizVehicleManagementDTO entity) {
        BizVehicleManagement bizVehicleManagement = BeanConvertUtils.copyProperties(entity, BizVehicleManagement.class);
        return bizVehicleManagementMapper.updateById(bizVehicleManagement);
    }

    /**
     * 还车
     *
     * @param id 根据车辆信息id，更新用车状态为未安排（0）
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int revert(Long id) {
        BizVehicleManagement bizVehicleManagement = bizVehicleManagementMapper.selectById(id);
        bizVehicleManagement.setVehicleStatus(NOT_ARRANGE);
        return bizVehicleManagementMapper.updateById(bizVehicleManagement);
    }

    /**
     * 删除和批量删除
     *
     * @param ids the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(List<Long> ids) {
        int result = 0;
        if (ObjectUtil.isNotNull(ids) && ids.size() != 0) {
            for (Long id : ids) {
                int deleteCount = bizVehicleManagementMapper.deleteById(id);
                result = result + deleteCount;
            }
        }
        return result;
    }


    /**
     * 验证是否存在
     *
     * @param BizVehicleManagementId
     * @return
     */
    @Override
    public boolean existByBizVehicleManagementId(Long BizVehicleManagementId) {
        if (BizVehicleManagementId != null) {
            LambdaQueryWrapper<BizVehicleManagement> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizVehicleManagement::getId, BizVehicleManagementId);
            List<BizVehicleManagement> result = bizVehicleManagementMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean insertBatch(List<BizVehicleManagementImportDTO> dataList) {
        log.info("BizVehicleManagementServiceImpl.insertBatch 读取到一条数据: {}", dataList);
        // 新建一个保存车辆信息导入失败记录的集合
        List<BizVehicleManagementImportDTO> failedList = new ArrayList<>();
        List<BizVehicleManagementDTO> insertList = new ArrayList<>();
        if (ObjectUtil.isNull(dataList) && dataList.size() == 0) {
            throw new BusinessException("导入表格为空");
        }
        // 由于导入的 Excel 中车辆状态为中文，需转换成 Ingter 类型
        String plateNumberRegexp = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$";
        for (BizVehicleManagementImportDTO vehicleManagementExcel : dataList) {
            BizVehicleManagementDTO bizVehicleManagementDTO = new BizVehicleManagementDTO();
            if (vehicleManagementExcel.getVehicleInfo() != null) {
                bizVehicleManagementDTO.setVehicleInfo(vehicleManagementExcel.getVehicleInfo());
            } else {
                vehicleManagementExcel.setFailedReason("车俩信息不能为空");
                failedList.add(vehicleManagementExcel);
                continue;
            }
            if (vehicleManagementExcel.getPlateNumber() != null && Pattern.matches(plateNumberRegexp, vehicleManagementExcel.getPlateNumber())) {
                bizVehicleManagementDTO.setPlateNumber(vehicleManagementExcel.getPlateNumber());
            } else {
                vehicleManagementExcel.setFailedReason("车牌信息不正确");
                failedList.add(vehicleManagementExcel);
                continue;
            }
            if (vehicleManagementExcel.getDriverName() != null) {
                bizVehicleManagementDTO.setDriverName(vehicleManagementExcel.getDriverName());
            } else {
                vehicleManagementExcel.setFailedReason("司机姓名不能为空");
                failedList.add(vehicleManagementExcel);
                continue;
            }
            if (vehicleManagementExcel.getDriverMobile() != null && Pattern.matches(SysRegEx.MOBILE_PATTERN, vehicleManagementExcel.getDriverMobile())) {
                bizVehicleManagementDTO.setDriverMobile(vehicleManagementExcel.getDriverMobile());
            } else {
                vehicleManagementExcel.setFailedReason("手机号格式有误");
                failedList.add(vehicleManagementExcel);
                continue;
            }
            if (vehicleManagementExcel.getLimitNumber() != null && vehicleManagementExcel.getLimitNumber() > 0) {
                bizVehicleManagementDTO.setLimitNumber(vehicleManagementExcel.getLimitNumber());
            } else {
                vehicleManagementExcel.setFailedReason("可乘人数不能为空或负数");
                failedList.add(vehicleManagementExcel);
                continue;
            }
            bizVehicleManagementDTO.setVehicleStatus(NOT_ARRANGE);
            insertList.add(bizVehicleManagementDTO);
        }
        for (BizVehicleManagementDTO vehicleManagementDTO : insertList) {
            try {
                // 插入单条数据到数据库中
                this.create(vehicleManagementDTO);
            } catch (BusinessException e) {
                BizVehicleManagementImportDTO bizVehicleManagementImportDTO = new BizVehicleManagementImportDTO();
                bizVehicleManagementImportDTO.setVehicleInfo(vehicleManagementDTO.getVehicleInfo());
                bizVehicleManagementImportDTO.setLimitNumber(vehicleManagementDTO.getLimitNumber());
                bizVehicleManagementImportDTO.setDriverMobile(vehicleManagementDTO.getDriverMobile());
                bizVehicleManagementImportDTO.setDriverName(vehicleManagementDTO.getDriverName());
                bizVehicleManagementImportDTO.setPlateNumber(vehicleManagementDTO.getPlateNumber());
                bizVehicleManagementImportDTO.setFailedReason(e.getMessage());
                failedList.add(bizVehicleManagementImportDTO);
            }
        }
        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dataList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.VEHICLE_MANAGEMENT_IMPORT));

        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
            return true;
        } else {
            // 保存导入记录，并上传Excel失败文件
            iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, BizVehicleManagementImportDTO.class, FileBasePathName.VEHICLE_MANAGEMENT_IMPORT);
            return false;
        }
    }

    /**
     * 批量导出车辆信息
     */
    @Override
    public Boolean exportVehicle(List<Long> ids, HttpServletResponse response) {
        ArrayList<BizVehicleManagementExportDTO> bizVehicleManagementExportDTOS = new ArrayList<>();
        LambdaQueryWrapper<BizVehicleManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BizVehicleManagement::getId, ids);
        List<BizVehicleManagement> bizVehicleManagements = bizVehicleManagementMapper.selectList(queryWrapper);
        for (BizVehicleManagement entity : bizVehicleManagements) {
            BizVehicleManagementExportDTO bizVehicleManagementExportDTO = new BizVehicleManagementExportDTO();
            bizVehicleManagementExportDTO.setVehicleInfo(entity.getVehicleInfo());
            bizVehicleManagementExportDTO.setPlateNumber(entity.getPlateNumber());
            bizVehicleManagementExportDTO.setDriverName(entity.getDriverName());
            bizVehicleManagementExportDTO.setDriverMobile(entity.getDriverMobile());
            bizVehicleManagementExportDTO.setLimitNumber(entity.getLimitNumber());
            bizVehicleManagementExportDTO.setStatus(NOT_ARRANGE == entity.getVehicleStatus().intValue() ? "未安排" : "已安排");
            bizVehicleManagementExportDTOS.add(bizVehicleManagementExportDTO);
        }
        ExcelCustomize excelCustomize = new ExcelCustomize();
        excelCustomize.setFileName("车辆信息管理");
        excelCustomize.setSheetName("导出信息");
        // 从第3行开始写
        excelCustomize.setRelativeHeadRowIndex(2);
        // 设置表头样式和信息
        excelCustomize.setHeader(new DetectionSheetWriteHandler());
        List<String> columnName = new ArrayList<>();
        columnName.add("id");
        excelCustomize.setExcludeColumnFiledNames(columnName);

        Boolean bool = true;
        try {
            bool = exportToExcelService.exportToExcelCustomize(bizVehicleManagementExportDTOS, BizVehicleManagementExportDTO.class, excelCustomize, response);
        } catch (IOException e) {
            e.printStackTrace();
            bool = false;
        }
        return bool;
    }

    /**
     * 自定义表头信息
     */
    public static class DetectionSheetWriteHandler implements SheetWriteHandler {
        String exportUserName = SecurityUtils.getCurrentRealName();

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            Sheet sheet = workbook.getSheetAt(0);
            //设置第一行标题
            Row row1 = sheet.createRow(0);
            row1.setHeight((short) 800);
            Cell row1Cell1 = row1.createCell(0);
            row1Cell1.setCellValue("车辆信息列表");
            CellStyle row1CellStyle = workbook.createCellStyle();
            row1CellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            row1CellStyle.setAlignment(HorizontalAlignment.CENTER);
            Font row1Font = workbook.createFont();
            row1Font.setBold(true);
            row1Font.setFontName("宋体");
            row1Font.setFontHeightInPoints((short) 18);
            row1CellStyle.setFont(row1Font);
            row1Cell1.setCellStyle(row1CellStyle);
            // 合并单元格，起始行,结束行,起始列,结束列
            sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, 5));

            // 设置第二行标题
            Row row2 = sheet.createRow(1);
            row2.setHeight((short) 400);
            Cell row2Cell1 = row2.createCell(0);
            row2Cell1.setCellValue("导出人：" + exportUserName);
            CellStyle row2CellStyle = workbook.createCellStyle();
            row2CellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            row2CellStyle.setAlignment(HorizontalAlignment.RIGHT);
            Font row2Font = workbook.createFont();
            row2Font.setFontName("宋体");
            row2Font.setFontHeightInPoints((short) 10);
            row2CellStyle.setFont(row2Font);
            row2Cell1.setCellStyle(row2CellStyle);
            sheet.addMergedRegionUnsafe(new CellRangeAddress(1, 1, 0, 5));

        }
    }
}