package com.ctsi.vehicle.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Data
@ColumnWidth(14)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel(value = "BizVehicleManagementExportDTO对象", description = "车辆信息导出解析Excel")
public class BizVehicleManagementExportDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    @ExcelIgnore
    private Long id;

    /**
     * 车俩信息
     */
    @ApiModelProperty(value = "车俩信息")
    @ExcelProperty(value = "车俩信息", index = 0)
    private String vehicleInfo;

    /**
     * 车牌信息
     */
    @ApiModelProperty(value = "车牌信息")
    @ExcelProperty(value = "车牌信息", index = 1)
    private String plateNumber;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @ExcelProperty(value = "司机姓名", index = 2)
    private String driverName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "司机联系电话", index = 3)
    @ColumnWidth(17)
    private String driverMobile;

    /**
     * 可乘人数
     */
    @ApiModelProperty(value = "可乘人数")
    @ExcelProperty(value = "可乘人数", index = 4)
    private Integer limitNumber;

    /**
     * 车辆状态：未安排是0 已安排是1
     */
    @ApiModelProperty(value = "车辆状态：未安排是0 已安排是1")
    @ExcelProperty(value = "状态", index = 5)
    private String status;
}
