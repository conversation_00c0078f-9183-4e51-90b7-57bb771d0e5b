package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizBoxFileDTO对象", description="")
public class BizBoxFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号id
     */
    @ApiModelProperty(value = "盒号id")
    private Long boxId;

    /**
     * 盒号|档案盒
     */
    @ApiModelProperty(value = "盒号|档案盒")
    private String boxNumber;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    private Long officialFileId;

    /**
     * 归档文件路径
     */
    @ApiModelProperty(value = "归档文件路径")
    private String filePath;


}
