package com.ctsi.archive.service;

import com.ctsi.archive.entity.BizArchiveBox;
import com.ctsi.archive.entity.dto.BizArchiveBoxDTO;
import com.ctsi.archive.entity.dto.BizArchiveBoxResultDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 档案盒管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
public interface IBizArchiveBoxService extends SysBaseServiceI<BizArchiveBox> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizArchiveBoxResultDTO> queryListPage(BizArchiveBoxDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizArchiveBoxResultDTO> queryList(BizArchiveBoxDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizArchiveBoxDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizArchiveBoxDTO create(BizArchiveBoxDTO entity);

    /**
     * 保存
     *
     * @param entity
     * @return
     */
    int updateWithEntity(BizArchiveBox entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizArchiveBoxDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizArchiveBoxId
     * @param code
     * @return
     */
    boolean existByBizArchiveBoxId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizArchiveBoxDTO> dataList);


}
