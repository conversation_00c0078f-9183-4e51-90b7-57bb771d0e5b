package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <p>
 * 归档管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizArchiveFileDTO对象", description="归档管理")
public class BizArchiveFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号id
     */
    @ApiModelProperty(value = "盒号id")
    @NotNull
    private Long boxId;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    @NotNull
    private Long officialFileId;

    /**
     * 盒号|档案盒
     */
    @ApiModelProperty(value = "盒号|档案盒")
    @NotNull
    private String boxNumber;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    @NotNull
    private String title;

    /**
     * 公文类型（报告、其他...）
     */
    @ApiModelProperty(value = "公文类型（报告、其他...）")
    private String documentType;

    /**
     * 办件号
     */
    @ApiModelProperty(value = "办件号")
    private Integer transactNumber;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    @NotNull
    private String referenceNumber;

    /**
     * 归属部门
     */
    @ApiModelProperty(value = "归属部门")
    private String archiveDepartment;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "机构(归档部门)")
    private String orgName;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate archiveDate;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageCount;

    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、呈批件）")
    private String archiveResource;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;

    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 是否附件
     */
    @ApiModelProperty(value = "是否附件")
    private String annex;

    /**
     * 作废日期
     */
    @ApiModelProperty(value = "作废日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate discardDate;

    /**
     * 是否作废 0：未作废 1：作废
     */
    @ApiModelProperty(value = "是否作废 0：未作废 1：已作废")
    private Integer discard;

    @ApiModelProperty(value = "创建人名称")
    private String createName;
}
