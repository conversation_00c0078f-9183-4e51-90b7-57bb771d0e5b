package com.ctsi.archive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 归档管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_archive_file")
@ApiModel(value="BizArchiveFile对象", description="归档管理")
public class BizArchiveFile extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号id
     */
    @ApiModelProperty(value = "盒号id")
    private Long boxId;

    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    private Long officialFileId;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文类型（报告、其他...）
     */
    @ApiModelProperty(value = "公文类型（报告、其他...）")
    private String documentType;

    /**
     * 办件号
     */
    @ApiModelProperty(value = "办件号")
    private Integer transactNumber;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 归属部门
     */
    @ApiModelProperty(value = "归属部门")
    private String archiveDepartment;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "机构(归档部门)")
    private String orgName;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 创建日期（公文流程已完结日期）
     */
    @ApiModelProperty(value = "创建日期（公文流程已完结日期）")
    private LocalDate createDate;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    private LocalDate archiveDate;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageCount;

    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、呈批件）")
    private String archiveResource;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;

    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 是否附件
     */
    @ApiModelProperty(value = "是否附件")
    private String annex;

    /**
     * 作废日期
     */
    @ApiModelProperty(value = "作废日期 yyyy-MM-dd")
    private LocalDate discardDate;

    /**
     * 是否作废 0：未作废 1：作废
     */
    @ApiModelProperty(value = "是否作废 0：未作废 1：作废")
    private Integer discard;

    /**
     * 是否完全删除 0：否 1：是
     */
    @ApiModelProperty(value = "是否完全删除 0：否 1：是")
    private Integer removeStatus;

    /**
     * 是否归档 0：否 1：是
     */
    @ApiModelProperty(value = "是否归档 0：否 1：是")
    private Integer archiveStatus;
}
