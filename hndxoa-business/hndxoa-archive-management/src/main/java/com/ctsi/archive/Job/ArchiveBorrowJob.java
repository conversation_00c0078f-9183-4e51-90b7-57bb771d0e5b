/*
package com.ctsi.archive.Job;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.archive.entity.BizArchiveBorrow;
import com.ctsi.archive.service.IBizArchiveBorrowService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

*/
/**
 * <AUTHOR>
 * @Description 档案借阅 到期自动归还定时任务
 * @date 2022/8/8
 *//*

@Component
public class ArchiveBorrowJob {
    private static Logger logger = LoggerFactory.getLogger(ArchiveBorrowJob.class);

    @Autowired
    private IBizArchiveBorrowService bizArchiveBorrowService;

    */
/**
     * 1表示已归还
     *//*

    private static final Integer RESTORED = 1;

    */
/**
     * 根据档案归还日期自动归还已借阅档案
     *
     * @throws Exception
     *//*

    @XxlJob("autoReturnArchiveStatus")
    public void autoEnableAttendanceStatus() throws Exception {
        logger.info("定时任务：根据档案归还日期自动归还已借阅档案开始");
        XxlJobHelper.log("定时任务：根据档案归还日期自动归还已借阅档案开始");
        LambdaUpdateWrapper<BizArchiveBorrow> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(BizArchiveBorrow::getRestoreDate, LocalDate.now())
                .set(BizArchiveBorrow::getIsRestore, RESTORED);
        bizArchiveBorrowService.update(null, updateWrapper);
        logger.info("定时任务：根据档案归还日期自动归还已借阅档案结束");
    }
}
*/
