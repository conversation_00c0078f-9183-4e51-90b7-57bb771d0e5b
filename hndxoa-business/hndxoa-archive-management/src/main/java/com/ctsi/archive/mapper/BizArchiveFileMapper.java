package com.ctsi.archive.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.archive.entity.BizArchiveFile;
import com.ctsi.archive.entity.dto.BizArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizToArchiveFileDTO;
import com.ctsi.archive.entity.dto.FormModelDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 归档管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
public interface BizArchiveFileMapper extends MybatisBaseMapper<BizArchiveFile> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<BizToArchiveFileDTO> selectToArchiveList(IPage<BizToArchiveFileDTO> page,
                                                   @Param("toArchive") BizArchiveFile bizArchiveFile,
                                                   @Param("excludeIds") List<Long> excludeIds);

    @InterceptorIgnore(tenantLine = "true")
    BizArchiveFileDTO selectOneOffice(@Param("id") Long id);

    /**
     * 查询公文流程结束日期
     * */
    @InterceptorIgnore(tenantLine = "true")
    List<BizToArchiveFileDTO> selectProcEndTime(@Param("list")List<Long> list);

    @InterceptorIgnore(tenantLine = "true")
    List<FormModelDTO> getModelByArchivist();
}
