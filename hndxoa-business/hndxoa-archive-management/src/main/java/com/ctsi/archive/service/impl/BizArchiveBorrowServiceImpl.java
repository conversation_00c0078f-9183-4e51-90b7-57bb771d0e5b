package com.ctsi.archive.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.archive.entity.BizArchiveBorrow;
import com.ctsi.archive.entity.BizArchiveFile;
import com.ctsi.archive.entity.dto.BizArchiveBorrowDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowRequestDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowResultDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowVerifyDTO;
import com.ctsi.archive.mapper.BizArchiveBorrowMapper;
import com.ctsi.archive.mapper.BizArchiveFileMapper;
import com.ctsi.archive.service.IBizArchiveBorrowService;
import com.ctsi.archive.service.IBizArchiveFileService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 档案借阅管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Slf4j
@Service
public class BizArchiveBorrowServiceImpl extends SysBaseServiceImpl<BizArchiveBorrowMapper, BizArchiveBorrow> implements IBizArchiveBorrowService {

    @Autowired
    private BizArchiveBorrowMapper bizArchiveBorrowMapper;
    @Autowired
    private BizArchiveFileMapper bizArchiveFileMapper;
    @Autowired
    private IBizArchiveFileService bizArchiveFileService;

    /**
     * 0表示未归还
     */
    private static final Integer NOT_RESTORE = 0;
    /**
     * 1表示已归还
     */
    private static final Integer RESTORED = 1;

    /**
     * 2表示借阅审核中
     */
    private static final Integer VERIFY_APPROVAL = 2;
    /**
     * 1表示同意借阅
     */
    private static final Integer APPROVAL = 0;
    /**
     * 1表示已归档
     */
    private static final Integer ARCHIVE = 1;

    /**
     * 借阅申请 分页查询多条数据.
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizArchiveBorrowRequestDTO> queryBizArchiveBorrowRequestPage(BizArchiveBorrowRequestDTO entityDTO, BasePageForm basePageForm) {
        // 查询未归还或审核中的档案
        LambdaQueryWrapper<BizArchiveBorrow> queryWrapperBorrow = new LambdaQueryWrapper();
        queryWrapperBorrow.or().eq(BizArchiveBorrow::getIsRestore, NOT_RESTORE);
        queryWrapperBorrow.or().eq(BizArchiveBorrow::getIsApproval, VERIFY_APPROVAL);
        List<BizArchiveBorrow> bizArchiveBorrowList = bizArchiveBorrowMapper.selectList(queryWrapperBorrow);
        // 获取所有未归还、审核中的文档的fileId集合
        List<Long> archiveFileIdList = bizArchiveBorrowList.stream().map(i -> i.getOfficialFileId()).collect(Collectors.toList());
        // 查询所有已归档可请借阅的公文
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getBoxNumber()), BizArchiveFile::getBoxNumber, entityDTO.getBoxNumber());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveFile::getTitle, entityDTO.getTitle());
        queryWrapper.like(ObjectUtil.isNotNull(entityDTO.getTransactNumber()), BizArchiveFile::getTransactNumber, entityDTO.getTransactNumber());
        queryWrapper.eq(BizArchiveFile::getArchiveStatus, ARCHIVE);
        IPage<BizArchiveFile> pageData = bizArchiveFileMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizArchiveBorrowRequestDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizArchiveBorrowRequestDTO.class));
        // 给未归还、审核中的档案设置归还状态为0，表示为归还
        data.getRecords().stream().forEach(i -> {
            i.setIsRestore(RESTORED);
            if (archiveFileIdList.contains(i.getId())) {
                i.setIsRestore(NOT_RESTORE);
            }
        });
        return new PageResult<BizArchiveBorrowRequestDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 借阅审核 分页查询多条数据.
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizArchiveBorrowDTO> queryBizArchiveBorrowVerifyPage(BizArchiveBorrowDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizArchiveBorrow> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveBorrow::getTitle, entityDTO.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getReferenceNumber()), BizArchiveBorrow::getReferenceNumber, entityDTO.getReferenceNumber());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getCreateName()), BizArchiveBorrow::getCreateName, entityDTO.getCreateName());
        queryWrapper.eq(BizArchiveBorrow::getIsApproval, VERIFY_APPROVAL);
        queryWrapper.orderByDesc(BizArchiveBorrow::getCreateTime);
        IPage<BizArchiveBorrow> pageData = bizArchiveBorrowMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizArchiveBorrowDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizArchiveBorrowDTO.class));

        return new PageResult<BizArchiveBorrowDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 借阅结果 分页查询多条数据.
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizArchiveBorrowResultDTO> queryBizArchiveBorrowResultPage(BizArchiveBorrowDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizArchiveBorrow> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveBorrow::getTitle, entityDTO.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getReferenceNumber()), BizArchiveBorrow::getReferenceNumber, entityDTO.getReferenceNumber());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getCreateName()), BizArchiveBorrow::getCreateName, entityDTO.getCreateName());
        // 只能查询当前用户部门的借阅结果
        queryWrapper.eq(BizArchiveBorrow::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.ne(BizArchiveBorrow::getIsApproval, VERIFY_APPROVAL);
        queryWrapper.orderByAsc(BizArchiveBorrow::getIsRestore, BizArchiveBorrow::getIsApproval)
                .orderByDesc(BizArchiveBorrow::getBorrowDate);
        IPage<BizArchiveBorrow> pageData = bizArchiveBorrowMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        // 查询归档文件详情
        List<Long> archiveFileIds = pageData.getRecords().stream().map(i -> i.getOfficialFileId()).collect(Collectors.toList());
        //返回
        IPage<BizArchiveBorrowResultDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizArchiveBorrowResultDTO.class));
        if (CollectionUtils.isNotEmpty(archiveFileIds)) {
            // 查询已借阅文档在已归档表中的实体类
            LambdaQueryWrapper<BizArchiveFile> bizArchiveFileLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bizArchiveFileLambdaQueryWrapper.in(BizArchiveFile::getId, archiveFileIds);
            List<BizArchiveFile> bizArchiveFiles = bizArchiveFileService.selectListNoAdd(bizArchiveFileLambdaQueryWrapper);
            // 将bizArchiveFiles已归档集合转成Map，减少两层循环遍历
            Map<Long, List<BizArchiveFile>> archiveMap = bizArchiveFiles.stream().collect(Collectors.groupingBy(BizArchiveFile::getId));
            // 给已借阅文档添加密级、页数、正文、附件和备注等信息
            data.getRecords().stream().forEach(y -> {
                List<BizArchiveFile> archiveFiles = archiveMap.get(y.getOfficialFileId());
                if (CollectionUtils.isNotEmpty(archiveFiles)) {
                    y.setSecret(archiveFiles.get(0).getSecret());
                    y.setPageCount(archiveFiles.get(0).getPageCount());
                    y.setDocument(archiveFiles.get(0).getDocument());
                    y.setAnnex(archiveFiles.get(0).getAnnex());
                    y.setComments(archiveFiles.get(0).getComments());
                    y.setArchiveDate(archiveFiles.get(0).getArchiveDate());
                }
            });
        }
        return new PageResult<BizArchiveBorrowResultDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizArchiveBorrowDTO> queryList(BizArchiveBorrowDTO entityDTO) {
        LambdaQueryWrapper<BizArchiveBorrow> queryWrapper = new LambdaQueryWrapper();
        List<BizArchiveBorrow> listData = bizArchiveBorrowMapper.selectList(queryWrapper);
        List<BizArchiveBorrowDTO> BizArchiveBorrowDTOList = ListCopyUtil.copy(listData, BizArchiveBorrowDTO.class);
        return BizArchiveBorrowDTOList;
    }

    /**
     * 单个查询 档案详情
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizArchiveBorrowResultDTO findOne(Long id) {
        BizArchiveBorrow bizArchiveBorrow = bizArchiveBorrowMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizArchiveBorrow, BizArchiveBorrowResultDTO.class);
    }


    /**
     * 保存提交
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizArchiveBorrowVerifyDTO borrow(BizArchiveBorrowVerifyDTO entityDTO) {
        // 查找该档案是否审核中或未归
        BizArchiveBorrow entity = bizArchiveBorrowMapper.selectOneOnlyAddTenantId(
                new LambdaQueryWrapper<BizArchiveBorrow>()
                        .eq(BizArchiveBorrow::getOfficialFileId, entityDTO.getId())
                        .eq(BizArchiveBorrow::getIsRestore, NOT_RESTORE));
        // 检查当前档案是否未归还或审核中
        if (ObjectUtil.isNotNull(entity)) {
            throw new BusinessException("档案借阅中，请在该档案归还后再进行借阅申请！");
        }
        BizArchiveFile bizArchiveFile = bizArchiveFileService.getById(entityDTO.getId());
        if (ObjectUtil.isNull(bizArchiveFile)) {
            throw new BusinessException("该档案公文没有数据！");
        }

        BizArchiveBorrow bizArchiveBorrow = BeanConvertUtils.copyProperties(entityDTO, BizArchiveBorrow.class);
        assert bizArchiveBorrow != null;
        // 借阅状态设置为：审核中
        bizArchiveBorrow.setIsApproval(VERIFY_APPROVAL);
        bizArchiveBorrow.setIsRestore(NOT_RESTORE);
        // 设置归档文件id，方便后续查看借阅结果查询
        bizArchiveBorrow.setOfficialFileId(bizArchiveFile.getOfficialFileId());
        // 借阅审核界面没有（借阅人）字段，需后端自动保存
        bizArchiveBorrow.setBorrowerDepartment(SecurityUtils.getCurrentCscpUserDetail().getDepartmentName());
        bizArchiveBorrow.setId(null);
        // 借阅日期不为当天则设置为当天
        if (!bizArchiveBorrow.getBorrowDate().equals(LocalDate.now())) {
            bizArchiveBorrow.setBorrowDate(LocalDate.now());
        }
        save(bizArchiveBorrow);
        return BeanConvertUtils.copyProperties(bizArchiveBorrow, BizArchiveBorrowVerifyDTO.class);
    }

    /**
     * 归还档案
     *
     * @param id 主键id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int restore(Long id) {
        BizArchiveBorrow entity = bizArchiveBorrowMapper.selectById(id);
        // 检查是否为当前登录用户归还
        if (!entity.getCreateBy().equals(SecurityUtils.getCurrentUserId())) {
            throw new BusinessException("只能归还本人借阅的档案！");
        }
        entity.setIsRestore(RESTORED);
        BizArchiveBorrow bizArchiveBorrow = BeanConvertUtils.copyProperties(entity, BizArchiveBorrow.class);
        return bizArchiveBorrowMapper.updateById(bizArchiveBorrow);
    }

    /**
     * 修改 借阅审核
     *
     * @param entityDTO the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizArchiveBorrowVerifyDTO entityDTO) {
        // 检查归还日期是否有误
        if (entityDTO.getRestoreDate().isBefore(LocalDate.now())) {
            throw new BusinessException("归还日期设置错误，不能是过去的日期！");
        }
        BizArchiveBorrow entity = bizArchiveBorrowMapper.selectById(entityDTO.getId());
        // 当借阅审核意见为同意，设置归还状态为未归还，
        // 当借阅审核意见为驳回，则该字段设置为空。
        entity.setIsRestore(entityDTO.getIsApproval().equals(APPROVAL) ? NOT_RESTORE : RESTORED);
        // 设置同意 or 驳回
        entity.setIsApproval(entityDTO.getIsApproval());
        entity.setRestoreDate(entityDTO.getRestoreDate());
        entity.setIsDownload(entityDTO.getIsDownload());
        BizArchiveBorrow bizArchiveBorrow = BeanConvertUtils.copyProperties(entity, BizArchiveBorrow.class);
        return bizArchiveBorrowMapper.updateById(bizArchiveBorrow);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizArchiveBorrowMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizArchiveBorrowId
     * @return
     */
    @Override
    public boolean existByBizArchiveBorrowId(Long BizArchiveBorrowId) {
        if (BizArchiveBorrowId != null) {
            LambdaQueryWrapper<BizArchiveBorrow> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizArchiveBorrow::getId, BizArchiveBorrowId);
            List<BizArchiveBorrow> result = bizArchiveBorrowMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizArchiveBorrowDTO> dataList) {
        List<BizArchiveBorrow> result = ListCopyUtil.copy(dataList, BizArchiveBorrow.class);
        return saveBatch(result);
    }


}
