package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 待销毁公文
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizToDiscardFileDTO对象", description="待销毁公文")
public class BizToDiscardFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    private Long officialFileId;


    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文类型（报告、其他...）
     */
    @ApiModelProperty(value = "公文类型（报告、其他...）")
    private String documentType;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 归属部门
     */
    @ApiModelProperty(value = "归属部门")
    private String archiveDepartment;


    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、呈批件）")
    private String archiveResource;


    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 是否附件
     */
    @ApiModelProperty(value = "是否附件")
    private String annex;

    /**
     * 作废日期
     */
    @ApiModelProperty(value = "作废日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate discardDate;

    /**
     * 是否作废 0：未作废 1：作废
     */
    @ApiModelProperty(value = "是否作废 0：未作废 1：已作废")
    private Integer discard;
}
