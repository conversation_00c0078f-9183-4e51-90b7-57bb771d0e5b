package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 借阅结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizArchiveBorrowResultDTO 对象", description="借阅申请详情")
public class BizArchiveBorrowResultDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    private Long officialFileId;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 借阅人(借阅部门)
     */
    @ApiModelProperty(value = "借阅人(借阅部门)")
    private String borrowerDepartment;

    /**
     * 借阅日期 申请日期
     */
    @ApiModelProperty(value = "借阅日期")
    private LocalDate borrowDate;


    /**
     * 归还日期
     */
    @ApiModelProperty(value = "归还日期")
    private LocalDate restoreDate;

    /**
     * 是否批准 0：同意 1：驳回 2：审核中
     */
    @ApiModelProperty(value = "是否批准 0：同意 1：驳回 2：审核中")
    private Integer isApproval;

    /**
     * 是否归还 0：未归还 1：已归还
     */
    @ApiModelProperty(value = "是否归还 0：未归还 1：已归还")
    private Integer isRestore;

    /**
     * 下载申请 0：是，1：否
     */
    @ApiModelProperty(value = "下载申请 0：是，1：否")
    private Integer isDownload;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "归档部门")
    private String orgName;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate archiveDate;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageCount;

    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 是否附件
     */
    @ApiModelProperty(value = "是否附件")
    private String annex;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "创建人名称")
    private String createName;
}
