package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 借阅申请
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizArchiveBorrowRequestDTO对象", description="借阅申请")
public class BizArchiveBorrowRequestDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 办件号
     */
    @ApiModelProperty(value = "办件号")
    private Integer transactNumber;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "归档部门")
    private String orgName;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate archiveDate;

    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、呈批件）")
    private String archiveResource;

    /**
     * 是否归还 0：未归还 1：已归还
     */
    @ApiModelProperty(value = "是否归还 0：未归还 1：已归还")
    private Integer isRestore;

    /**
     * 下载申请 0：是，1：否
     */
    @ApiModelProperty(value = "下载申请 0：是，1：否")
    private Integer isDownload;

    /**
     * 借阅理由
     */
    @ApiModelProperty(value = "借阅理由")
    private String reason;
}
