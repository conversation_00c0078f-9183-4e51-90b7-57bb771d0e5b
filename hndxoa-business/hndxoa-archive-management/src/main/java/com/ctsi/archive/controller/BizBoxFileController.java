package com.ctsi.archive.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.archive.entity.BizBoxFile;
import com.ctsi.archive.entity.dto.BizBoxFileDTO;
import com.ctsi.archive.service.IBizBoxFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizBoxFile")
@Api(value = "", tags = "接口")
public class BizBoxFileController extends BaseController {

    private static final String ENTITY_NAME = "bizBoxFile";

    @Autowired
    private IBizBoxFileService bizBoxFileService;



    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizBoxFile.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizBoxFile.add')")
    public ResultVO createBatch(@RequestBody List<BizBoxFileDTO> bizBoxFileList) {
       Boolean  result = bizBoxFileService.insertBatch(bizBoxFileList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizBoxFile.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizBoxFile.add')")
    public ResultVO<BizBoxFileDTO> create(@RequestBody BizBoxFileDTO bizBoxFileDTO)  {
        BizBoxFileDTO result = bizBoxFileService.create(bizBoxFileDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizBoxFile.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizBoxFile.update')")
    public ResultVO update(@RequestBody BizBoxFileDTO bizBoxFileDTO) {
	    Assert.notNull(bizBoxFileDTO.getId(), "general.IdNotNull");
        int count = bizBoxFileService.update(bizBoxFileDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizBoxFile.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizBoxFile.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizBoxFileService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizBoxFileDTO bizBoxFileDTO = bizBoxFileService.findOne(id);
        return ResultVO.success(bizBoxFileDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizBoxFilePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizBoxFileDTO>> queryBizBoxFilePage(BizBoxFileDTO bizBoxFileDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizBoxFileService.queryListPage(bizBoxFileDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizBoxFile")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizBoxFileDTO>> queryBizBoxFile(BizBoxFileDTO bizBoxFileDTO) {
       List<BizBoxFileDTO> list = bizBoxFileService.queryList(bizBoxFileDTO);
       return ResultVO.success(new ResResult<BizBoxFileDTO>(list));
   }

}
