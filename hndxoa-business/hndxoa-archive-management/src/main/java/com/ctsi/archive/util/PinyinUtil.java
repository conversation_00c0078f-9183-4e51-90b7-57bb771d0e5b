package com.ctsi.archive.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

public class PinyinUtil {
    /**
     * @param chinese (字符串 汉字)
     * @return 汉字转拼音 其它字符不变
     */
    public static String getAllSpell(String chinese){
        StringBuffer buffer = new StringBuffer();
        HanyuPinyinOutputFormat formart = new HanyuPinyinOutputFormat();
        formart.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        formart.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        formart.setVCharType(HanyuPinyinVCharType.WITH_V);
        char[] array = chinese.trim().toCharArray();
        try {
            for (int i = 0;i < array.length; i++) {
                char t = array[i];
                //匹配是否是中文
                if(Character.toString(t).matches("[\\u4e00-\\u9fa5]")){
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(t,formart);
                    buffer.append(temp[0]);
                }else{
                    buffer.append(t);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {

        }
        return buffer.toString().replaceAll("\\W", "").trim();
    }

    /**
     * 获取汉字串拼音首字母，英文字符不变
     * @param chinese 汉字串
     * @return 汉语拼音首字母小写  大写可以自动转换
     */
    public static String getFirstSpell(String chinese) {
        StringBuffer buffer = new StringBuffer();
        char[] array = chinese.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (int i = 0; i < array.length; i++) {
            if (array[i] > 128) {
                try {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(array[i], defaultFormat);
                    if (temp != null) {
                        // 截取首字母 小写
//                        buffer.append(temp[0].charAt(0));
                        // 字母大写
                        char charAt = temp[0].charAt(0);
                        charAt -= 32; // 改变字符的编码值
                        buffer.append(charAt);
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                buffer.append(array[i]);
            }
        }
        return buffer.toString().replaceAll("\\W", "").trim();
    }
}
