package com.ctsi.archive.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 文件档案导出
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Data
@ColumnWidth(14)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel(value="BizArchiveFileExportDTO对象", description="文件档案导出Excel")
public class BizArchiveFileExportDTO {

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    @ExcelIgnore
    private Long id;

    /**
     * 全宗号
     */
    @ApiModelProperty(value = "全宗号")
    @ExcelProperty(value = "全宗号", index = 0)
    private String dossierNumber;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份", index = 1)
    private String dossierYear;

    /**
     * 期限
     */
    @ApiModelProperty(value = "期限")
    @ExcelProperty(value = "期限", index = 2)
    private String duration;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "机构")
    @ExcelProperty(value = "机构", index = 3)
    private String orgName;

    /**
     * 办件号
     */
    @ApiModelProperty(value = "办件号")
    @ExcelProperty(value = "办件号", index = 4)
    private Integer transactNumber;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @ExcelProperty(value = "负责人", index = 5)
    private String principal;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    @ExcelProperty(value = "文号", index = 6)
    private String referenceNumber;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @ExcelProperty(value = "标题", index = 7)
    private String title;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    @ExcelProperty(value = "归档日期", index = 8)
    private String archiveDate;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    @ExcelProperty(value = "页数", index = 9)
    private Integer pageCount;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "密级")
    @ExcelProperty(value = "密级", index = 10)
    private String secret;

    /**
     * 盒号
     */
    @ApiModelProperty(value = "盒号")
    @ExcelProperty(value = "盒号", index = 11)
    @ColumnWidth(28)
    private String boxNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注", index = 12)
    private String comments;
}
