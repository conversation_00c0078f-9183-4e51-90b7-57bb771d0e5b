package com.ctsi.archive.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.archive.entity.BizArchiveBox;
import com.ctsi.archive.entity.BizArchiveFile;
import com.ctsi.archive.entity.BizBoxFile;
import com.ctsi.archive.entity.dto.*;
import com.ctsi.archive.mapper.BizArchiveBoxMapper;
import com.ctsi.archive.mapper.BizArchiveFileMapper;
import com.ctsi.archive.mapper.BizBoxFileMapper;
import com.ctsi.archive.service.IBizArchiveBoxService;
import com.ctsi.archive.service.IBizArchiveFileService;
import com.ctsi.archive.util.PinyinUtil;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.DateUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpDocumentFileMapper;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.model.ExcelCustomize;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 档案归档管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@Slf4j
@Service
public class BizArchiveFileServiceImpl extends SysBaseServiceImpl<BizArchiveFileMapper, BizArchiveFile> implements IBizArchiveFileService {

    @Autowired
    private BizArchiveFileMapper bizArchiveFileMapper;

    @Autowired
    private BizArchiveBoxMapper bizArchiveBoxMapper;
    @Autowired
    private IBizArchiveBoxService bizArchiveBoxService;
    @Autowired
    private ExportToExcelService exportToExcelService;
    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;
    @Autowired
    private CscpDocumentFileMapper cscpDocumentFileMapper;
    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;
    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;
    @Autowired
    private BizBoxFileServiceImpl bizBoxFileServiceImpl;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;
    @Autowired
    private BizBoxFileMapper bizBoxFileMapper;

    /**
     * 1表示完全删除
     */
    private static final Integer REMOVE = 1;
    /**
     * 0表示未删除
     */
    private static final Integer UN_REMOVE = 0;
    /**
     * 1表示归档作废
     */
    private static final Integer DISCARD = 1;
    /**
     * 0表示未作废
     */
    private static final Integer VALID = 0;
    /**
     * 1表示已归档
     */
    private static final Integer ARCHIVE = 1;
    /**
     * 0表示未归档
     */
    private static final Integer UN_ARCHIVE = 0;

    /**
     * 已归档管理 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizArchiveFileDTO> queryBizArchiveFilePage(BizArchiveFileDTO entityDTO, BasePageForm basePageForm) {
        //查询归档公文
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper<BizArchiveFile>();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getBoxNumber()), BizArchiveFile::getBoxNumber, entityDTO.getBoxNumber());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveFile::getTitle, entityDTO.getTitle());
        queryWrapper.like(ObjectUtil.isNotNull(entityDTO.getTransactNumber()), BizArchiveFile::getTransactNumber, entityDTO.getTransactNumber());
        queryWrapper.eq(BizArchiveFile::getArchiveStatus, ARCHIVE);
        queryWrapper.orderByDesc(BizArchiveFile:: getCreateTime);
        IPage pageData = bizArchiveFileMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizArchiveFileDTO.class));
        return new PageResult<BizArchiveFileDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 待归档公文 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizToArchiveFileDTO> queryToArchiveFileListPage(BizToArchiveFileDTO entityDTO, BasePageForm basePageForm) {
        BizArchiveFile bizArchiveFile = BeanConvertUtils.copyProperties(entityDTO, BizArchiveFile.class);
        // 按单位隔离，进查询本单位的发文、收文、呈批件
        bizArchiveFile.setCompanyId(SecurityUtils.getCurrentCompanyId());

        // 查询已作废、已完全删除、已归档文件
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.or().eq(BizArchiveFile::getArchiveStatus, ARCHIVE)
                .or().eq(BizArchiveFile::getDiscard, DISCARD)
                .or().eq(BizArchiveFile::getRemoveStatus, REMOVE);
        List<Long> excludeIds = bizArchiveFileMapper.selectListNoAdd(queryWrapper).stream()
                .filter(i -> i.getOfficialFileId() != null)
                .map(i -> i.getOfficialFileId()).collect(Collectors.toList());
        //查询已归档公文
        IPage<BizToArchiveFileDTO> data = bizArchiveFileMapper.selectToArchiveList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), bizArchiveFile, excludeIds);
        return new PageResult<BizToArchiveFileDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 待销毁公文 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizToDiscardFileDTO> queryToDiscardFileListPage(BizToDiscardFileDTO entityDTO, BasePageForm basePageForm) {
        //查询待归档公文
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper<BizArchiveFile>();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveFile::getTitle, entityDTO.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getArchiveDepartment()), BizArchiveFile::getArchiveDepartment, entityDTO.getArchiveDepartment());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getArchiveResource()), BizArchiveFile::getArchiveResource, entityDTO.getArchiveResource());
        queryWrapper.eq(BizArchiveFile::getArchiveStatus, UN_ARCHIVE);
        queryWrapper.eq(BizArchiveFile::getDiscard, DISCARD);
        queryWrapper.eq(BizArchiveFile::getRemoveStatus, UN_REMOVE);
        IPage pageData = bizArchiveFileMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizToDiscardFileDTO.class));
        return new PageResult<BizToDiscardFileDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 部门档案 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizDepartmentArchiveFileDTO> queryDepartmentArchiveFileListPage(BizDepartmentArchiveFileDTO entityDTO, BasePageForm basePageForm) {
        //查询部门档案
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper<BizArchiveFile>();
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getBoxNumber()), BizArchiveFile::getBoxNumber, entityDTO.getBoxNumber());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getTitle()), BizArchiveFile::getTitle, entityDTO.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getReferenceNumber()), BizArchiveFile::getTransactNumber, entityDTO.getReferenceNumber());
        queryWrapper.eq(BizArchiveFile::getDepartmentId, SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
        IPage pageData = bizArchiveFileMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizDepartmentArchiveFileDTO.class));
        return new PageResult<BizDepartmentArchiveFileDTO>(data.getRecords(), data.getTotal(), data.getCurrent());
    }

    /**
     * 归档管理 导出档案
     *
     * @param response
     * @return 档案导出列表.xlsx
     */
    @Override
    public Boolean exportAllArchiveFile(HttpServletResponse response) {
        // 查询所有档案信息
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper<BizArchiveFile>();
        queryWrapper.eq(BizArchiveFile::getArchiveStatus,ARCHIVE);
        List<BizArchiveFile> bizArchiveFileDTOS = bizArchiveFileMapper.selectList(queryWrapper);
        // 字段转换成导出Excel格式
        List<BizArchiveFileExportDTO> bizArchiveFileExportDTOs = bizArchiveFileDTOS.stream().map(i -> {
            BizArchiveFileExportDTO data = BeanConvertUtils.copyProperties(i, BizArchiveFileExportDTO.class);
            if(StringUtils.isNotEmpty(i.getBoxNumber())) {
                String[] BoxVariables = i.getBoxNumber().split("-");
                data.setDossierNumber(BoxVariables[0]);
                data.setDossierYear(BoxVariables[1]);
                data.setDuration(BoxVariables[3]);
                data.setArchiveDate(i.getArchiveDate().toString());
            }
            String title = Jsoup.parse(i.getTitle()).text();
            title = StringUtils.deleteWhitespace(title);
            data.setTitle(title);
            data.setPrincipal(i.getCreateName());
            return data;
        }).collect(Collectors.toList());

        // 设置Excel表头信息
        ExcelCustomize excelCustomize = new ExcelCustomize();
        excelCustomize.setFileName("文件档案目录表");
        excelCustomize.setSheetName("导出信息");
        // 设置表头样式和信息
        excelCustomize.setHeader(new DetectionSheetWriteHandler());
        // 从第3行开始写
        excelCustomize.setRelativeHeadRowIndex(2);
        // 排除id字段列，不导出
        List<String> columnName = new ArrayList<>();
        columnName.add("id");
        excelCustomize.setExcludeColumnFiledNames(columnName);

        Boolean bool = true;
        try {
            response.setContentType("multipart/form-data");
            bool = exportToExcelService.exportToExcelCustomize(bizArchiveFileExportDTOs, BizArchiveFileExportDTO.class, excelCustomize, response);
        } catch (IOException e) {
            e.printStackTrace();
            bool = false;
        }
        return bool;
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizArchiveFileDTO> queryList(BizArchiveFileDTO entityDTO) {
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper();
        List<BizArchiveFile> listData = bizArchiveFileMapper.selectList(queryWrapper);
        List<BizArchiveFileDTO> BizArchiveFileDTOList = ListCopyUtil.copy(listData, BizArchiveFileDTO.class);
        return BizArchiveFileDTOList;
    }

    /**
     * 根据主键id，查询待归档公文详情
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizArchiveFileDTO getToArchiveFile(Long id) {
        return bizArchiveFileMapper.selectOneOffice(id);
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizArchiveFileDTO findOne(Long id) {
        BizArchiveFile bizArchiveFile = bizArchiveFileMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizArchiveFile, BizArchiveFileDTO.class);
    }

    /**
     * 公文归档和新增档案 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizArchiveFileDTO archive(BizArchiveFileDTO entityDTO) {
        BizArchiveFile bizArchiveFile = BeanConvertUtils.copyProperties(entityDTO, BizArchiveFile.class);
        assert bizArchiveFile != null;
        // 文件id不为空表示为归档操作，才需要设置办件号。手动新增的归档文件办件号为空。
        if (entityDTO.getOfficialFileId() != null) {
            // 查询当前盒号下归档文件的最大办件号是多少
            LambdaQueryWrapper<BizArchiveFile> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(BizArchiveFile::getBoxId, entityDTO.getBoxId());
            queryWrapper.orderByDesc(BizArchiveFile::getTransactNumber);
            List<BizArchiveFile> bizArchiveFiles = bizArchiveFileMapper.selectList(queryWrapper);
            Integer transactNumber = CollectionUtils.isNotEmpty(bizArchiveFiles) ? bizArchiveFiles.get(0).getTransactNumber() : 0;
            // 设置当前归档文件的办件号
            bizArchiveFile.setTransactNumber(transactNumber + 1);
        }
        bizArchiveFile.setArchiveStatus(ARCHIVE);
        bizArchiveFile.setRemoveStatus(UN_REMOVE);
        bizArchiveFile.setAnnex(StringUtils.isNotBlank(entityDTO.getAnnex()) ? "1" : "0");
        save(bizArchiveFile);

        BizArchiveFileDTO bizArchiveFileDTO = BeanConvertUtils.copyProperties(bizArchiveFile, BizArchiveFileDTO.class);

        // 更新档案盒办件数+1
        BizArchiveBox bizArchiveBox = bizArchiveBoxMapper.selectById(entityDTO.getBoxId());
        bizArchiveBox.setTransactCount(bizArchiveBox.getTransactCount() + 1);
        bizArchiveBoxService.updateWithEntity(bizArchiveBox);

        // 更新附件表中的关联关系，将附件和本条档案的 form_data_id 进行关联。
        if (StringUtils.isNotBlank(entityDTO.getAnnex())) {
            cscpEnclosureFileService.enclosureChangeEnclosure(Long.valueOf(entityDTO.getAnnex()), entityDTO.getOfficialFileId());
        }
        LambdaQueryWrapper<CscpOrg> cscpOrgwrapper = new LambdaQueryWrapper();
        cscpOrgwrapper.eq(CscpOrg::getId,SecurityUtils.getCurrentCompanyId());
        CscpOrg cscpOrg = cscpOrgRepository.selectOne(cscpOrgwrapper);

        //正文
        LambdaQueryWrapper<CscpDocumentFile> documentQueryWrapper = new LambdaQueryWrapper<>();
        documentQueryWrapper.eq(CscpDocumentFile::getFormDataId,entityDTO.getOfficialFileId());
        CscpDocumentFile documentFile = cscpDocumentFileMapper.selectOne(documentQueryWrapper);
        String orgPinyin = PinyinUtil.getFirstSpell(bizArchiveBox.getOrgName());
        String filepath = "gdda/"+cscpOrg.getDossierNumber()+"_"+cscpOrg.getPathCode()+"/"
                + bizArchiveBox.getDossierYear()+"/"+bizArchiveBox.getDuration()+"/"+orgPinyin+"/"+bizArchiveBox.getBoxNumber()+"/"+ String.format("%05d", bizArchiveFileDTO.getTransactNumber())+"/";
        if(ObjectUtil.isNotEmpty(documentFile)){
            String newfilepath= filepath + "01ZW.ofd";
            fileStoreTemplateService.fileCopy(documentFile.getOfdPath(),newfilepath);
            BizBoxFileDTO bizBoxFileDTO = new BizBoxFileDTO();
            bizBoxFileDTO.setBoxId(bizArchiveBox.getId());
            bizBoxFileDTO.setBoxNumber(bizArchiveBox.getBoxNumber());
            bizBoxFileDTO.setOfficialFileId(bizArchiveFileDTO.getId());
            bizBoxFileDTO.setFilePath(newfilepath);
            bizBoxFileServiceImpl.create(bizBoxFileDTO);
        }

        //附件
        LambdaQueryWrapper<CscpEnclosureFile> enclosureQueryWrapper = new LambdaQueryWrapper<>();
        enclosureQueryWrapper.eq(CscpEnclosureFile::getFormDataId,entityDTO.getOfficialFileId());
        List<CscpEnclosureFile> enclosurelist = cscpEnclosureFileMapper.selectList(enclosureQueryWrapper);
        if(ObjectUtil.isNotEmpty(enclosurelist)){
            int i = 1;
            for(CscpEnclosureFile enclosureFile : enclosurelist){

                String fjfilepath = filepath + String.format("%02d", i)+"FJ"+"."+enclosureFile.getExtName();
                fileStoreTemplateService.fileCopy(enclosureFile.getFileUrl(),fjfilepath);
                BizBoxFileDTO bizBoxFileDTO = new BizBoxFileDTO();
                bizBoxFileDTO.setBoxId(bizArchiveBox.getId());
                bizBoxFileDTO.setBoxNumber(bizArchiveBox.getBoxNumber());
                bizBoxFileDTO.setOfficialFileId(bizArchiveFileDTO.getId());
                bizBoxFileDTO.setFilePath(fjfilepath);
                bizBoxFileServiceImpl.create(bizBoxFileDTO);
                i++;
            }
        }

        //保存目录
        return BeanConvertUtils.copyProperties(bizArchiveFile, BizArchiveFileDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizArchiveFileDTO entity) {
        BizArchiveFile bizArchiveFile = BeanConvertUtils.copyProperties(entity, BizArchiveFile.class);
        return bizArchiveFileMapper.updateById(bizArchiveFile);
    }

    /**
     * 完全删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        BizArchiveFile bizArchiveFile = new BizArchiveFile();
        bizArchiveFile.setId(id);
        bizArchiveFile.setRemoveStatus(REMOVE);
        return bizArchiveFileMapper.updateById(bizArchiveFile);
    }

    /**
     * 归档作废 待归档翻页
     *
     * @param id 公文id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int discard(Long id) {
        BizArchiveFileDTO bizArchiveFileDTO = bizArchiveFileMapper.selectOneOffice(id);
        BizArchiveFile bizArchiveFile = BeanConvertUtils.copyProperties(bizArchiveFileDTO, BizArchiveFile.class);
        bizArchiveFile.setOfficialFileId(bizArchiveFile.getId());
        bizArchiveFile.setId(null);
        bizArchiveFile.setDiscardDate(LocalDate.now());
        bizArchiveFile.setDiscard(DISCARD);
        bizArchiveFile.setArchiveStatus(UN_ARCHIVE);
        bizArchiveFile.setRemoveStatus(UN_REMOVE);
        save(bizArchiveFile);
        return 1;
    }

    /**
     * 取消作废
     *
     * @param id 公文id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelDiscard(Long id) {
        BizArchiveFile bizArchiveFile = bizArchiveFileMapper.selectById(id);
        Assert.notNull(bizArchiveFile.getId(), "id不存在");
        bizArchiveFile.setDiscard(VALID);
        return bizArchiveFileMapper.updateById(bizArchiveFile);
    }

    /**
     * 取消归档
     *
     * @param id 公文id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelArchive(Long id) {
        BizArchiveFileDTO entityDTO = findOne(id);
        BizArchiveBox bizArchiveBox = bizArchiveBoxMapper.selectById(entityDTO.getBoxId());
        // 档案盒办件数-1
        bizArchiveBox.setTransactCount(Math.max(bizArchiveBox.getTransactCount() - 1, 0));
        BizArchiveFile bizArchiveFile = BeanConvertUtils.copyProperties(entityDTO, BizArchiveFile.class);
        bizArchiveFile.setArchiveStatus(UN_ARCHIVE);
        bizArchiveFile.setBoxId(null);
        bizArchiveFile.setBoxNumber("");
        bizArchiveFileMapper.updateById(bizArchiveFile);
//        bizArchiveFileMapper.deleteById(bizArchiveFile.getId());
        //删除归档档案文件
        LambdaQueryWrapper<BizBoxFile> boxFilewrapper = new LambdaQueryWrapper();
        boxFilewrapper.eq(BizBoxFile::getBoxId,bizArchiveBox.getId())
        .eq(BizBoxFile::getOfficialFileId,bizArchiveFile.getId());
        List<BizBoxFile> bizBoxFile = bizBoxFileMapper.selectList(boxFilewrapper);
        if(ObjectUtil.isNotEmpty(bizBoxFile)){
            for(BizBoxFile ent : bizBoxFile){
                fileStoreTemplateService.deleteFile(ent.getFilePath());
                bizBoxFileMapper.deleteById(ent.getId());
            }

        }


        return bizArchiveBoxService.updateWithEntity(bizArchiveBox);
    }


    /**
     * 验证是否存在
     *
     * @param BizArchiveFileId
     * @return
     */
    @Override
    public boolean existByBizArchiveFileId(Long BizArchiveFileId) {
        if (BizArchiveFileId != null) {
            LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizArchiveFile::getId, BizArchiveFileId);
            List<BizArchiveFile> result = bizArchiveFileMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizArchiveFileDTO> dataList) {
        List<BizArchiveFile> result = ListCopyUtil.copy(dataList, BizArchiveFile.class);
        return saveBatch(result);
    }

    @Override
    public int getBizArchiveFileByBoxId(Long boxId) {
        LambdaQueryWrapper<BizArchiveFile> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizArchiveFile::getBoxId, boxId);
        List<BizArchiveFile> result = bizArchiveFileMapper.selectListNoAdd(queryWrapper);
        return result.size();
    }

    /**
     * 自定义表头信息
     */
    private static class DetectionSheetWriteHandler implements SheetWriteHandler {
        String exportUserName = SecurityUtils.getCurrentRealName();

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            Sheet sheet = workbook.getSheetAt(0);
            //设置第一行标题
            Row row1 = sheet.createRow(0);
            row1.setHeight((short) 800);
            Cell row1Cell1 = row1.createCell(0);
            row1Cell1.setCellValue("文件档案目录表");
            CellStyle row1CellStyle = workbook.createCellStyle();
            row1CellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            row1CellStyle.setAlignment(HorizontalAlignment.CENTER);
            Font row1Font = workbook.createFont();
            row1Font.setBold(true);
            row1Font.setFontName("宋体");
            row1Font.setFontHeightInPoints((short) 18);
            row1CellStyle.setFont(row1Font);
            row1Cell1.setCellStyle(row1CellStyle);
            // 合并单元格，起始行,结束行,起始列,结束列
            sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, 12));

            // 设置第二行标题
            Row row2 = sheet.createRow(1);
            row2.setHeight((short) 400);
            Cell row2Cell1 = row2.createCell(0);
            row2Cell1.setCellValue("导出人：" + exportUserName);
            CellStyle row2CellStyle = workbook.createCellStyle();
            row2CellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            row2CellStyle.setAlignment(HorizontalAlignment.RIGHT);
            Font row2Font = workbook.createFont();
            row2Font.setFontName("宋体");
            row2Font.setFontHeightInPoints((short) 10);
            row2CellStyle.setFont(row2Font);
            row2Cell1.setCellStyle(row2CellStyle);
            sheet.addMergedRegionUnsafe(new CellRangeAddress(1, 1, 0, 12));

        }
    }

}
