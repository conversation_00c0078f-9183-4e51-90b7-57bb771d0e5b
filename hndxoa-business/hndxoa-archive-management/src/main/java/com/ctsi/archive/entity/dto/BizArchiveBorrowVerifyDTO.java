package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <p>
 * 借阅审核
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizArchiveBorrowVerifyDTO对象", description="借阅审核")
public class BizArchiveBorrowVerifyDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 借阅日期 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private LocalDate borrowDate;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "归档部门")
    private String orgName;

    /**
     * 归还日期
     */
    @ApiModelProperty(value = "归还日期")
    @NotNull
    private LocalDate restoreDate;

    /**
     * 是否批准 0：同意 1：驳回 2：待审核
     */
    @ApiModelProperty(value = "是否批准 0：同意 1：驳回 2：待审核")
    @NotNull
    private Integer isApproval;

    /**
     * 下载申请 0：是，1：否
     */
    @ApiModelProperty(value = "下载申请 0：是，1：否")
    @NotNull
    private Integer isDownload;

    /**
     * 借阅理由
     */
    @ApiModelProperty(value = "借阅理由")
    private String reason;
}
