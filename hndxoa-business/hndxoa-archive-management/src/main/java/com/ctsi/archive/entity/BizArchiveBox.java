package com.ctsi.archive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 档案盒管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_archive_box")
@ApiModel(value="BizArchiveBox对象", description="档案盒管理")
public class BizArchiveBox extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "档案类型")
    private String archiveType;

    /**
     * 期限
     */
    @ApiModelProperty(value = "期限")
    private String duration;

    /**
     * 办件数
     */
    @ApiModelProperty(value = "办件数")
    private Integer transactCount;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String orgName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 件号
     */
    @ApiModelProperty(value = "件号")
    private Integer partNumber;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String dossierYear;


}
