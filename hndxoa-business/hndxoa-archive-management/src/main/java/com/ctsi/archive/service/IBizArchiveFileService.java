package com.ctsi.archive.service;

import com.ctsi.archive.entity.BizArchiveFile;
import com.ctsi.archive.entity.dto.BizArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizDepartmentArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizToArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizToDiscardFileDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 档案归档管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
public interface IBizArchiveFileService extends SysBaseServiceI<BizArchiveFile> {


    /**
     * 归档管理 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizArchiveFileDTO> queryBizArchiveFilePage(BizArchiveFileDTO entityDTO, BasePageForm page);

    /**
     * 待归档公文 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizToArchiveFileDTO> queryToArchiveFileListPage(BizToArchiveFileDTO entityDTO, BasePageForm page);

    /**
     * 待销毁公文 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizToDiscardFileDTO> queryToDiscardFileListPage(BizToDiscardFileDTO entityDTO, BasePageForm page);

    /**
     * 部门档案 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizDepartmentArchiveFileDTO> queryDepartmentArchiveFileListPage(BizDepartmentArchiveFileDTO entityDTO, BasePageForm page);

    /**
     * 归档管理 导出档案
     *
     * @param response
     * @return
     */
    Boolean exportAllArchiveFile( HttpServletResponse response);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizArchiveFileDTO> queryList(BizArchiveFileDTO entity);

    /**
     * 根据主键id，查询待归档公文详情
     *
     * @param id
     * @return
     */
    BizArchiveFileDTO getToArchiveFile(Long id);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizArchiveFileDTO findOne(Long id);

    /**
     * 公文归档和新增档案
     *
     * @param entity
     * @return
     */
    BizArchiveFileDTO archive(BizArchiveFileDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizArchiveFileDTO entity);

    /**
     * 公文作废
     *
     * @param id
     * @return
     */
    int discard(Long id);

    /**
     * 取消作废
     *
     * @param id
     * @return
     */
    int cancelDiscard(Long id);

    /**
     * 完全删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 取消归档
     *
     * @param id
     * @return
     */
    int cancelArchive(Long id);

     /**
     * 是否存在
     *
     * existByBizArchiveFileId
     * @param code
     * @return
     */
    boolean existByBizArchiveFileId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizArchiveFileDTO> dataList);

    int getBizArchiveFileByBoxId(Long boxId);
}
