package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * 档案盒管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizArchiveBoxDTO对象", description = "档案盒管理")
public class BizArchiveBoxDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号|档案盒
     */
    @ApiModelProperty(value = "盒号|档案盒")
    private String boxNumber;

    /**
     * 全宗号
     */
    @ApiModelProperty(value = "全宗号")
    @Size(max = 10, message = "限定10个字符以内")
    private String dossierNumber;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Length(min = 4, max = 4, message = "必须为4位数")
    private String dossierYear;

    /**
     * 档案类型
     */
    @ApiModelProperty(value = "档案类型")
    private String archiveType;

    /**
     * 期限
     */
    @ApiModelProperty(value = "期限")
    private String duration;

    /**
     * 办件数
     */
    @ApiModelProperty(value = "办件数")
    private Integer transactCount;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private List<String> orgName;

    /**
     * 件号
     */
    @ApiModelProperty(value = "件号")
    private Integer partNumber;

    /**
     * 档案门类
     */
    @ApiModelProperty(value = "档案门类")
    @Length(max = 50, message = "限定限50个字符以内")
    private String category;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Length(max = 500, message = "限定限500个字符以内")
    private String description;

}
