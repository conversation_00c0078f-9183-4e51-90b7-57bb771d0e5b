package com.ctsi.archive.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 归档管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizArchiveFileDTO对象", description="归档管理")
public class BizToArchiveFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文类型（报告、其他...）
     */
    @ApiModelProperty(value = "公文类型（报告、其他...）")
    private String documentType;


    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 归属部门
     */
    @ApiModelProperty(value = "归属部门")
    private String archiveDepartment;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 创建日期（公文流程已完结日期）
     */
    @ApiModelProperty(value = "创建日期（公文流程已完结日期）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format="yyyy-MM-dd") //数据库导出页面时json格式化
    private LocalDate createDate;


    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、新增...）")
    private String archiveResource;

}
