package com.ctsi.archive.service;

import com.ctsi.archive.entity.BizArchiveBorrow;
import com.ctsi.archive.entity.dto.BizArchiveBorrowDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowResultDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowRequestDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowVerifyDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 档案借阅管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface IBizArchiveBorrowService extends SysBaseServiceI<BizArchiveBorrow> {


    /**
     * 借阅申请 分页查询多条数据.
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizArchiveBorrowRequestDTO> queryBizArchiveBorrowRequestPage(BizArchiveBorrowRequestDTO entityDTO, BasePageForm page);

    /**
     * 借阅审核 分页查询多条数据.
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizArchiveBorrowDTO> queryBizArchiveBorrowVerifyPage(BizArchiveBorrowDTO entityDTO, BasePageForm page);

    /**
     * 借阅结果 分页查询多条数据.
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizArchiveBorrowResultDTO> queryBizArchiveBorrowResultPage(BizArchiveBorrowDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizArchiveBorrowDTO> queryList(BizArchiveBorrowDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizArchiveBorrowResultDTO findOne(Long id);

    /**
     * 借阅申请
     *
     * @param entity
     * @return
     */
    BizArchiveBorrowVerifyDTO borrow(BizArchiveBorrowVerifyDTO entity);

    /**
     * 归还档案
     *
     * @param id
     * @return
     */
    int restore(Long id);

    /**
     * 更新 借阅审核
     *
     * @param entity
     * @return
     */
    int update(BizArchiveBorrowVerifyDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizArchiveBorrowId
     * @param code
     * @return
     */
    boolean existByBizArchiveBorrowId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizArchiveBorrowDTO> dataList);


}
