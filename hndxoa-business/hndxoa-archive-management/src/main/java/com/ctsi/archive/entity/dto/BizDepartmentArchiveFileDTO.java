package com.ctsi.archive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 部门档案
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizDepartmentArchiveFileDTO对象", description="部门档案")
public class BizDepartmentArchiveFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 盒号id
     */
    @ApiModelProperty(value = "盒号id")
    private Long boxId;

    /**
     * 公文id
     */
    @ApiModelProperty(value = "公文id")
    private Long officialFileId;

    /**
     * 盒号|档案盒
     */
    @ApiModelProperty(value = "盒号|档案盒")
    private String boxNumber;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "机构(归档部门)")
    private List<String> orgName;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 归档日期
     */
    @ApiModelProperty(value = "归档日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate archiveDate;

    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageCount;

    /**
     * 档案来源（发文、收文、新增...）
     */
    @ApiModelProperty(value = "档案来源（发文、收文、呈批件）")
    private String archiveResource;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String comments;

    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 是否附件
     */
    @ApiModelProperty(value = "是否附件")
    private String annex;
}
