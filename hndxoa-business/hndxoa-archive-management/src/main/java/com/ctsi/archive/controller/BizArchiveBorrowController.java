package com.ctsi.archive.controller;

import com.ctsi.archive.entity.dto.BizArchiveBorrowDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowRequestDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowResultDTO;
import com.ctsi.archive.entity.dto.BizArchiveBorrowVerifyDTO;
import com.ctsi.archive.service.IBizArchiveBorrowService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizArchiveBorrow")
@Api(value = "档案借阅管理", tags = "档案借阅管理接口")
public class BizArchiveBorrowController extends BaseController {

    private static final String ENTITY_NAME = "bizArchiveBorrow";

    @Autowired
    private IBizArchiveBorrowService bizArchiveBorrowService;



     /**
     *  新增数据 借阅申请 保存提交.
     */
    @PostMapping("/borrow")
    @ApiOperation(value = "保存提交(需传入id值) (权限code码为：cscp.bizArchiveBorrow.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增档案借阅管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBorrow.add')")
    public ResultVO<BizArchiveBorrowVerifyDTO> borrow(@RequestBody BizArchiveBorrowVerifyDTO bizArchiveBorrowVerifyDTO)  {
        BizArchiveBorrowVerifyDTO result = bizArchiveBorrowService.borrow(bizArchiveBorrowVerifyDTO);
        return ResultVO.success(result);
    }

     /**
     *  新增数据 归还档案.
     */
    @PostMapping("/restore/{id}")
    @ApiOperation(value = "归还档案(权限code码为：cscp.bizArchiveBorrow.restore)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增档案借阅管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBorrow.add')")
    public ResultVO restore(@PathVariable Long id)  {
        int result = bizArchiveBorrowService.restore(id);
        if(result > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  更新存在数据 借阅审核.
     */
    @PostMapping("/update")
    @ApiOperation(value = "借阅审核(0：同意 1：驳回) (权限code码为：cscp.bizArchiveBorrow.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新档案借阅管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBorrow.update')")
    public ResultVO update(@Valid @RequestBody BizArchiveBorrowVerifyDTO bizArchiveBorrowVerifyDTO) {
	    Assert.notNull(bizArchiveBorrowVerifyDTO.getId(), "general.IdNotNull");
        int count = bizArchiveBorrowService.update(bizArchiveBorrowVerifyDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    ///**
    // * 查看档案详情 查询单条数据.
    // */
    //@GetMapping("/get/{id}")
    //@ApiOperation(value = "档案详情 查询单条数据", notes = "传入参数")
    ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    //public ResultVO get(@PathVariable Long id) {
    //    BizArchiveFileDTO bizArchiveFileDTO = bizArchiveFileService.findOne(id);
    //    return ResultVO.success(bizArchiveFileDTO);
    //}

    ///**
    // * 借阅申请、借阅审核.
    // */
    //@GetMapping("/getBorrowDetail/{id}")
    //@ApiOperation(value = "借阅申请、借阅审核", notes = "传入参数")
    ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    //public ResultVO getBorrowDetail(@PathVariable Long id) {
    //    BizArchiveBorrowResultDTO bizArchiveBorrowDTO = bizArchiveBorrowService.findOne(id);
    //    return ResultVO.success(bizArchiveBorrowDTO);
    //}

    /**
     *  借阅申请 分页查询多条数据.
     */
    @GetMapping("/queryBizArchiveBorrowRequestPage")
    @ApiOperation(value = "借阅申请 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizArchiveBorrowRequestDTO>> queryBizArchiveBorrowRequestPage(BizArchiveBorrowRequestDTO bizArchiveBorrowRequestDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveBorrowService.queryBizArchiveBorrowRequestPage(bizArchiveBorrowRequestDTO, basePageForm));
    }

    /**
    *  借阅审核 分页查询多条数据.
    */
    @GetMapping("/queryBizArchiveBorrowVerifyPage")
    @ApiOperation(value = "借阅审核 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizArchiveBorrowDTO>> queryBizArchiveBorrowVerifyPage(BizArchiveBorrowDTO bizArchiveBorrowDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveBorrowService.queryBizArchiveBorrowVerifyPage(bizArchiveBorrowDTO, basePageForm));
    }

    /**
    *  借阅结果 分页查询多条数据.
    */
    @GetMapping("/queryBizArchiveBorrowResultPage")
    @ApiOperation(value = "借阅结果 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizArchiveBorrowResultDTO>> queryBizArchiveBorrowResultPage(BizArchiveBorrowDTO bizArchiveBorrowDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveBorrowService.queryBizArchiveBorrowResultPage(bizArchiveBorrowDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizArchiveBorrow")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizArchiveBorrowDTO>> queryBizArchiveBorrow(BizArchiveBorrowDTO bizArchiveBorrowDTO) {
       List<BizArchiveBorrowDTO> list = bizArchiveBorrowService.queryList(bizArchiveBorrowDTO);
       return ResultVO.success(new ResResult<BizArchiveBorrowDTO>(list));
   }

}
