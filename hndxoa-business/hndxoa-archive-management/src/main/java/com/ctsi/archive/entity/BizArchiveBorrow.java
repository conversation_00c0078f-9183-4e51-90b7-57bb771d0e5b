package com.ctsi.archive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 档案借阅管理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_archive_borrow")
@ApiModel(value="BizArchiveBorrow对象", description="档案借阅管理")
public class BizArchiveBorrow extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 归档文件id
     */
    @ApiModelProperty(value = "归档文件id")
    private Long officialFileId;

    /**
     * 公文标题
     */
    @ApiModelProperty(value = "公文标题")
    private String title;

    /**
     * 公文文号
     */
    @ApiModelProperty(value = "公文文号")
    private String referenceNumber;

    /**
     * 盒号（档案盒）
     */
    @ApiModelProperty(value = "盒号（档案盒）")
    private String boxNumber;

    /**
     * 公文密级
     */
    @ApiModelProperty(value = "公文密级")
    private String secret;

    /**
     * 机构(归档部门)
     */
    @ApiModelProperty(value = "归档部门")
    private String orgName;

    /**
     * 借阅人(借阅部门)
     */
    @ApiModelProperty(value = "借阅人（借阅部门）")
    private String borrowerDepartment;

    /**
     * 借阅日期
     */
    @ApiModelProperty(value = "借阅日期")
    private LocalDate borrowDate;

    /**
     * 归还日期
     */
    @ApiModelProperty(value = "归还日期")
    private LocalDate restoreDate;

    /**
     * 是否归还 0：未归还 1：已归还
     */
    @ApiModelProperty(value = "是否归还 0：未归还 1：已归还")
    private Integer isRestore;

    /**
     * 是否批准 0：同意 1：驳回 2：待审核
     */
    @ApiModelProperty(value = "是否批准 0：同意 1：驳回 2：待审核")
    private Integer isApproval;

    /**
     * 下载申请 0：是，1：否
     */
    @ApiModelProperty(value = "下载申请 0：是，1：否")
    private Integer isDownload;

    /**
     * 借阅理由
     */
    @ApiModelProperty(value = "借阅理由")
    private String reason;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;


}
