package com.ctsi.archive.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.archive.entity.BizArchiveBox;
import com.ctsi.archive.entity.dto.BizArchiveBoxDTO;
import com.ctsi.archive.entity.dto.BizArchiveBoxResultDTO;
import com.ctsi.archive.mapper.BizArchiveBoxMapper;
import com.ctsi.archive.service.IBizArchiveBoxService;
import com.ctsi.archive.service.IBizArchiveFileService;
import com.ctsi.archive.util.PinyinUtil;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.security.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 档案盒管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
@Slf4j
@Service
public class BizArchiveBoxServiceImpl extends SysBaseServiceImpl<BizArchiveBoxMapper, BizArchiveBox> implements IBizArchiveBoxService {

    @Autowired
    private BizArchiveBoxMapper bizArchiveBoxMapper;

    @Autowired
    private CscpOrgService cscpOrgService;

    private CscpOrgDTO cscpOrgDTO;

    @Autowired
    private IBizArchiveFileService bizArchiveFileService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizArchiveBoxResultDTO> queryListPage(BizArchiveBoxDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizArchiveBox> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByDesc(BizArchiveBox::getCreateTime);
        IPage<BizArchiveBox> pageData = bizArchiveBoxMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizArchiveBoxResultDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, BizArchiveBoxResultDTO.class));

        return new PageResult<BizArchiveBoxResultDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizArchiveBoxResultDTO> queryList(BizArchiveBoxDTO entityDTO) {
        LambdaQueryWrapper<BizArchiveBox> queryWrapper = new LambdaQueryWrapper();
        List<BizArchiveBox> listData = bizArchiveBoxMapper.selectList(queryWrapper);
        List<BizArchiveBoxResultDTO> BizArchiveBoxResultDTOList = ListCopyUtil.copy(listData, BizArchiveBoxResultDTO.class);
        return BizArchiveBoxResultDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizArchiveBoxDTO findOne(Long id) {
        BizArchiveBox bizArchiveBox = bizArchiveBoxMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizArchiveBox, BizArchiveBoxDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizArchiveBoxDTO create(BizArchiveBoxDTO entityDTO) {

        //保存单位的全宗号
        CscpOrgDTO cscpOrg = cscpOrgService.findOne(SecurityUtils.getCurrentCompanyId());
        if(!entityDTO.getDossierNumber().equals(cscpOrg.getDossierNumber())){
            cscpOrg.setDossierNumber(entityDTO.getDossierNumber());
            cscpOrgService.update(cscpOrg);
        }

        LambdaQueryWrapper<BizArchiveBox> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizArchiveBox::getCompanyId, SecurityUtils.getCurrentCompanyId())
        .eq(BizArchiveBox::getDossierYear,entityDTO.getDossierYear()).orderByDesc(BizArchiveBox::getPartNumber);
        List<BizArchiveBox> result = bizArchiveBoxMapper.selectList(queryWrapper);
        String partNumber = null;
        int maxPartNumber = 1;
        if(ObjectUtil.isNotEmpty(result)){
            partNumber = String.format("%04d", result.get(0).getPartNumber()+1);
            maxPartNumber = result.get(0).getPartNumber()+1;
        }else{
            partNumber = String.format("%04d", 1);
        }

        // 判断是否选择了多个机构（部门），多个部门之间用,分隔
        String orgName = CollectionUtil.size(entityDTO.getOrgName()) > 1 ? StringUtils.join(entityDTO.getOrgName(), ",") : entityDTO.getOrgName().get(0);
        String orgPinyin = PinyinUtil.getFirstSpell(orgName);
        String boxNumber = new StringBuilder(
                entityDTO.getDossierNumber())//全宗号
                .append("-")
                .append(entityDTO.getCategory())//档案门类
                .append("-")
                .append(entityDTO.getDossierYear())//年份
                .append("-")
                .append(entityDTO.getDuration())//期限
                .append("-")
                .append(orgPinyin)//机构
                .append("-")
                .append(partNumber)//件号
                .toString();//.append("-").append(entityDTO.getArchiveType())
        // 设置全宗号
        BizArchiveBox bizArchiveBox = BeanConvertUtils.copyProperties(entityDTO, BizArchiveBox.class);
        bizArchiveBox.setBoxNumber(boxNumber);
        bizArchiveBox.setOrgName(orgName);
        bizArchiveBox.setTransactCount(0);
        bizArchiveBox.setPartNumber(maxPartNumber);
        save(bizArchiveBox);
        return BeanConvertUtils.copyProperties(bizArchiveBox, BizArchiveBoxDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateWithEntity(BizArchiveBox entity) {
        BizArchiveBox bizArchiveBox = BeanConvertUtils.copyProperties(entity, BizArchiveBox.class);
        return bizArchiveBoxMapper.updateById(bizArchiveBox);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizArchiveBoxDTO entity) {
        BizArchiveBox bizArchiveBox = BeanConvertUtils.copyProperties(entity, BizArchiveBox.class);
        return bizArchiveBoxMapper.updateById(bizArchiveBox);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    public int delete(Long id) {
       int count = bizArchiveFileService.getBizArchiveFileByBoxId(id);
       if(count>0){
           return -1;
       }else{
           return bizArchiveBoxMapper.deleteById(id);
       }
    }


    /**
     * 验证是否存在
     *
     * @param BizArchiveBoxId
     * @return
     */
    @Override
    public boolean existByBizArchiveBoxId(Long BizArchiveBoxId) {
        if (BizArchiveBoxId != null) {
            LambdaQueryWrapper<BizArchiveBox> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizArchiveBox::getId, BizArchiveBoxId);
            List<BizArchiveBox> result = bizArchiveBoxMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizArchiveBoxDTO> dataList) {
        List<BizArchiveBox> result = ListCopyUtil.copy(dataList, BizArchiveBox.class);
        return saveBatch(result);
    }

}
