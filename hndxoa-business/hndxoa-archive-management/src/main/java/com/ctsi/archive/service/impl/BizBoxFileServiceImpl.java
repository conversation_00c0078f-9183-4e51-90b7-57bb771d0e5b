package com.ctsi.archive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.archive.entity.BizBoxFile;
import com.ctsi.archive.entity.dto.BizBoxFileDTO;
import com.ctsi.archive.mapper.BizBoxFileMapper;
import com.ctsi.archive.service.IBizBoxFileService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Slf4j
@Service
public class BizBoxFileServiceImpl extends SysBaseServiceImpl<BizBoxFileMapper, BizBoxFile> implements IBizBoxFileService {

    @Autowired
    private BizBoxFileMapper bizBoxFileMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizBoxFileDTO> queryListPage(BizBoxFileDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizBoxFile> queryWrapper = new LambdaQueryWrapper();

        IPage<BizBoxFile> pageData = bizBoxFileMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizBoxFileDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizBoxFileDTO.class));

        return new PageResult<BizBoxFileDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizBoxFileDTO> queryList(BizBoxFileDTO entityDTO) {
        LambdaQueryWrapper<BizBoxFile> queryWrapper = new LambdaQueryWrapper();
            List<BizBoxFile> listData = bizBoxFileMapper.selectList(queryWrapper);
            List<BizBoxFileDTO> BizBoxFileDTOList = ListCopyUtil.copy(listData, BizBoxFileDTO.class);
        return BizBoxFileDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizBoxFileDTO findOne(Long id) {
        BizBoxFile  bizBoxFile =  bizBoxFileMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizBoxFile,BizBoxFileDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizBoxFileDTO create(BizBoxFileDTO entityDTO) {
       BizBoxFile bizBoxFile =  BeanConvertUtils.copyProperties(entityDTO,BizBoxFile.class);
        save(bizBoxFile);
        return  BeanConvertUtils.copyProperties(bizBoxFile,BizBoxFileDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizBoxFileDTO entity) {
        BizBoxFile bizBoxFile = BeanConvertUtils.copyProperties(entity,BizBoxFile.class);
        return bizBoxFileMapper.updateById(bizBoxFile);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizBoxFileMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizBoxFileId
     * @return
     */
    @Override
    public boolean existByBizBoxFileId(Long BizBoxFileId) {
        if (BizBoxFileId != null) {
            LambdaQueryWrapper<BizBoxFile> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizBoxFile::getId, BizBoxFileId);
            List<BizBoxFile> result = bizBoxFileMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizBoxFileDTO> dataList) {
        List<BizBoxFile> result = ListCopyUtil.copy(dataList, BizBoxFile.class);
        return saveBatch(result);
    }


}
