package com.ctsi.archive.controller;

import com.ctsi.archive.entity.dto.BizArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizDepartmentArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizToArchiveFileDTO;
import com.ctsi.archive.entity.dto.BizToDiscardFileDTO;
import com.ctsi.archive.service.IBizArchiveFileService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizArchiveFile")
@Api(value = "档案归档管理", tags = "档案归档管理接口")
public class BizArchiveFileController extends BaseController {

    private static final String ENTITY_NAME = "bizArchiveFile";

    @Autowired
    private IBizArchiveFileService bizArchiveFileService;



    /**
     *  新增归档管理批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizArchiveFile.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增归档管理批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.add')")
    public ResultVO createBatch(@RequestBody List<BizArchiveFileDTO> bizArchiveFileList) {
       Boolean  result = bizArchiveFileService.insertBatch(bizArchiveFileList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  公文归档和新增档案 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "公文归档和新增档案(权限code码为：cscp.bizArchiveFile.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增归档管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.add')")
    public ResultVO<BizArchiveFileDTO> create(@RequestBody BizArchiveFileDTO bizArchiveFileDTO)  {
        BizArchiveFileDTO result = bizArchiveFileService.archive(bizArchiveFileDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizArchiveFile.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新归档管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.update')")
    public ResultVO update(@RequestBody BizArchiveFileDTO bizArchiveFileDTO) {
	    Assert.notNull(bizArchiveFileDTO.getId(), "general.IdNotNull");
        int count = bizArchiveFileService.update(bizArchiveFileDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  归档作废.
     */
    @PostMapping("/discard/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除归档管理数据")
    @ApiOperation(value = "归档作废(权限code码为：cscp.bizArchiveFile.discard)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.delete')")
    public ResultVO discard(@PathVariable Long id) {
        int count = bizArchiveFileService.discard(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  取消作废.
     */
    @PostMapping("/cancelDiscard/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除归档管理数据")
    @ApiOperation(value = "取消作废(权限code码为：cscp.bizArchiveFile.cancelDiscard)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.delete')")
    public ResultVO cancelDiscard(@PathVariable Long id) {
        int count = bizArchiveFileService.cancelDiscard(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  完全删除.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除归档管理数据")
    @ApiOperation(value = "完全删除(权限code码为：cscp.bizArchiveFile.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizArchiveFileService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  取消归档.
     */
    @PostMapping("/cancelArchive/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "取消归档")
    @ApiOperation(value = "取消归档(权限code码为：cscp.bizArchiveFile.cancelArchive)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveFile.delete')")
    public ResultVO cancelArchive(@PathVariable Long id) {
        int count = bizArchiveFileService.cancelArchive(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据待归档公文详情
     */
    @GetMapping("/getToArchiveFile/{id}")
    @ApiOperation(value = "公文归档 查询单条数据待归档公文详情", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getToArchiveFile(@PathVariable Long id) {
        BizArchiveFileDTO bizArchiveFileDTO = bizArchiveFileService.getToArchiveFile(id);
        return ResultVO.success(bizArchiveFileDTO);
    }

    /**
     * 归档管理，查询单条数据.
     */
    @GetMapping("/getArchiveFile/{id}")
    @ApiOperation(value = "归档管理 查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizArchiveFileDTO bizArchiveFileDTO = bizArchiveFileService.findOne(id);
        return ResultVO.success(bizArchiveFileDTO);
    }

    /**
    *  归档管理 分页查询多条数据.
    */
    @GetMapping("/queryBizArchiveFilePage")
    @ApiOperation(value = "归档管理 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizArchiveFileDTO>> queryBizArchiveFilePage(BizArchiveFileDTO bizArchiveFileDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveFileService.queryBizArchiveFilePage(bizArchiveFileDTO, basePageForm));
    }

    /**
    *  待归档公文 分页查询多条数据.
    */
    @GetMapping("/queryToArchiveFileListPage")
    @ApiOperation(value = "待归档公文 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizToArchiveFileDTO>> queryToArchiveFileListPage(BizToArchiveFileDTO bizArchiveFileDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveFileService.queryToArchiveFileListPage(bizArchiveFileDTO, basePageForm));
    }

    /**
    *  待销毁公文 分页查询多条数据.
    */
    @GetMapping("/queryToDiscardFileListPage")
    @ApiOperation(value = "待销毁公文 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizToDiscardFileDTO>> queryToDiscardFileListPage(BizToDiscardFileDTO bizToDiscardFileDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveFileService.queryToDiscardFileListPage(bizToDiscardFileDTO, basePageForm));
    }

    /**
    *  部门档案 分页查询多条数据.
    */
    @GetMapping("/queryDepartmentArchiveFileListPage")
    @ApiOperation(value = "部门档案 翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizDepartmentArchiveFileDTO>> queryDepartmentArchiveFileListPage(BizDepartmentArchiveFileDTO bizDepartmentArchiveFileDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveFileService.queryDepartmentArchiveFileListPage(bizDepartmentArchiveFileDTO, basePageForm));
    }

    /**
     * 归档管理 导出档案
     * */
    @GetMapping("/exportAllArchiveFile")
    @ApiOperation(value = "归档管理 导出档案接口", notes = "传入参数")
    public void exportAllArchiveFile( HttpServletResponse response) {
        bizArchiveFileService.exportAllArchiveFile(response);
    }

}
