package com.ctsi.archive.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.archive.entity.BizArchiveBox;
import com.ctsi.archive.entity.dto.BizArchiveBoxDTO;
import com.ctsi.archive.entity.dto.BizArchiveBoxResultDTO;
import com.ctsi.archive.mapper.BizArchiveBoxMapper;
import com.ctsi.archive.service.IBizArchiveBoxService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizArchiveBox")
@Api(value = "档案盒管理", tags = "档案盒管理接口")
public class BizArchiveBoxController extends BaseController {

    private static final String ENTITY_NAME = "bizArchiveBox";

    @Autowired
    private IBizArchiveBoxService bizArchiveBoxService;

    @Autowired
    private BizArchiveBoxMapper bizArchiveBoxMapper;



    /**
     *  新增档案盒管理批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizArchiveBox.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增档案盒管理批量数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBox.add')")
    public ResultVO createBatch(@RequestBody List<BizArchiveBoxDTO> bizArchiveBoxList) {
       Boolean  result = bizArchiveBoxService.insertBatch(bizArchiveBoxList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizArchiveBox.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增档案盒管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBox.add')")
    public ResultVO<BizArchiveBoxDTO> create(@Valid @RequestBody BizArchiveBoxDTO bizArchiveBoxDTO)  {
        BizArchiveBoxDTO result = bizArchiveBoxService.create(bizArchiveBoxDTO);
        return ResultVO.success(result);
    }

    /**
     *  查询件号.
     */
    @GetMapping("/getPartNumber/{year}")
    @ApiOperation(value = "查询件号数", notes = "传入参数单位ID")
    public ResultVO getPartNumber(@PathVariable String year) {
//        String year = null;
        LambdaQueryWrapper<BizArchiveBox> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizArchiveBox::getCompanyId, SecurityUtils.getCurrentCompanyId())
                .eq(BizArchiveBox::getDossierYear,year).orderByDesc(BizArchiveBox::getPartNumber);
        List<BizArchiveBox> result = bizArchiveBoxMapper.selectList(queryWrapper);
        String partNumber = null;
        if(ObjectUtil.isNotEmpty(result)){
            partNumber = String.format("%04d", result.get(0).getPartNumber()+1);
        }else{
            partNumber = String.format("%04d", 1);
        }

        return ResultVO.success(partNumber);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizArchiveBox.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新档案盒管理数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBox.update')")
    public ResultVO update(@RequestBody BizArchiveBoxDTO bizArchiveBoxDTO) {
	    Assert.notNull(bizArchiveBoxDTO.getId(), "general.IdNotNull");
        int count = bizArchiveBoxService.update(bizArchiveBoxDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除档案盒管理数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizArchiveBox.delete)", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizArchiveBox.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizArchiveBoxService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else if(count==-1){
            return ResultVO.error(ResultCode.ARCHIVE_BOX_DELETE);
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizArchiveBoxDTO bizArchiveBoxDTO = bizArchiveBoxService.findOne(id);
        return ResultVO.success(bizArchiveBoxDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizArchiveBoxPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizArchiveBoxResultDTO>> queryBizArchiveBoxPage(BizArchiveBoxDTO bizArchiveBoxDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizArchiveBoxService.queryListPage(bizArchiveBoxDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizArchiveBox")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizArchiveBoxResultDTO>> queryBizArchiveBox(BizArchiveBoxDTO bizArchiveBoxDTO) {
       List<BizArchiveBoxResultDTO> list = bizArchiveBoxService.queryList(bizArchiveBoxDTO);
       return ResultVO.success(new ResResult<BizArchiveBoxResultDTO>(list));
   }

}
