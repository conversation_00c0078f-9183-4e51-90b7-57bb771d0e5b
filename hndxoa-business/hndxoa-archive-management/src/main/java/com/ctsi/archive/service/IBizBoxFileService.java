package com.ctsi.archive.service;

import com.ctsi.archive.entity.dto.BizBoxFileDTO;
import com.ctsi.archive.entity.BizBoxFile;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
public interface IBizBoxFileService extends SysBaseServiceI<BizBoxFile> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizBoxFileDTO> queryListPage(BizBoxFileDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizBoxFileDTO> queryList(BizBoxFileDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizBoxFileDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizBoxFileDTO create(BizBoxFileDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizBoxFileDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizBoxFileId
     * @param code
     * @return
     */
    boolean existByBizBoxFileId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizBoxFileDTO> dataList);


}
