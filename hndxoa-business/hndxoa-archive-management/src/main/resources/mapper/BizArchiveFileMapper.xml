<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.archive.mapper.BizArchiveFileMapper">
    <!--suppress SqlResolve -->
    <select id="selectToArchiveList" parameterType="com.ctsi.archive.entity.BizArchiveFile"
            resultType="com.ctsi.archive.entity.dto.BizToArchiveFileDTO">
        SELECT
        t.id AS id,
        t.title AS title,
        t.document_type AS document_type,
        t.reference_number AS reference_number,
        t.archive_department AS archive_department,
        t.secret AS secret,
        t.archive_resource AS archive_resource,
        b.PROC_END_TIME as createDate
        FROM
        ( SELECT
        id,
        title,
        document_type AS document_type,
        reference_number AS reference_number,
        department_name AS archive_department,
        secret AS secret,
        '发文' AS archive_resource
        <!--发文-->
        FROM t_senddoc_master
        WHERE deleted=0
        AND bpm_status = 3
        AND company_id = #{toArchive.companyId}
        <!--公文标题-->
        <if test="toArchive.title != null and toArchive.title != ''">
            and title like concat('%',#{toArchive.title},'%')
        </if>
        <!--归属部门-->
        <if test="toArchive.archiveDepartment != null and toArchive.archiveDepartment != ''">
            and department_name like concat('%',#{toArchive.archiveDepartment},'%')
        </if>
        UNION
        SELECT
        id,
        title,
        receive_type AS document_type,
        incoming_no AS reference_number,
        department_name AS archive_department,
        "" AS secret,
        '收文' AS archive_resource
        <!--收文-->
        FROM t_receive_management
        WHERE deleted=0
        AND bpm_status = 3
        AND company_id = #{toArchive.companyId}
        <!--公文标题-->
        <if test="toArchive.title != null and toArchive.title != ''">
            and title like concat('%',#{toArchive.title},'%')
        </if>
        <!--归属部门-->
        <if test="toArchive.archiveDepartment != null and toArchive.archiveDepartment != ''">
            and department_name like concat('%',#{toArchive.archiveDepartment},'%')
        </if>
        UNION
        SELECT
        id,
        title,
        '呈批件' AS document_type,
        document_no AS reference_number,
        department_name AS archive_department,
        "" AS secret,
        '呈批件' AS archive_resource
        <!--呈批件-->
        FROM biz_approval_management
        WHERE deleted=0
        AND bpm_status = 3
        AND company_id = #{toArchive.companyId}
        <!--公文标题-->
        <if test="toArchive.title != null and toArchive.title != ''">
            and title like concat('%',#{toArchive.title},'%')
        </if>
        <!--归属部门-->
        <if test="toArchive.archiveDepartment != null and toArchive.archiveDepartment != ''">
            and department_name like concat('%',#{toArchive.archiveDepartment},'%')
        </if>
        ) t
        left join
        cscp_proc_base b on t.id = b.FORM_DATA_ID
        <!--查询流程表中办结日期-->
        WHERE 1=1
        <!--去除已作废、已完全删除、已归档文件-->
        <if test="excludeIds != null and excludeIds.size()>0" >
            AND t.id NOT IN
            <foreach item="item" index="index" collection="excludeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--归档来源-->
        <if test="toArchive.archiveResource != null and toArchive.archiveResource != ''">
            and b.table_name  = #{toArchive.archiveResource}
        </if>
        order by b.PROC_END_TIME desc
    </select>

    <select id="selectOneOffice" parameterType="com.ctsi.archive.entity.dto.BizToArchiveFileDTO"
            resultType="com.ctsi.archive.entity.dto.BizArchiveFileDTO">
        SELECT
        t.id AS id,
        t.title AS title,
        t.document_type AS document_type,
        t.reference_number AS reference_number,
        t.annex AS annex,
        t.document AS document,
        t.secret AS secret,
        t.archive_department AS archive_department,
        t.archive_resource AS archive_resource
        FROM
        ( SELECT
        id,
        title,
        document_type AS document_type,
        reference_number AS reference_number,
        annex,
        document,
        secret,
        department_name AS archive_department,
        '发文' AS archive_resource
        <!--发文-->
        FROM t_senddoc_master
        WHERE deleted = 0
        AND id = #{id}
        UNION
        SELECT
        id,
        title,
        receive_type AS document_type,
        incoming_no AS reference_number,
        annex,
        document,
        '' as secret,
        department_name AS archive_department,
        '收文' AS archive_resource
        <!--收文-->
        FROM t_receive_management
        WHERE deleted=0
        AND id = #{id}
        UNION
        SELECT
        id,
        title,
        '' AS document_type,
        document_no AS reference_number,
        annex,
        document,
        '' as secret,
        department_name AS archive_department,
        '呈批件' AS archive_resource
        <!--呈批件-->
        FROM biz_approval_management
        WHERE deleted=0
        AND id = #{id}
        ) t
    </select>

    <select id="selectProcEndTime" resultType="com.ctsi.archive.entity.dto.BizToArchiveFileDTO">
        SELECT FORM_DATA_ID as id, PROC_END_TIME as create_date
        FROM cscp_proc_base
        WHERE bpm_status = 3
        AND deleted = 0
        <foreach item="item" index="index" collection="excludeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getModelByArchivist" resultType="com.ctsi.archive.entity.dto.FormModelDTO">
        select MODEL_DATA AS modelData,model_data_type AS modelDataType,MODEL_NAME AS modelName from cform_model
        where deleted = 0 and is_archivist = 1
    </select>
</mapper>
