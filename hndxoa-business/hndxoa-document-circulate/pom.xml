<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ctsi.hndxoa</groupId>
        <artifactId>hndxoa-business</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <description>公文传阅</description>

    <artifactId>hndxoa-document-circulate</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-userorg</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-file-operation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-activiti</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-userorg</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-system</artifactId>
        </dependency>
    </dependencies>

</project>