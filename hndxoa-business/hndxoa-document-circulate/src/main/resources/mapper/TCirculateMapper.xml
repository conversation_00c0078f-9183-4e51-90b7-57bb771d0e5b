<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.biaodan.mapper.TCirculateMapper">

    <resultMap id="qtcup" type="com.ctsi.biaodan.entity.dto.ResTcirculateUserDTO">
        <id column="ID" property="id"/>
        <result column="TITLE" property="title"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="SIGN_IN_STATUS" property="signInStatus"/>
        <result column="USER_NAME" property="userName"/>
        <result column="CREATE_NAME" property="createName"/>
        <result column="FROM_DATA_ID" property="fromDataId"/>
        <result column="circulate_status" property="circulateStatus"/>
        <result column="is_attach" property="isAttach"/>
        <result column="is_process" property="isProcess"/>
        <result column="process_definition_key" property="processDefinitionKey"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="root_process_instance_id" property="rootProcessInstanceId"/>
        <result column="form_id" property="formId"/>
        <result column="first_open_time" property="firstOpenTime"/>
        <result column="sign_in_time" property="signInTime"/>
    </resultMap>


    <select id="queryCirculateUserPage" parameterType="com.ctsi.biaodan.entity.dto.PageTcirculateDTO" resultMap="qtcup">
        SELECT tc.ID,tc.TITLE, tcu.CREATE_TIME, tc.circulation_summary, tcu.SIGN_IN_STATUS, tcu.DOCUMENT_REPLY,
        tc.meeting_type as meetingType ,
        tc.circulation_summary as circulationSummary,
               tc.meeting_place as meetingPlace,tc.meeting_time as meetingTime,
        tcu.CREATE_NAME,tcu.USER_NAME,tc.COMPANY_ID,tc.COMPANY_NAME,tc.DEPARTMENT_NAME,
        tc.circulate_status,tc.is_attach,tc.is_process,tc.process_definition_key,tc.process_instance_id,tc.root_process_instance_id,
        tc.form_id,tc.FROM_DATA_ID,tc.secret_level,tc.secret_level_name,tc.secret
        ,DATE_FORMAT(tcu.first_open_time,'%Y-%m-%d %H:%i:%s') as first_open_time,tcu.sign_in_time
        FROM t_circulate tc
        RIGHT JOIN t_circulate_user tcu on tc.ID = tcu.CIRCULATE_ID
        <where>
            and tc.DELETED = 0
            and tcu.DELETED = 0
            and tcu.USER_ID = #{param.userId}
            <if test="param.signInStatus == 0">
                and tcu.SIGN_IN_STATUS IN (0,2)
            </if>

            <if test="param.id != null and param.id != '' ">
                and tc.id = #{param.id}
            </if>

            <if test="param.signInStatus == 1">
                and tcu.SIGN_IN_STATUS =1
            </if>
            <if test="param.circulateStatus != null">
                and tc.circulate_status = #{param.circulateStatus}
            </if>

            and tc.termination = 0
            <if test="param.title != null and param.title != ''">
                and tc.TITLE like concat('%',#{param.title},'%')
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tc.CREATE_TIME >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tc.CREATE_TIME &lt;= #{param.endTime}
            </if>
            <if test="param.securityClassificationCode == 0">
                and (tc.secret_level_name is null or tc.secret_level_name like '%内部%' or tc.secret_level_name like '%秘密%')
            </if>
            order by tcu.CREATE_TIME desc,tcu.first_open_time desc,tcu.sign_in_time desc
        </where>
    </select>


    <select id="selectCirculateList" parameterType="com.ctsi.biaodan.entity.dto.PageTcirculateDTO" resultType="com.ctsi.biaodan.entity.TCirculate">
        select tc.ID, tc.TITLE, tc.circulation_summary, tcu.CREATE_TIME, tc.CREATE_BY, tc.CREATE_NAME, tc.UPDATE_TIME, tc.UPDATE_BY,
            tc.UPDATE_NAME, tc.DEPARTMENT_ID, tc.COMPANY_ID, tc.TENANT_ID, tc.DELETED, tc.TYPE_NAME, tc.TYPE_ID, tc.FROM_DATA_ID,
            tc.sms_reminder, tc.selection_method, tc.termination, tc.termination_time, tc.is_release, tc.COMPANY_NAME, tc.circulate_status,
            tc.is_attach, tc.is_process, tc.process_definition_key, tc.process_instance_id, tc.root_process_instance_id,tc.form_id,
            tc.DEPARTMENT_NAME, tc.secret_level, tc.is_doc, tc.is_print, tc.secret, tc.secret_level_name, tc.meeting_type, tc.meeting_time, tc.meeting_place
        FROM t_circulate tc
        LEFT JOIN (
                SELECT CIRCULATE_ID, MAX(CREATE_TIME) AS CREATE_TIME
                FROM t_circulate_user
                where DELETED = 0
                GROUP BY CIRCULATE_ID
                ) tcu
        ON tc.ID = tcu.CIRCULATE_ID
        <where>
            and tc.DELETED = 0
            and tc.CREATE_BY = #{param.userId}
            <if test="param.termination != null">
                and tc.termination != #{param.termination}
            </if>

            <if test="param.circulateStatus != null">
                and tc.circulate_status = #{param.circulateStatus}
            </if>
            <if test="param.title != null and param.title != ''">
                and tc.TITLE like concat('%',#{param.title},'%')
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tc.CREATE_TIME >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tc.CREATE_TIME &lt;= #{param.endTime}
            </if>
            <if test="param.securityClassificationCode == 0">
                and (tc.secret_level_name is null or tc.secret_level_name like '%内部%' or tc.secret_level_name like '%秘密%')
            </if>
        </where>
        order by tcu.CREATE_TIME desc

    </select>


    <select id="queryCirculateUserList" parameterType="com.ctsi.biaodan.entity.dto.PageTcirculateDTO" resultMap="qtcup">
        SELECT tc.ID,tc.TITLE, tc.CREATE_TIME, tc.circulation_summary, tcu.SIGN_IN_STATUS, tcu.DOCUMENT_REPLY,
        tcu.CREATE_NAME,tcu.USER_NAME,tc.COMPANY_ID
        FROM t_circulate tc
        RIGHT JOIN t_circulate_user tcu on tc.ID = tcu.CIRCULATE_ID
        <where>
            and tc.DELETED = 0
            and tcu.DELETED = 0
            and tcu.USER_ID = #{param.userId}
            and tcu.SIGN_IN_STATUS = #{param.whetherRead}
            and tc.termination = 0
            <if test="param.title != null and param.title != ''">
                and tc.TITLE like concat('%',#{param.title},'%')
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tc.CREATE_TIME >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tc.CREATE_TIME &lt;= #{param.endTime}
            </if>
            order by tc.CREATE_TIME desc
        </where>
    </select>

    <select id="queryReplyPage" parameterType="com.ctsi.biaodan.entity.dto.PageTcirculateDTO" resultMap="qtcup">
        SELECT tc.ID,tc.TITLE, tc.CREATE_TIME, tcu.SIGN_IN_STATUS, tcu.USER_NAME,tcu.CREATE_NAME
        FROM t_circulate tc
        RIGHT JOIN t_circulate_user tcu on tc.ID = tcu.CIRCULATE_ID
        <where>
            and tc.DELETED = 0
            and tcu.DELETED = 0
            and tcu.USER_ID = #{param.userId}
            and tcu.SIGN_IN_STATUS = 1
            and tcu.DOCUMENT_REPLY != ''
            <if test="param.title != null and param.title != ''">
                and tc.TITLE = #{param.ed.title}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tc.CREATE_TIME >= #{param.ed.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tc.CREATE_TIME &lt;= #{param.ed.endTime}
            </if>
            order by tc.CREATE_TIME desc
        </where>
    </select>

    <select id="querUnreadCirculateCount" parameterType="com.ctsi.biaodan.entity.dto.PageTcirculateDTO"
            resultType="java.lang.Long">
        SELECT tcu.id
        FROM t_circulate tc
        RIGHT JOIN t_circulate_user tcu on tc.ID = tcu.CIRCULATE_ID
        WHERE tc.DELETED = 0
        and tcu.DELETED = 0
        and tcu.USER_ID = #{param.userId}
        and tcu.SIGN_IN_STATUS = #{param.whetherRead}
        and tc.termination = 0
        order by tc.CREATE_TIME desc
    </select>
<!--    呈批件业务数据查询-->
    <select id="selectCpjById" resultType="java.util.Map">
        select title as
                   titleCld,is_print,process_instance_id,telegraph,secret,duration_classification,duration_classification_name
        from   biz_approval_management a where 1=1
        and id =#{id}
    </select>


    <!--子呈批件查询父呈批件-->
    <select id="selectParentApproval" resultType="com.ctsi.biaodan.entity.dto.QueryApprovalForwardRecordDTO">
        select base.title as FORM_MAIN_NAME,pa.task_id,pa.NODE_KEY,base.bpm_status,base.MODELKEY as process_definition_key,base.root_PROC_INST_ID AS root_process_instance_id,
        base.PROC_INST_ID AS process_instance_id, base.PROC_TYPE_NAME,base.form_data_id,base.FORM_DEF_ID  AS form_id, apm.duration_classification
        from cscp_proc_base base
        left join cscp_proc_assignee pa on base.PROC_INST_ID = pa.PROCESS_INSTANCE_ID
        left join biz_approval_management apm on base.PROC_INST_ID = apm.process_instance_id
        where base.deleted = 0
            AND base.PROC_INST_ID = #{processInstanceId}

        <!--AND pa.task_id = #{taskId}-->
    </select>
</mapper>
