<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.biaodan.mapper.TCirculateUserMapper">

    <!--查询通知公告和传阅最近选择的10个人-->
    <select id="selectRecentPeople" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserRecentDTO">
        SELECT USER_ID as ID ,USER_ID,USER_NAME,UNIT_ID,UNIT_NAME,BRANCH_ID,BRANCH_NAME,mobile,UNIT_ID as company_id,UNIT_NAME AS company_name,
        BRANCH_ID as department_id, BRANCH_NAME as department_name,CREATE_TIME from (
            SELECT
            USER_ID,USER_NAME,UNIT_ID,UNIT_NAME,BRANCH_ID,BRANCH_NAME,'' as mobile,CREATE_TIME
            FROM t_circulate_user
            WHERE deleted = 0
            AND create_by = #{userId}
            UNION ALL
            SELECT
            USER_ID,USER_NAME,UNIT_ID,UNIT_NAME,BRANCH_ID,BRANCH_NAME,mobile, CREATE_TIME
            FROM t_notice_user
            WHERE deleted = 0
            AND create_by = #{userId}
        )
        order by CREATE_TIME desc
        LIMIT 100;

    </select>
    <select id="selectUserCompanyDepart" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserRecentDTO">
        select
        cu.id,
        cu.id            as user_id,
        cu.real_name     as user_name,
        cu.mobile,
        cuo.org_id       as department_id,
        co.org_name      as department_name,
        cuo.org_id       as branch_id,
        co.org_name      as branch_name,
        cuo.company_id   as unit_id,
        cuo.company_name as unit_name,
        cuo.company_id,
        cuo.company_name
        FROM cscp_user cu
        INNER JOIN cscp_user_org cuo
        ON cu.id = cuo.user_id
        LEFT JOIN cscp_org co
        ON cuo.org_id = co.id
        WHERE cu.deleted = 0
            and cu.id = #{userId}
            and cuo.deleted = 0
            and cu.display = 1
            and cu.status = 1

    </select>
</mapper>
