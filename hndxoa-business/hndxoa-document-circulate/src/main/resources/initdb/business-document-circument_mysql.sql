CREATE TABLE `t_circulate` (
                               `ID` bigint NOT NULL DEFAULT '0' COMMENT '主键',
                               `TITLE` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                               `circulation_summary` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '传阅摘要',
                               `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                               `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                               `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                               `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                               `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                               `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                               `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                               `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                               `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                               `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                               `TYPE_NAME` int DEFAULT NULL COMMENT '类型',
                               `TYPE_ID` bigint DEFAULT NULL COMMENT '类型id',
                               `FROM_DATA_ID` bigint DEFAULT NULL COMMENT '业务id',
                               `sms_reminder` int DEFAULT NULL COMMENT '是否需要短信提醒（1：要 0：不要）',
                               `selection_method` int DEFAULT NULL COMMENT '选人方式（0：本单位 1：自定义 2：外单位）',
                               `termination` int unsigned DEFAULT '0' COMMENT '终止传阅（0：不终止 1：终止）',
                               `termination_time` datetime DEFAULT NULL COMMENT '终止时间',
                               PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公文传阅';

CREATE TABLE `t_circulate_user` (
                                    `ID` bigint NOT NULL COMMENT '主键',
                                    `USER_ID` bigint DEFAULT NULL COMMENT '用户id',
                                    `branch_id` bigint DEFAULT NULL COMMENT '部门id',
                                    `branch_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
                                    `unit_id` bigint DEFAULT NULL COMMENT '单位id',
                                    `unit_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
                                    `CIRCULATE_ID` bigint DEFAULT NULL COMMENT '传阅id',
                                    `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
                                    `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
                                    `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
                                    `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
                                    `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
                                    `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
                                    `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
                                    `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
                                    `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
                                    `DELETED` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                    `SIGN_IN_STATUS` int DEFAULT '0' COMMENT '签收状态 0:未办 1:已阅',
                                    `USER_NAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户名称',
                                    `DOCUMENT_REPLY` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公文回复',
                                    `SIGN_IN_TIME` datetime DEFAULT NULL COMMENT '签收时间',
                                    `URGING_TIME` datetime DEFAULT NULL COMMENT '催办时间',
                                    PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公文传阅签收表';

