CREATE TABLE "myapp"."t_circulate"
(
"ID" BIGINT DEFAULT 0 NOT NULL,
"TITLE" VARCHAR(50),
"circulation_summary" TEXT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" INT,
"TYPE_NAME" INT,
"TYPE_ID" BIGINT,
"FROM_DATA_ID" BIGINT,
"sms_reminder" INT,
"selection_method" INT,
"termination" BIGINT DEFAULT 0, ,
"termination_time" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("ID"),
CHECK("termination" >= 0)) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_circulate" IS '公文传阅';COMMENT ON COLUMN "myapp"."t_circulate"."ID" IS '主键';
COMMENT ON COLUMN "myapp"."t_circulate"."TITLE" IS '标题';
COMMENT ON COLUMN "myapp"."t_circulate"."circulation_summary" IS '传阅摘要';
COMMENT ON COLUMN "myapp"."t_circulate"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_circulate"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_circulate"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_circulate"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_circulate"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_circulate"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_circulate"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_circulate"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_circulate"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_circulate"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_circulate"."TYPE_NAME" IS '类型';
COMMENT ON COLUMN "myapp"."t_circulate"."TYPE_ID" IS '类型id';
COMMENT ON COLUMN "myapp"."t_circulate"."FROM_DATA_ID" IS '业务id';
COMMENT ON COLUMN "myapp"."t_circulate"."sms_reminder" IS '是否需要短信提醒（1：要 0：不要）';
COMMENT ON COLUMN "myapp"."t_circulate"."selection_method" IS '选人方式（0：本单位 1：自定义 2：外单位）';
COMMENT ON COLUMN "myapp"."t_circulate"."termination" IS '终止传阅（0：不终止 1：终止）';
COMMENT ON COLUMN "myapp"."t_circulate"."termination_time" IS '终止时间';




CREATE TABLE "myapp"."t_circulate_user"
(
"ID" BIGINT NOT NULL,
"USER_ID" BIGINT,
"branch_id" BIGINT,
"branch_name" VARCHAR(100),
"unit_id" BIGINT,
"unit_name" VARCHAR(100),
"CIRCULATE_ID" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" INT,
"SIGN_IN_STATUS" INT DEFAULT 0,
"USER_NAME" VARCHAR(5, 0),
"DOCUMENT_REPLY" VARCHAR(255),
"SIGN_IN_TIME" TIMESTAMP(0),
"URGING_TIME" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_circulate_user" IS '公文传阅签收表';COMMENT ON COLUMN "myapp"."t_circulate_user"."ID" IS '主键';
COMMENT ON COLUMN "myapp"."t_circulate_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."branch_id" IS '部门id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."branch_name" IS '部门名称';
COMMENT ON COLUMN "myapp"."t_circulate_user"."unit_id" IS '单位id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."unit_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."t_circulate_user"."CIRCULATE_ID" IS '传阅id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_circulate_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_circulate_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_circulate_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_circulate_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_circulate_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_circulate_user"."SIGN_IN_STATUS" IS '签收状态 0:未办 1:已阅';
COMMENT ON COLUMN "myapp"."t_circulate_user"."USER_NAME" IS '用户名称';
COMMENT ON COLUMN "myapp"."t_circulate_user"."DOCUMENT_REPLY" IS '公文回复';
COMMENT ON COLUMN "myapp"."t_circulate_user"."SIGN_IN_TIME" IS '签收时间';
COMMENT ON COLUMN "myapp"."t_circulate_user"."URGING_TIME" IS '催办时间';




