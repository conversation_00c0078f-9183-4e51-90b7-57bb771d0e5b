CREATE TABLE "public"."t_circulate" (
                                        ID int8 NOT NULL DEFAULT 0,
                                        TITLE varchar(50 char) NULL,
	"circulation_summary" text NULL,
	CREATE_TIME timestamp(6) NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp(6) NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	TYPE_NAME int4 NULL,
	TYPE_ID int8 NULL,
	FROM_DATA_ID int8 NULL,
	"sms_reminder" int4 NULL,
	"selection_method" int4 NULL,
	"termination" int8 NULL DEFAULT 0,
	"termination_time" timestamp(6) NULL,
	CONSTRAINT "t_circulate_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_circulate"."termination_time" IS '终止时间';
COMMENT ON COLUMN "public"."t_circulate"."termination" IS '终止传阅（0：不终止 1：终止）';
COMMENT ON COLUMN "public"."t_circulate"."selection_method" IS '选人方式（0：本单位 1：自定义 2：外单位）';
COMMENT ON COLUMN "public"."t_circulate"."sms_reminder" IS '是否需要短信提醒（1：要 0：不要）';
COMMENT ON COLUMN "public"."t_circulate"."FROM_DATA_ID" IS '业务id';
COMMENT ON COLUMN "public"."t_circulate"."TYPE_ID" IS '类型id';
COMMENT ON COLUMN "public"."t_circulate"."TYPE_NAME" IS '类型';
COMMENT ON COLUMN "public"."t_circulate"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_circulate"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."t_circulate"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."t_circulate"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."t_circulate"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."t_circulate"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."t_circulate"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."t_circulate"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_circulate"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."t_circulate"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_circulate"."circulation_summary" IS '传阅摘要';
COMMENT ON COLUMN "public"."t_circulate"."TITLE" IS '标题';
COMMENT ON COLUMN "public"."t_circulate"."ID" IS '主键';
COMMENT ON TABLE "public"."t_circulate" IS '公文传阅';


CREATE TABLE "public"."t_circulate_user" (
                                             ID int8 NOT NULL,
                                             USER_ID int8 NULL,
                                             "branch_id" int8 NULL,
                                             "branch_name" varchar(100 char) NULL,
	"unit_id" int8 NULL,
	"unit_name" varchar(100 char) NULL,
	CIRCULATE_ID int8 NULL,
	CREATE_TIME timestamp(6) NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp(6) NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	SIGN_IN_STATUS int4 NULL DEFAULT 0,
	USER_NAME varchar(50 char) NULL,
	DOCUMENT_REPLY varchar(255 char) NULL,
	SIGN_IN_TIME timestamp(6) NULL,
	URGING_TIME timestamp(6) NULL,
	CONSTRAINT "t_circulate_user_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_circulate_user"."URGING_TIME" IS '催办时间';
COMMENT ON COLUMN "public"."t_circulate_user"."SIGN_IN_TIME" IS '签收时间';
COMMENT ON COLUMN "public"."t_circulate_user"."DOCUMENT_REPLY" IS '公文回复';
COMMENT ON COLUMN "public"."t_circulate_user"."USER_NAME" IS '用户名称';
COMMENT ON COLUMN "public"."t_circulate_user"."SIGN_IN_STATUS" IS '签收状态 0:未办 1:已阅';
COMMENT ON COLUMN "public"."t_circulate_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_circulate_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."t_circulate_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."t_circulate_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."t_circulate_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."t_circulate_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."t_circulate_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."t_circulate_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_circulate_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."t_circulate_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_circulate_user"."CIRCULATE_ID" IS '传阅id';
COMMENT ON COLUMN "public"."t_circulate_user"."unit_name" IS '单位名称';
COMMENT ON COLUMN "public"."t_circulate_user"."unit_id" IS '单位id';
COMMENT ON COLUMN "public"."t_circulate_user"."branch_name" IS '部门名称';
COMMENT ON COLUMN "public"."t_circulate_user"."branch_id" IS '部门id';
COMMENT ON COLUMN "public"."t_circulate_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "public"."t_circulate_user"."ID" IS '主键';
COMMENT ON TABLE "public"."t_circulate_user" IS '公文传阅签收表';
