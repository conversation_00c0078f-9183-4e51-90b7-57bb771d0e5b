package com.ctsi.biaodan.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.biaodan.entity.TCirculate;
import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.biaodan.mapper.TCirculateMapper;
import com.ctsi.biaodan.mapper.TCirculateUserMapper;
import com.ctsi.biaodan.service.ITCirculateUserService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.constant.UserConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.RestTemplateRequestJWT;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpUserRecentDTO;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.SpringUtil;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */

@Slf4j
@Service
public class TCirculateUserServiceImpl extends SysBaseServiceImpl<TCirculateUserMapper, TCirculateUser> implements ITCirculateUserService {

    @Autowired
    private TCirculateUserMapper tCirculateUserMapper;

    @Autowired
    private TCirculateMapper tCirculateMapper;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private com.ctsi.ssdc.admin.service.ITUserFocusService userFocusService;
    @Autowired
    private ITSysDictRecordService tSysDictRecordService;


    /**
     * 查询未阅的公文传阅
     *
     * @param circulate
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResTcirculateUserDTO> queryListPage(PageTcirculateDTO circulate, BasePageForm basePageForm) {
        long userId = SecurityUtils.getCurrentUserId();
        CscpUser cscpUser = cscpUserService.getById(userId);
        if(cscpUser == null){
            throw new BusinessException("用户不存在");
        }
        circulate.setUserId(userId);
        circulate.setSignInStatus(0);
        circulate.setSecurityClassificationCode(cscpUser.getSecurityClassificationCode());
        // TODO: 2023/5/5 wubin 这里直接取出数据就行，不需要再重新获取单位名称
        IPage<ResTcirculateUserDTO> iPage = tCirculateMapper.queryCirculateUserPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), circulate);
        //获取对应的单位
//        iPage.getRecords().stream().forEach(i -> {
//            CscpOrg cscpOrg = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getOrgName).eq(CscpOrg::getId, i.getCompanyId()));
//            i.setCompanyName(cscpOrg.getOrgName());
//        });

        // 获取密级期限数据字典
        Map<String, String> tSysDictMap = this.getTSysDictMap();
        // collect 设置传阅人员信息
        String template = "{}/{}";
        for (ResTcirculateUserDTO data : iPage.getRecords()) {
            LambdaQueryWrapper<TCirculateUser> qw = new LambdaQueryWrapper();
            qw.eq(TCirculateUser::getCirculateId,data.getId());
            List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectListNoAdd(qw);
            int openCount = 0;
            int signCount = 0;
            // 设置打开和签收情况
            for (TCirculateUser cUser : tCirculateUserList) {
                if (ObjectUtil.equal(cUser.getSignInStatus(), 2) || ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                    // 统计已打开人数
                    openCount =openCount+1;
                }
                if (ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                    // 统计已确认人数
                    signCount =signCount+1;
                }
            }
            data.setOpenStatistics(StrUtil.format(template,openCount,tCirculateUserList.size()));
            data.setSignStatistics(StrUtil.format(template,signCount,tCirculateUserList.size()));

            // 设置接收传阅人员信息
            List<String> userList =tCirculateUserList.stream().map(i -> i.getUserName()).collect(Collectors.toList());
            data.setUserList(userList);
            String secretLevel = tSysDictMap.get(data.getSecret());
            if(StringUtils.isNotBlank(secretLevel)){
                data.setSecretLevelCode(data.getSecret());
                data.setSecretLevel(secretLevel);
            }else{
                data.setSecretLevelCode(data.getSecretLevel());
                data.setSecretLevel(data.getSecretLevelName());
            }
        }
        // TODO 设置密级期限
        return new PageResult<>(iPage.getRecords(), iPage.getTotal(), iPage.getCurrent());
    }

    /**
     * 签收公文传阅
     *
     * @param signForDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer signFor(SignForDTO signForDTO) {
        TCirculateUser tCirculateUser = new TCirculateUser();

        LambdaQueryWrapper<TCirculateUser> circulateUser = new LambdaQueryWrapper<>();
        circulateUser.eq(TCirculateUser::getCirculateId, signForDTO.getId());

        // 签收的走接收人查询
        circulateUser.eq(TCirculateUser::getUserId, SecurityUtils.getCurrentUserId());
        // 未传值则默认已读未签收
        Integer signInStatus = signForDTO.getSignInStatus();

        tCirculateUser.setSignInStatus(signInStatus);


        if(signInStatus !=null && signInStatus.intValue() == 2){ // 不知道是不是首次 若没有首次打开时间 则是首次
            List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(circulateUser);
            if(tCirculateUsers.size() > 0){
                TCirculateUser tCirculateUser1 = tCirculateUsers.get(0);
                LocalDateTime firstOpenTime = tCirculateUser1.getFirstOpenTime();
                if(null == firstOpenTime){
                    tCirculateUser.setFirstOpenTime(LocalDateTime.now());
                }
            }
        }

        if(signInStatus !=null && signInStatus.intValue() == 1){  // 签收
            // 签收时间
            tCirculateUser.setSignInTime(LocalDateTime.now());
            // 回复内容
            tCirculateUser.setDocumentReply(signForDTO.getReplyData());
        }

        return tCirculateUserMapper.update(tCirculateUser, circulateUser);
    }

    /**
     * 查询未阅的公文传阅
     *
     * @param circulate
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResTcirculateUserDTO> queryReadPage(PageTcirculateDTO circulate, BasePageForm basePageForm) {
        long userId = SecurityUtils.getCurrentUserId();
        CscpUser cscpUser = cscpUserService.getById(userId);
        if(cscpUser == null){
            throw new BusinessException("用户不存在");
        }
        circulate.setUserId(SecurityUtils.getCurrentUserId());
        circulate.setSecurityClassificationCode(cscpUser.getSecurityClassificationCode());
        IPage<ResTcirculateUserDTO> iPage = tCirculateMapper.queryCirculateUserPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), circulate);
        //获取对应的单位
//        iPage.getRecords().stream().forEach(i -> {
//            CscpOrg cscpOrg = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().select(CscpOrg::getOrgName).eq(CscpOrg::getId, i.getCompanyId()));
//            i.setCompanyName(cscpOrg.getOrgName());
//        });

        //返回
        List<ResTcirculateUserDTO> records = iPage.getRecords();
        List<Long> dataids = records.stream().map(i -> i.getId()).collect(Collectors.toList());

        List<TUserFocusDTO> tUserFocusDTOS = userFocusService.queryListByDataIds(dataids);
        tUserFocusDTOS.forEach(i -> {
            Long id = i.getDataId();
            records.forEach(j ->{
                if(j.getId().longValue()==id.longValue() ){
                    j.setIsFocus(true);
                }
            });
        });

        // 获取密级期限数据字典
        Map<String, String> tSysDictMap = this.getTSysDictMap();
        // collect 设置传阅人员信息
        for (ResTcirculateUserDTO data : records) {
            LambdaQueryWrapper<TCirculateUser> qw = new LambdaQueryWrapper();
            qw.eq(TCirculateUser::getCirculateId,data.getId());
            List<String> userList =
                    tCirculateUserMapper.selectListNoAdd(qw).stream().map(i -> i.getUserName()).collect(Collectors.toList());
            data.setUserList(userList);
            data.setSecretLevelCode(data.getSecretLevel());
            data.setSecretLevel(tSysDictMap.get(data.getSecretLevel()));
        }

        return new PageResult<>(records , iPage.getTotal(), iPage.getCurrent());
    }

    /**
     * 回复传阅
     *
     * @param documentReplyDTO
     * @return
     */
    @Override
    public Integer documentReply(DocumentReplyDTO documentReplyDTO) {
//        TCirculateUser tCirculateUser = BeanConvertUtils.copyProperties(documentReplyDTO, TCirculateUser.class);
//        return tCirculateUserMapper.updateTenantId(tCirculateUser, new LambdaQueryWrapper<TCirculateUser>().eq(TCirculateUser::getCirculateId, documentReplyDTO.getId()));
        TCirculateUser tCirculateUser = new TCirculateUser();
        //回复内容
        String replyContent = documentReplyDTO.getDocumentReply();
        if (null == replyContent || "".equals(replyContent)) {
            replyContent = documentReplyDTO.getReplyContent();
        }
        tCirculateUser.setDocumentReply(replyContent);
        LambdaQueryWrapper<TCirculateUser> circulateUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        circulateUserLambdaQueryWrapper.eq(TCirculateUser::getCirculateId, documentReplyDTO.getId());
        circulateUserLambdaQueryWrapper.eq(TCirculateUser::getUserId, SecurityUtils.getCurrentUserId());
        return tCirculateUserMapper.updateTenantId(tCirculateUser, circulateUserLambdaQueryWrapper);
    }

    /**
     * 公文回复
     *
     * @param pageTcirculateDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TCirculateUserDTO> queryReplyPage(PageTcirculateDTO pageTcirculateDTO, BasePageForm basePageForm) {
        pageTcirculateDTO.setUserId(SecurityUtils.getCurrentUserId());
        IPage<TCirculateUserDTO> iPage = tCirculateMapper.queryReplyPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), pageTcirculateDTO);
        return new PageResult<TCirculateUserDTO>(iPage.getRecords(), iPage.getTotal(), iPage.getCurrent());
    }

    /**
     * 一键催办
     *
     * @param querysignForDTO
     * @return
     */
    @Override
    public Integer onekeyUrge(QuerysignForDTO querysignForDTO) {
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, querysignForDTO.getId());
        lambdaQueryWrapper.eq(TCirculateUser::getCreateBy, SecurityUtils.getCurrentUserId());
        //查询本单位
        lambdaQueryWrapper.eq(0 == querysignForDTO.getQueryType(), TCirculateUser::getUnitId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        //查询外单位
        lambdaQueryWrapper.ne(1 == querysignForDTO.getQueryType(), TCirculateUser::getUnitId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId());

        lambdaQueryWrapper.eq(TCirculateUser::getSignInStatus, 0);
        lambdaQueryWrapper.select(TCirculateUser::getId, TCirculateUser::getUserId);
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper);

        if (tCirculateUsers.isEmpty()) {
            throw new BusinessException("用户全部已阅!");
        }

        //获取所有手机号码
        List<String> cscpUserMoileAll = cscpUserService.getCscpUserMoileAll(tCirculateUsers.stream().map(i -> i.getUserId()).collect(Collectors.toList()));

        TCirculate tCirculate = tCirculateMapper.selectOneNoAdd(new LambdaQueryWrapper<TCirculate>().eq(TCirculate::getId, querysignForDTO.getId()));

        //发送短信
        try {
            StringBuffer stringBuffer = new StringBuffer("您有一份标题“");
            stringBuffer.append(tCirculate.getTitle()).append("”的传阅待查阅，请登录协同办公平台进行查阅。");
            Map<String, Object> pushMap = new HashMap<>();
            pushMap.put("moileList", cscpUserMoileAll);
            pushMap.put("titile", stringBuffer.toString());
            String url = SysConstant.sendSmsByPhoneListApi.replace(SysConstant.replaceValue, "tSysSms");
            ResultVO responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + url, pushMap, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new IllegalArgumentException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<TCirculateUser> collect = tCirculateUsers.stream().map(i -> {
            TCirculateUser tCirculateUser = new TCirculateUser();
            tCirculateUser.setId(i.getId());
            tCirculateUser.setUrgingTime(LocalDateTime.now());
            return tCirculateUser;
        }).collect(Collectors.toList());

        return this.saveOrUpdateBatch(collect) ? 1 : 0;
    }

    @Override
    public Integer urge(UrgeDTO urge) {
        CscpUser cscpUser = cscpUserService.getById(urge.getUserId());
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("用户不存在");
        }
        if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
            throw new BusinessException("用户已被禁用");
        }

        try {
            List<String> cscpUserMoileAll = new ArrayList<>();
            cscpUserMoileAll.add(cscpUser.getMobile());
            StringBuffer stringBuffer = new StringBuffer("您有一份标题《");
            stringBuffer.append(urge.getTitle()).append("》的传阅待查阅，请登录协同办公平台进行查阅。");
            Map<String, Object> pushMap = new HashMap<>();
            pushMap.put("moileList", cscpUserMoileAll);
            pushMap.put("titile", stringBuffer.toString());
            String url = SysConstant.sendSmsByPhoneListApi.replace(SysConstant.replaceValue, "tSysSms");
            ResultVO responseBO = RestTemplateRequestJWT.post(SpringUtil.getLocalUrlPort() + url, pushMap, ResultVO.class);
            if (!ResultCode.SUCCESS.code().equals(responseBO.getResultCode())) {
                throw new IllegalArgumentException(responseBO.getResultMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        TCirculateUser tCirculateUser = new TCirculateUser();
        tCirculateUser.setId(urge.getId());
        tCirculateUser.setUrgingTime(LocalDateTime.now());
        return tCirculateUserMapper.updateById(tCirculateUser) > 0 ? 1 : 0;
    }

    /**
     * app查询角标（未看公文）
     *
     * @return
     */
    @Override
    public Integer getAngleMark() {
        return getAngleMark(null);
    }

    @Override
    public Integer getAngleMark(Long id) {
        PageTcirculateDTO circulate = new PageTcirculateDTO();
        // circulate.setUserId(SecurityUtils.getCurrentUserId());
        circulate.setUserId( null == id ? SecurityUtils.getCurrentUserId() : id);
        circulate.setWhetherRead(0);
        Integer integer = tCirculateMapper.querUnreadCirculateCount(circulate).size();
        return integer;
    }

    /**
     * 查询未读传阅条数
     *
     * @return
     */
    @Override
    public Integer queryUnreadCirculationNumber() {
        //组装条件
        PageTcirculateDTO circulate = new PageTcirculateDTO();
        circulate.setUserId(SecurityUtils.getCurrentUserId());
        circulate.setWhetherRead(0);

        //查询未阅的公文
        List<ResTcirculateUserDTO> circulateList = Optional.ofNullable(tCirculateMapper.queryCirculateUserList(circulate))
                .orElse(new LinkedList<>());

        return circulateList.size();
    }

    @Override
    public List<TCirculateUser> getListByFormDataId(Long formDataId) {
        LambdaQueryWrapper<TCirculate> lambdaCirculateQueryWrapper = new LambdaQueryWrapper<>();
        lambdaCirculateQueryWrapper.eq(TCirculate::getFromDataId, formDataId);
        TCirculate tCirculate = tCirculateMapper.selectOneNoAdd(lambdaCirculateQueryWrapper);
        if(tCirculate != null){
            LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, tCirculate.getId());
            lambdaQueryWrapper.eq(TCirculateUser::getSignInStatus,1);
            List<TCirculateUser> list = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper);
            return list;
        }else{
            return null;
        }
    }

    /**
     * 查询当前用户在传阅和通知公告中最近发送的10个人
     */
    @Override
    public List<CscpUserRecentDTO> getCirculateNoticeRecentPeople() {
        List<CscpUserRecentDTO> list = tCirculateUserMapper.selectRecentPeople(10,SecurityUtils.getCurrentUserId());
        if (CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }

        list = list.stream()
                .filter(distinctByKey(CscpUserRecentDTO::getUserId))
                .sorted(Comparator.comparing(CscpUserRecentDTO::getCreateTime).reversed())
                .limit(10).collect(Collectors.toList());
        for (CscpUserRecentDTO dto : list) {
            if (dto.getUnitId()==null||dto.getBranchId()==null || StringUtils.isBlank(dto.getMobile())){
                // 确定单位和部门信息的查询后补充完整
                List<CscpUserRecentDTO> cscpUserRecentDTOList = tCirculateUserMapper.selectUserCompanyDepart(dto.getUserId());
                if (CollectionUtil.isNotEmpty(cscpUserRecentDTOList)) {
                    BeanUtils.copyProperties(cscpUserRecentDTOList.get(0), dto);
                    dto.setUserName(KeyCenterUtils.decrypt(dto.getUserName()));
                    try {
                        dto.setMobile(new String(Base64.getDecoder().decode(cscpUserRecentDTOList.get(0).getMobile()), StandardCharsets.UTF_8));
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return list;
    }

    public <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    /**
     * 查询公文传阅类型
     */
    @Override
    public List<Map> getCirculateType(){
        List<TCirculateTypeEnum> allCirculateTypes = TCirculateTypeEnum.getAllCirculateTypes();
        // 项目前期需求未定，暂用枚举和map替代类型管理
        return allCirculateTypes.stream().map(i->{
            HashMap<String, Object> map = new HashMap<>();
            map.put("circulateStatus", i.getCirculateStatus());
            map.put("circulateType", i.getCirculateType());
            return map;
        }).collect(Collectors.toList());
    }

    @Override
    public TCirculateUser selectMyRecord(Long id , long userId) {
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, id)
                .eq(TCirculateUser::getUserId,userId);
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper);
        return tCirculateUsers.isEmpty() ? new TCirculateUser() : tCirculateUsers.get(0);
    }

    @Override
    public List<TCirculateUser> selectListByCyIdAndType(Long cyid , Integer typeCy) {
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, cyid)
                .eq(TCirculateUser::getSignInStatus,0)
        ;
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper);
        return tCirculateUsers;
    }

    /**
     * 查询密集期限数据字典
     * */
    private Map<String, String> getTSysDictMap(){
        List<TSysDictRecordDTO> dictRecordList = tSysDictRecordService.getDictRecordListByDictCode("mjqx", SecurityUtils.getCurrentCompanyId());
        Map<String, String> dictMap = dictRecordList.stream().collect(Collectors.toMap(TSysDictRecordDTO::getCode,TSysDictRecordDTO ::getName,(v1, v2) -> v1));
        return dictMap;
    }


    /**
     * 查询当前用户在传阅总最近发送的10个人
     */
    public List<CscpUserRecentDTO> getCirculateRecentPeople() {

        QueryWrapper<TCirculateUser> query = new QueryWrapper<>();
        query.select("ID,USER_ID,USER_NAME,UNIT_ID,UNIT_NAME,BRANCH_ID,BRANCH_NAME,CREATE_TIME").lambda()
                .eq(TCirculateUser::getCreateBy, SecurityUtils.getCurrentUserId())
                .orderByDesc(TCirculateUser::getCreateTime);
        List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectList(query);
        List<CscpUserRecentDTO> list = tCirculateUserList.stream()
                .filter(distinctByKey(TCirculateUser::getUserId))
                .sorted(Comparator.comparing(TCirculateUser::getCreateTime).reversed())
                .map(x -> {
                    CscpUserRecentDTO dto = new CscpUserRecentDTO();
                    dto.setId(x.getId());
                    dto.setUserId(x.getUserId());
                    dto.setUserName(x.getUserName());
                    dto.setUnitId(x.getUnitId());
                    dto.setUnitName(x.getUnitName());
                    dto.setBranchId(x.getBranchId());
                    dto.setBranchName(x.getBranchName());

                    dto.setCompanyId(x.getUnitId());
                    dto.setCompanyName(x.getUnitName());
                    dto.setDepartmentId(x.getBranchId());
                    dto.setDepartmentName(x.getBranchName());
                    return dto;
                }).limit(10).collect(Collectors.toList());

        return list;
    }

}
