package com.ctsi.biaodan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.biaodan.entity.NotifiCountHistory;
import com.ctsi.biaodan.entity.dto.NotifiCountHistoryDTO;
import com.ctsi.biaodan.mapper.NotifiCountHistoryMapper;
import com.ctsi.biaodan.service.INotifiCountHistoryService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.ListCopyUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户关注 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
@Service
public class NotifiCountHistoryServiceImpl extends SysBaseServiceImpl<NotifiCountHistoryMapper, NotifiCountHistory> implements INotifiCountHistoryService {

	@Override
	public List<NotifiCountHistoryDTO> queryList(NotifiCountHistoryDTO dto) {
		LambdaQueryWrapper<NotifiCountHistory> queryWrapper = new LambdaQueryWrapper();
		queryWrapper.eq(NotifiCountHistory::getPid,dto.getPid())
				.orderByDesc(NotifiCountHistory::getCreateTime)
				;
		List<NotifiCountHistory> listData = selectListNoAdd(queryWrapper);
		return ListCopyUtil.copy(listData,NotifiCountHistoryDTO.class);
	}
}
