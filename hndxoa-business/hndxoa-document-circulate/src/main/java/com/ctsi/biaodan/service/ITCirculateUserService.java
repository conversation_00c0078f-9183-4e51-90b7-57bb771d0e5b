package com.ctsi.biaodan.service;

import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.admin.domain.dto.CscpUserRecentDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface ITCirculateUserService extends SysBaseServiceI<TCirculateUser> {


    /**
     * 查询未阅的公文传阅
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<ResTcirculateUserDTO> queryListPage(PageTcirculateDTO entityDTO, BasePageForm page);


    /**
     * 签收公文传阅
     *
     * @param signForDTO
     * @return
     */
    Integer signFor(SignForDTO signForDTO);


    /**
     * 查询未阅的公文传阅
     *
     * @param tCirculateUserDTO
     * @param basePageForm
     * @return
     */
    PageResult<ResTcirculateUserDTO> queryReadPage(PageTcirculateDTO tCirculateUserDTO, BasePageForm basePageForm);

    /**
     * 回复传阅
     *
     * @param documentReplyDTO
     * @return
     */
    Integer documentReply(DocumentReplyDTO documentReplyDTO);


    /**
     * 公文回复
     *
     * @param tCirculateUserDTO
     * @param basePageForm
     * @return
     */
    PageResult<TCirculateUserDTO> queryReplyPage(PageTcirculateDTO tCirculateUserDTO, BasePageForm basePageForm);

    /**
     * 一键催办
     *
     * @param querysignForDTO
     * @return
     */
    Integer onekeyUrge(QuerysignForDTO querysignForDTO);

    /**
     * 催办
     *
     * @return
     */
    Integer urge(UrgeDTO urge);

    /**
     * app查询角标（未看公文）
     *
     * @return
     */
    Integer getAngleMark();

    Integer getAngleMark(Long id);
    /**
     * 查询未读传阅条数
     *
     * @return
     */
    Integer queryUnreadCirculationNumber();

    List<TCirculateUser> getListByFormDataId(Long formDataId);

    /**
     * 查询当前用户在传阅和通知公告中最近发送的10个人
     */
    List<CscpUserRecentDTO> getCirculateNoticeRecentPeople();

    /**
     * 查询公文传阅类型
     */
    List<Map> getCirculateType();

    /**
     *
     * @param id  传阅 主表id
     * @param currentUserId 用户id
     * @return
     */
    TCirculateUser selectMyRecord(Long id , long currentUserId);


	List<TCirculateUser> selectListByCyIdAndType(Long documentId , Integer typeCy);
}
