package com.ctsi.biaodan.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Classname QuerysignForDTO
 * @Description
 * @Date 2021/12/23 16:40
 */

@Data
@ApiModel(value = "查阅详情", description = "")
public class QuerysignForDTO {

    @ApiModelProperty(value = "签收主键id", required = true)
    @NotNull(message = "签收主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "公文id", required = true)
    @NotNull(message = "公文id不能为空")
    private Long circulateId;

    @ApiModelProperty(value = "查询类型（0 ：本单位 1：外单位）", required = true)
    @NotNull(message = "查询类型不能为空")
    private Integer queryType;

    @ApiModelProperty(value = "条件查询 姓名")
    private String userName;


    /**
     * 0-未打开(未办)，1-已确认，2-已打开未确认，3-未确认，4-查询全部
     */
    @ApiModelProperty(value = "0-未打开(未办)，1-已确认，2-已打开未确认   <未确认需要动态计算,总数-确认数量>    查询传9 表示查 未签收的数据")
    private Integer signInStatus;
}
