package com.ctsi.biaodan.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.biaodan.entity.BizApprovalForwardRecord;
import com.ctsi.biaodan.entity.NotifiCountHistory;
import com.ctsi.biaodan.entity.TCirculate;
import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.biaodan.mapper.BizApprovalForwardRecordMapper;
import com.ctsi.biaodan.mapper.TCirculateMapper;
import com.ctsi.biaodan.mapper.TCirculateUserMapper;
import com.ctsi.biaodan.service.ITCirculateService;
import com.ctsi.biaodan.service.ITCirculateUserService;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.ApprovalForwardTypeEnums;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpDocumentFileMapper;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.sms.smssend.SwSmsSendUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpDistributeBaseUserDTO;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.entity.SwSmsNotificationRecords;
import com.ctsi.ssdc.entity.dto.SwSmsNotificationRecordsBaseDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.BizService;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */

@Slf4j
@Service
public class TCirculateServiceImpl extends SysBaseServiceImpl<TCirculateMapper, TCirculate> implements ITCirculateService {

    @Autowired
    private TCirculateMapper tCirculateMapper;

    @Autowired
    private TCirculateUserMapper tCirculateUserMapper;

    @Autowired
    private ITCirculateUserService itCirculateUserService;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileMapper;
    @Autowired
    private com.ctsi.operation.service.CscpEnclosureFileService enclosureFileService;


    @Autowired
    private CscpDocumentFileMapper cscpDocumentFileMapper;
    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private CscpEnclosureFileMapper cscpFormFileRepository;
    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;

    @Autowired
    private CscpUserRepository cscpUserRepository;


    @Autowired
    private CscpProcBaseService cscpProcBaseService;
    @Autowired
    private com.ctsi.ssdc.admin.service.ITUserFocusService userFocusService;
    @Autowired
    private ITSysDictRecordService tSysDictRecordService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CscpOrgService cscpOrgService;


    @Autowired
    private com.ctsi.ssdc.repository.SwSmsNotificationRecordsMapper smsNotificationRecordsMapper;

    @Autowired
    private com.ctsi.biaodan.service.INotifiCountHistoryService notifiCountHistoryService;

    @Autowired
    private com.ctsi.business.repository.CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    private BizApprovalForwardRecordMapper approvalForwardRecordMapper;

    @Autowired
    private BizService bizService;

    /**
     * 翻页查询，我发布的传阅
     *     需要增加传阅人员列表
     * @param
     * @param
     * @return
     */
    @Override
    public PageResult<PageResTcirculateDTO> queryListPage(PageTcirculateDTO entityDTO, BasePageForm basePageForm) {
        CscpUser cscpUser = cscpUserService.getById(SecurityUtils.getCurrentUserId());
        if(cscpUser == null){
            throw new BusinessException("用户不存在");
        }

        entityDTO.setSecurityClassificationCode(cscpUser.getSecurityClassificationCode());
        entityDTO.setUserId(SecurityUtils.getCurrentUserId());

        ////设置条件
        //LambdaQueryWrapper<TCirculate> queryWrapper = new LambdaQueryWrapper();
        //queryWrapper.eq(TCirculate::getCreateBy, SecurityUtils.getCurrentUserId());
        //queryWrapper.eq(entityDTO.getCirculateStatus() != null, TCirculate::getCirculateStatus, entityDTO.getCirculateStatus());
        //// 首页-我发起的排除已终止的
        //queryWrapper.ne(entityDTO.getTermination() != null,TCirculate::getTermination, entityDTO.getTermination());
        //queryWrapper.orderByDesc(TCirculate::getCreateTime);
        //
        //queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), TCirculate::getTitle, entityDTO.getTitle());
        //
        //queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), TCirculate::getCreateTime, entityDTO.getStartTime());
        //
        //queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), TCirculate::getCreateTime, entityDTO.getEndTime());
        //
        //
        //if (Objects.equals(cscpUser.getSecurityClassificationCode(), "0")) {
        //    queryWrapper.isNull(TCirculate::getSecretLevelName)
        //            .and(wq->wq.like(TCirculate::getSecretLevelName, "内部").or().like(TCirculate::getSecretLevelName, "秘密"));
        //}
        //
        //IPage<TCirculate> pageData = tCirculateMapper.selectPage(
        //        PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        IPage<TCirculate> pageData = tCirculateMapper.selectCirculateList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);

        List<PageResTcirculateDTO> collect = pageData.getRecords().stream().map(i -> BeanConvertUtils.copyProperties(i, PageResTcirculateDTO.class)).collect(Collectors.toList());
        //返回
        List<Long> dataids = collect.stream().map(i -> i.getId()).collect(Collectors.toList());

        List<TUserFocusDTO> tUserFocusDTOS = userFocusService.queryListByDataIds(dataids);
        tUserFocusDTOS.forEach(i -> {
            Long id = i.getDataId();
            collect.forEach(j -> {
                if (j.getId().longValue() == id.longValue()) {
                    j.setIsFocus(true);
                }
            });
        });

        // 获取密级期限数据字典
        Map<String, String> tSysDictMap = this.getTSysDictMap();


        // collect 设置传阅人员信息
        // for (PageResTcirculateDTO data : collect) {
        //     LambdaQueryWrapper<TCirculateUser> qw = new LambdaQueryWrapper();
        //     qw.eq(TCirculateUser::getCirculateId,data.getId());
        //     List<String> userList =
        //             tCirculateUserMapper.selectListNoAdd(qw).stream().map(i -> i.getUserName()).collect(Collectors.toList());
        //     data.setUserList(userList);
        //     // 设置密级
        //     data.setSecretLevel(tSysDictMap.get(data.getSecretLevel()));
        // }


        // 获取密级期限数据字典
        // collect 设置传阅人员信息
        String template = "{}/{}";
        for (PageResTcirculateDTO data : collect) {
            LambdaQueryWrapper<TCirculateUser> qw = new LambdaQueryWrapper();
            qw.eq(TCirculateUser::getCirculateId,data.getId());
            List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectListNoAdd(qw);
            int openCount = 0;
            int signCount = 0;
            // 设置打开和签收情况
            for (TCirculateUser cUser : tCirculateUserList) {
                if (ObjectUtil.equal(cUser.getSignInStatus(), 2) || ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                    // 统计已打开人数
                    openCount =openCount+1;
                }
                if (ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                    // 统计已确认人数
                    signCount =signCount+1;
                }
            }
            data.setOpenStatistics(StrUtil.format(template,openCount,tCirculateUserList.size()));
            data.setOpenStatus(openCount != tCirculateUserList.size() ? 1 : 2);

            data.setSignStatistics(StrUtil.format(template,signCount,tCirculateUserList.size()));
            data.setSignStatus(signCount != tCirculateUserList.size() ? 1 : 2);

            // 设置接收传阅人员信息
            List<String> userList =tCirculateUserList.stream().map(i -> i.getUserName()).collect(Collectors.toList());
            data.setUserList(userList);
            String secretLevel = tSysDictMap.get(data.getSecretLevel());
            if (StringUtils.isNotEmpty(secretLevel)){
                data.setSecretLevel(secretLevel);
            }else {
                data.setSecretLevel(data.getSecretLevel());
            }
        }

        return new PageResult<PageResTcirculateDTO>(collect, pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param
     * @return
     */
    @Override
    public List<TCirculateDTO> queryList(TCirculateDTO entityDTO) {
        LambdaQueryWrapper<TCirculate> queryWrapper = new LambdaQueryWrapper();
        List<TCirculate> listData = tCirculateMapper.selectList(queryWrapper);
        List<TCirculateDTO> TCirculateDTOList = ListCopyUtil.copy(listData, TCirculateDTO.class);
        return TCirculateDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TCirculateDTO findOne(Long id) {
        TCirculate tCirculate = tCirculateMapper.selectById(id);
        TCirculateDTO tCirculateDTO = BeanConvertUtils.copyProperties(tCirculate, TCirculateDTO.class);
        // 获取密级期限数据字典
        Map<String, String> tSysDictMap = this.getTSysDictMap();
        // 设置密级
        tCirculateDTO.setSecretLevel(tSysDictMap.get(tCirculateDTO.getSecretLevel()));
        return tCirculateDTO;
    }


    /**
     * 新增
     *
     * @param
     * @return
     */
    @Override
    @Transactional
    public TCirculateDTO create(TCirculateDTO entityDTO) {
        //新增公文传阅
        TCirculate tCirculate = BeanConvertUtils.copyProperties(entityDTO, TCirculate.class);
        //获取业务所在单位
        String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
        tCirculate.setCompanyName(companyName);
        tCirculate.setDepartmentName(SecurityUtils.getCurrentCscpUserDetail().getDepartmentName());
        // 接收写入之后的主表数据
        TCirculate newExistsCirculate = new TCirculate();
        // 接收传阅人
        List<TCirculateUserDTO> cyrs = new ArrayList<>();

        //判断是否已经有数据
        List<TCirculateUser> existsCyrs = new ArrayList<>();
        //如果业务id不为空说明这是别的地方转过来的(复制文件数据，转公文传阅)
        tCirculate.setFromDataId(entityDTO.getFormDataId());
        if (null == entityDTO.getFormDataId()) {
            if (entityDTO.getTitle().contains("<转发>")){
                // A 转发公文传阅
                TCirculate oldTCirculate = tCirculateMapper.selectOneOnlyAddTenantId(new LambdaQueryWrapper<TCirculate>()
                        .eq(TCirculate::getId, entityDTO.getOldId()));
                if (oldTCirculate == null) {
                    throw new BusinessException("未找到原传阅");
                }
                // 同步原来传阅的正文、附件、流程、打印等状态信息
                tCirculate.setCirculateStatus(oldTCirculate.getCirculateStatus());
                tCirculate.setIsAttach(oldTCirculate.getIsAttach());
                tCirculate.setIsDoc(oldTCirculate.getIsDoc());
                tCirculate.setIsProcess(oldTCirculate.getIsProcess());
                tCirculate.setIsPrint(oldTCirculate.getIsPrint());
                // 同步原处理单id到转发后的传阅
                tCirculate.setFromDataId(oldTCirculate.getFromDataId());
                save(tCirculate);
                newExistsCirculate = tCirculate;
            }else {
                // B 设置为转公文传阅
                tCirculate.setCirculateStatus(0);
                tCirculate.setIsAttach(1);
                tCirculate.setIsDoc(1);
                tCirculate.setIsProcess(0);
                tCirculate.setIsPrint(entityDTO.getIsPrint());
                save(tCirculate);
                newExistsCirculate = tCirculate;
                //cyrs = entityDTO.getTCirculateUserDTOS();
            }
        } else {
            // 设置业务数据来源id  复制文件数据，转公文传阅
            Map<String,Object> data = tCirculateMapper.selectCpjById(entityDTO.getFormDataId());
            if(null != data){
                // 呈批件转 过来数据 没有 是否打印字段值
                String isPrint =Optional.ofNullable((String)data.get("is_print")).orElse("1");
                tCirculate.setIsPrint(isPrint);
                String durationClassification =Optional.ofNullable((String)data.get("duration_classification")).orElse(null);
                // tCirculate.setSecret(durationClassification);
                tCirculate.setSecretLevel(durationClassification);
                String durationClassificationName =Optional.ofNullable((String)data.get("duration_classification_name")).orElse(null);
                // tCirculate.setSecretLevel(durationClassificationName);
                tCirculate.setSecretLevelName(durationClassificationName);
            }
            tCirculate.setCirculateStatus(1);
            // tCirculate.setIsAttach(1);
            // tCirculate.setIsDoc(1);
            // tCirculate.setIsProcess(0);

            // ## 转传阅 人+业务数据 唯一
            LambdaQueryWrapper<TCirculate> cyWrapper = new LambdaQueryWrapper<>();
            cyWrapper.eq(TCirculate::getFromDataId, entityDTO.getFormDataId())
                    .eq(TCirculate::getCreateBy, SecurityUtils.getCurrentUserId());
            TCirculate existsCy = tCirculateMapper.selectOneOnlyAddTenantId(cyWrapper);
            if (null == existsCy) {
                save(tCirculate);
                Integer isAttach = tCirculate.getIsAttach();
                Integer isDoc = tCirculate.getIsDoc();
                Integer isProcess = tCirculate.getIsProcess();

                //正文
                if(null != isDoc  && 1 ==  isDoc.intValue() ){
                    // List<CscpDocumentFile> cscpDocumentFiles = cscpDocumentFileMapper.selectList(
                    //         new LambdaQueryWrapper<CscpDocumentFile>().eq(CscpDocumentFile::getFormDataId, entityDTO.getFormDataId()));

                    List<CscpDocumentFile> cscpDocumentFiles = cscpDocumentFileService.getFormFiles(entityDTO.getFormDataId());

                    cscpDocumentFiles.stream().forEach(i -> {
                        i.setId(null);
                        i.setFormDataId(tCirculate.getId());
                        cscpDocumentFileMapper.insert(i);
                    });
                }
                if(null != isAttach  && 1 ==  isAttach.intValue() ){
                    List<String> transferAnnexIdList = entityDTO.getTransferAnnexIdList();
                    //附件
                    // List<CscpEnclosureFile> cscpEnclosureFiles = cscpFormFileRepository.selectList(
                    //         new LambdaQueryWrapper<CscpEnclosureFile>().eq(CscpEnclosureFile::getFormDataId, entityDTO.getFormDataId()));

                    List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileService.getFormFiles(entityDTO.getFormDataId());
                    cscpEnclosureFiles.stream().forEach(i -> {
                        // 注释转传阅 带指定附件 可能会放开
                        // if(transferAnnexIdList != null && transferAnnexIdList.contains(String.valueOf(i.getId()))){
                            i.setId(null);
                            i.setFormDataId(tCirculate.getId());
                            cscpFormFileRepository.insert(i);
                        // }
                    });
                }
                newExistsCirculate = tCirculate;
            } else {
                // ### 多次 转传阅  正文附件 删除记录再添加
                 reSave(tCirculate,existsCy,entityDTO);
                // 已存在传阅记录 只插入该单位传阅人记录
                newExistsCirculate = existsCy;
                // 过滤掉已经发送人员的传阅记录
                LambdaQueryWrapper<TCirculateUser> cyrWrapper = new LambdaQueryWrapper<>();
                cyrWrapper.eq(TCirculateUser::getCirculateId, existsCy.getId());
                /**
                 * 签收状态 0:未阅 1:已阅
                 */
                // cyrWrapper.eq(TCirculateUser::getSignInStatus, 0);
                existsCyrs = tCirculateUserMapper.selectListOnlyAddTenantId(cyrWrapper);
            }
        }

        Map<Long, Object> filterMap = new HashMap<>();
        existsCyrs.stream().forEach(x -> {
            filterMap.put(x.getUserId(), null);
        });
        List<TCirculateUserDTO> tCirculateUserDTOS = entityDTO.getTCirculateUserDTOS();

        //需要查询单位的人员信息
        List<Long> companyList = new ArrayList<>();
        for (TCirculateUserDTO cyr : tCirculateUserDTOS) {
            if (null != cyr.getUserId() && !filterMap.containsKey(cyr.getUserId())) {
                // 不存在
                cyrs.add(cyr);
            } else if (ObjectUtil.isEmpty(cyr.getUserId()) && ObjectUtil.isNotEmpty(cyr.getUnitId())) {
                companyList.add(cyr.getUnitId());
            }
        }
        // TODO: 2023/3/28 wubin 外单位传阅，获取所有人信息插入子表
        if (null != companyList && ObjectUtil.isNotEmpty(companyList)) {
            CscpDistributeBaseUserDTO cscpDistributeBaseUserDTO = new CscpDistributeBaseUserDTO();
            cscpDistributeBaseUserDTO.setCompanyIdList(companyList);
            //通过用户名id集合和单位id集合，获取用户名分发信息
            List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserDTOList = cscpUserRepository.selectDistributeBaseUserInfo(cscpDistributeBaseUserDTO);
            for (CscpDistributeBaseUserDTO cdbus : cscpDistributeBaseUserDTOList) {
                if (!filterMap.containsKey(cdbus.getUserId())) {
                    TCirculateUserDTO tcud = new TCirculateUserDTO();
                    tcud.setUserId(cdbus.getUserId());
                    tcud.setUserName(cdbus.getUserName());
                    tcud.setBranchId(cdbus.getDepartmentId());
                    tcud.setBranchName(cdbus.getDepartmentName());
                    tcud.setUnitId(cdbus.getCompanyId());
                    tcud.setUnitName(cdbus.getCompanyName());
                    cyrs.add(tcud);
                }
            }
        }

        // 传阅人记录
        if (null != cyrs && cyrs.size() > 0) {
            List<TCirculateUser> copyCUsers = ListCopyUtil.copy(cyrs, TCirculateUser.class);
            //填充公文传阅id
            for (TCirculateUser copyCUser : copyCUsers) {
                copyCUser.setCirculateId(newExistsCirculate.getId());
            }
            itCirculateUserService.saveBatch(copyCUsers);
            insertSmsNotifiRecords(copyCUsers,newExistsCirculate);

        }

        // 转公文传阅记录维护
        if(entityDTO.getFormDataId() != null && CollectionUtils.isNotEmpty(entityDTO.getTCirculateUserDTOS())){
            BizApprovalForwardRecord bizApprovalForwardRecord = new BizApprovalForwardRecord();
            bizApprovalForwardRecord.setFormDataId(entityDTO.getFormDataId());
            bizApprovalForwardRecord.setForwardType(ApprovalForwardTypeEnums.CIRCULATE.getType());
            bizApprovalForwardRecord.setForwardUserName(SecurityUtils.getCurrentRealName());
            List<String> receiveUserList = entityDTO.getTCirculateUserDTOS()
                    .stream().map(TCirculateUserDTO::getUserName)
                    .distinct().collect(Collectors.toList());
            bizApprovalForwardRecord.setReceiveUserName(String.join(",", receiveUserList));
            bizApprovalForwardRecord.setReceiveUserTotal(receiveUserList.size());
            StringJoiner joiner = new StringJoiner(",");
            if (Objects.equals(entityDTO.getIsProcess(), 1)) {
                joiner.add("处理单");
            }
            if (Objects.equals(entityDTO.getIsDoc(), 1)) {
                joiner.add("正文");
            }
            if (Objects.equals(entityDTO.getIsAttach(), 1)) {
                joiner.add("附件");
            }
            bizApprovalForwardRecord.setForwardContent(joiner.toString());
            approvalForwardRecordMapper.insert(bizApprovalForwardRecord);
        }

        return BeanConvertUtils.copyProperties(tCirculate, TCirculateDTO.class);
    }

    /**
     *  @param tCirculate    需要更新的数据
     * @param existCirculate  更新前数据
     * @param entityDTO
     */
    private void reSave(TCirculate tCirculate , TCirculate existCirculate , TCirculateDTO entityDTO) {
        tCirculate.setId(existCirculate.getId());
        Integer isAttach = tCirculate.getIsAttach();
        Integer isDoc = tCirculate.getIsDoc();
        Long fromDataId = tCirculate.getFromDataId();
        if( null == fromDataId){
            return;
        }
        // 1 删除之前的正文
        List<Long> oldDoc = cscpDocumentFileMapper.selectList(
                new LambdaQueryWrapper<CscpDocumentFile>().eq(CscpDocumentFile::getFormDataId,
                        existCirculate.getId())).stream().map(i -> i.getId()).collect(Collectors.toList());
        if(!oldDoc.isEmpty()){
            cscpDocumentFileMapper.deleteBatchIds(oldDoc);
        }
        if(null != isDoc  && 1 ==  isDoc.intValue() ){
            List<CscpDocumentFile> cscpDocumentFiles = cscpDocumentFileService.getFormFiles(entityDTO.getFormDataId());
            cscpDocumentFiles.stream().forEach(i -> {
                i.setId(null);
                i.setFormDataId(tCirculate.getId());
                cscpDocumentFileMapper.insert(i);
            });
        }

        // 2 删除之前的附件
        List<Long> oldAnne = cscpFormFileRepository.selectList(
                new LambdaQueryWrapper<CscpEnclosureFile>().eq(CscpEnclosureFile::getFormDataId, existCirculate.getId()))
                .stream().map(i -> i.getId()).collect(Collectors.toList());
        if(!oldAnne.isEmpty()){
            cscpFormFileRepository.deleteBatchIds(oldAnne);
        }

        if(null != isAttach  && 1 ==  isAttach.intValue() ){
            List<String> transferAnnexIdList = entityDTO.getTransferAnnexIdList();
            List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileService.getFormFiles(entityDTO.getFormDataId());
            cscpEnclosureFiles.stream().forEach(i -> {
                // if(transferAnnexIdList != null && transferAnnexIdList.contains(String.valueOf(i.getId()))){
                    i.setId(null);
                    i.setFormDataId(tCirculate.getId());
                    cscpFormFileRepository.insert(i);
                // }
            });
        }
        // 3 更新 传阅表数据   // 终止的恢复传阅
        if(  null != existCirculate.getTermination() &&   1 == existCirculate.getTermination().intValue() ){
            tCirculate.setTermination(0);
            tCirculate.setTerminationTime(LocalDateTime.now());
        }
        saveOrUpdate(tCirculate);
    }

    /**
     * 插入短信通知记录
     * @param copyCUsers
     * @param newExistsCirculate
     */
    private void insertSmsNotifiRecords(List<TCirculateUser> copyCUsers , TCirculate newExistsCirculate) {
        SwSmsNotificationRecords swSmsNotificationRecords = new SwSmsNotificationRecords();
        List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList=new ArrayList<>();
        TCirculateUser tCirculateUser = copyCUsers.get(0);
        Long circulateId = tCirculateUser.getCirculateId();
        //设置短信内容
        String meetingType = newExistsCirculate.getMeetingType();
        String smsContent = "";

        // 会议类型
        if(StrUtil.equals(meetingType,"1")){
            smsContent = sysConfigService.getSysConfigValueByCode(SwSmsSendUtil.QIANPI_SMS_CONTENT_CIRCULATE_MEETING);
            if(StrUtil.isEmpty(smsContent)){  //  （联系人:%s%s,联系方式:%s）（湖南省委办公厅王崇跃,联系方式:13507451727）
                smsContent="【政务短信】您有信息传阅待签收（会议时间：%s，地点：%s），请登陆内网协同办公系统进行查收（联系人:%s%s,联系方式:%s）。";
            }
            // String formatSms = String.format(smsContent , newExistsCirculate.getMeetingTime(),
            //         newExistsCirculate.getMeetingPlace());

            // 获取当前环节下发人手机号
            CscpUser cscpUser = cscpUserService.getById(newExistsCirculate.getCreateBy());
            CscpOrg cscpOrg = cscpOrgService.getTopCompanyIdByUserId(newExistsCirculate.getCreateBy());

            String formatSms =  String.format(smsContent ,newExistsCirculate.getMeetingTime(),
                    newExistsCirculate.getMeetingPlace()
                    , cscpOrg != null ? cscpOrg.getOrgName() : null, newExistsCirculate.getCreateName(), cscpUser != null ? cscpUser.getMobile() : null);
            swSmsNotificationRecords.setUrgingContent(formatSms);
        }

        // 非会议类型
        if(!StrUtil.equals(meetingType,"1")){
            smsContent = sysConfigService.getSysConfigValueByCode(SwSmsSendUtil.QIANPI_SMS_CONTENT_CIRCULATE);
            if(StrUtil.isNotEmpty(smsContent)){
                CscpUserDetail userDetail = SecurityUtils.getCurrentUser().get();
                CscpOrg cscpOrg = cscpOrgService.getTopCompanyIdByUserId(userDetail.getId());
                String formatSms = String.format(smsContent , cscpOrg != null ? cscpOrg.getOrgName() : null ,
                        userDetail.getRealName() ,
                        userDetail != null ? userDetail.getMobile() : null);
                swSmsNotificationRecords.setUrgingContent(formatSms);
            }
        }

        // 您有一份信息传阅待签收，,请登录内网OA平台进行处理。(联系人:湖南省委办公厅陈露,联系方式:15857173324)
        //您有信息传阅待签收（会议时间：2024年2月12日下午3点，地点：机要局301），请登陆内网协同办公厅系统进行查收
        // 您有一份文件待签批,请登录内网OA平台进行处理。（联系人:%s%s,联系方式:%s

        for (TCirculateUser copyCUser : copyCUsers) {
            Long userId = copyCUser.getUserId();
            // 增加传阅短信通知列表
            CscpUser cscpUser = cscpUserService.getById(userId);
            LambdaQueryWrapper<CscpUserOrg> lambdaQueryWrapper=new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpUserOrg::getUserId,cscpUser.getId());
            List<CscpUserOrg> cscpUserOrgList = cscpUserOrgService.selectListNoAdd(lambdaQueryWrapper);
            CscpUserOrg cscpUserOrg = cscpUserOrgList.get(0);
            SwSmsNotificationRecords.NotifyPersonnel notifyPersonnel=new SwSmsNotificationRecords.NotifyPersonnel();
            notifyPersonnel.setUserID(cscpUser.getId());
            notifyPersonnel.setUserName(cscpUser.getRealName());
            notifyPersonnel.setOrgID(cscpUserOrg.getOrgId());
            notifyPersonnel.setOrgName(cscpUserOrg.getOrgName());
            notifyPersonnel.setPhone(cscpUser.getMobile());
            notifyPersonnelList.add(notifyPersonnel);
        }
        String noticePeopleJsonArrayString= JSON.toJSONString(notifyPersonnelList);

        swSmsNotificationRecords.setUrgingTime(new Date());
        swSmsNotificationRecords.setNoticePeople(noticePeopleJsonArrayString);
        swSmsNotificationRecords.setDocumentId(circulateId);
        swSmsNotificationRecords.setNotifiCount(0);
        swSmsNotificationRecords.setType(2);
        smsNotificationRecordsMapper.insert(swSmsNotificationRecords);
    }

    /**
     * 新增
     *
     * @param
     * @return
     */
    @Override
    @Transactional
    public void receiveCreate(TCirculateDTO entityDTO) {
        LambdaQueryWrapper<TCirculate> cyWrapper = new LambdaQueryWrapper<>();
        cyWrapper.eq(TCirculate::getFromDataId, entityDTO.getFromDataId());
        TCirculate existsCy = tCirculateMapper.selectOneOnlyAddTenantId(cyWrapper);
        Long id = 0L;
        if (null == existsCy) {
            //新增公文传阅
            TCirculate tCirculate = BeanConvertUtils.copyProperties(entityDTO, TCirculate.class);
            //获取业务所在单位
            String companyName = SecurityUtils.getCurrentCscpUserDetail().getCompanyName();
            tCirculate.setCompanyName(companyName);
            tCirculate.setCirculateStatus(1);
            tCirculate.setFromDataId(entityDTO.getFromDataId());
            save(tCirculate);
            id = tCirculate.getId();
        } else {
            id = existsCy.getId();
            existsCy.setIsAttach(entityDTO.getIsAttach());
            existsCy.setIsProcess(entityDTO.getIsProcess());
            updateById(existsCy);
        }

        // 传阅人记录
        if (null != entityDTO.getTCirculateUserDTOS() && entityDTO.getTCirculateUserDTOS().size() > 0) {
            List<TCirculateUser> copyCUsers = ListCopyUtil.copy(entityDTO.getTCirculateUserDTOS(), TCirculateUser.class);
            //填充公文传阅id
            for (TCirculateUser copyCUser : copyCUsers) {
                copyCUser.setSignInStatus(0);
                copyCUser.setCirculateId(id);
            }
            itCirculateUserService.saveBatch(copyCUsers);
        }
        cscpProcBaseService.updateIsCircularizeByProcInstId(entityDTO.getProcessInstanceId(), entityDTO.getReviewComments());
    }

    @Override
    public TCirculateDTO getReceiveDetails(Long formDataId) {
        LambdaQueryWrapper<TCirculate> lambdaCirculateQueryWrapper = new LambdaQueryWrapper<>();
        lambdaCirculateQueryWrapper.eq(TCirculate::getFromDataId, formDataId);
        TCirculate tCirculate = tCirculateMapper.selectOneNoAdd(lambdaCirculateQueryWrapper);
        if (tCirculate != null) {
            TCirculateDTO dto = BeanConvertUtils.copyProperties(tCirculate, TCirculateDTO.class);

            LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, tCirculate.getId());
            List<TCirculateUser> list = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper);
            List<TCirculateUserDTO> tCirculateUserDTO = ListCopyUtil.copy(list, TCirculateUserDTO.class);
            dto.setTCirculateUserDTOS(tCirculateUserDTO);

            CscpProcBase cscpProcBase = cscpProcBaseService.getProcBaseByProcInstId(tCirculate.getProcessInstanceId());
            dto.setReviewComments(cscpProcBase.getReviewComments());

            return dto;
        }
        return null;
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TCirculateDTO entity) {
        //转换
        TCirculate tCirculate = BeanConvertUtils.copyProperties(entity, TCirculate.class);

        //将传阅和中间表的对应关系删除
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, entity.getId());
        tCirculateUserMapper.delete(lambdaQueryWrapper);

        //批量新增传阅中间表数据
        boolean icu = itCirculateUserService.saveBatch(entity.getTCirculateUserDTOS().stream().map(i -> {
            TCirculateUser tCirculateUser = new TCirculateUser();
            tCirculateUser.setUserId(i.getUserId());
            tCirculateUser.setCirculateId(tCirculate.getId());
            tCirculateUser.setUserName(i.getUserName());
            return tCirculateUser;
        }).distinct().collect(Collectors.toList()));

        //修改传阅基础信息
        int ic = tCirculateMapper.updateById(tCirculate);

        return icu && ic > 0 ? 1 : 0;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        //删除公文传阅
        int circulateId = tCirculateMapper.deleteById(id);

        //删除对应公文传阅的中间表数据
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, id);
        int cuCount = tCirculateUserMapper.delete(lambdaQueryWrapper);
        if (cuCount==0){
            LambdaQueryWrapper<TCirculateUser> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TCirculateUser::getCirculateId, id);
            List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectListNoAdd(queryWrapper);
            if (CollectionUtils.isNotEmpty(tCirculateUserList)){
                throw new BusinessException("删除失败，请刷新页面后再试");
            }
        }

        // 删除个人关注的信息
        bizService.deleteFocusByBizId(id);
        return circulateId > 0 ? 1 : 0;
    }


    /**
     * 询公文传阅详情
     *
     * @param id
     * @return
     */
    @Override
    public ResTcirculate queryDetails(Long id) {
        LambdaQueryWrapper<TCirculate> tcl = new LambdaQueryWrapper<>();
        tcl.eq(TCirculate::getId, id);
        TCirculate tCirculate = tCirculateMapper.selectOneNoAdd(tcl);
        ResTcirculate resTcirculate = BeanConvertUtils.copyProperties(tCirculate, ResTcirculate.class);
        if (tCirculate.getSecret()!=null){
            resTcirculate.setSecret(tCirculate.getSecret());
        }else {
            resTcirculate.setSecret(tCirculate.getSecretLevel());
            resTcirculate.setSecretLevel(tCirculate.getSecretLevelName());
        }

        // 1 转传阅还有处理单标题给前端
        if(null != tCirculate.getFromDataId()){
            // Map<String,Object> data = cscpProcBaseService.getProcBaseByFormDataId(tCirculate.getFromDataId().toString());
            CscpProcBase base = cscpProcBaseService.getProcBaseByFormDataId(tCirculate.getFromDataId().toString());
            if(null != base){
                // String titleCld = (String)data.get("titleCld");
                // Long process_instance_id = (Long)data.get("process_instance_id");
                String titleCld = base.getTitle();
                resTcirculate.setTitleCld(titleCld);
                // 2 获取流程参数
                TaskVO taskVO = cscpProcBaseRepository.getProcessInfoByInstanceId(base.getProcInstId());
                resTcirculate.setTaskVO(taskVO);
            }
        }

        //正文
        LambdaQueryWrapper<CscpDocumentFile> cdfl = new LambdaQueryWrapper<>();
        cdfl.eq(null != tCirculate.getFromDataId(), CscpDocumentFile::getFormDataId, tCirculate.getFromDataId());
        cdfl.eq(null == tCirculate.getFromDataId(), CscpDocumentFile::getFormDataId, tCirculate.getId());
        List<CscpDocumentFile> cscpDocumentFiles = cscpDocumentFileMapper.selectListNoAdd(cdfl);
        resTcirculate.setDocumentFiles(cscpDocumentFiles);

        //附件
        LambdaQueryWrapper<CscpEnclosureFile> cefl = new LambdaQueryWrapper();
        if (tCirculate.getCirculateStatus() == 0) {
            cefl.eq(CscpEnclosureFile::getFormDataId, tCirculate.getId());
        } else {
            cefl.and(qr -> qr.eq(CscpEnclosureFile::getFormDataId, tCirculate.getFromDataId()).or()
                    .eq(CscpEnclosureFile::getFormDataId, tCirculate.getId()));
        }
        List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(cefl);
        resTcirculate.setEnclosureFiles(cscpEnclosureFiles);

        // 阅读人 新增去重 2023-03-25 added lizuolang
        String userNameList = tCirculateUserMapper.selectListNoAdd(new LambdaQueryWrapper<TCirculateUser>()
                        .eq(TCirculateUser::getCirculateId, id))
                .stream().map(i -> i.getUserName()).distinct().collect(Collectors.joining(","));

        resTcirculate.setUserName(userNameList);

        // 设置密级
        if (StrUtil.isNotBlank(resTcirculate.getSecretLevel())) {
            // 获取密级期限数据字典
            Map<String, String> tSysDictMap = this.getTSysDictMap();
            if (tSysDictMap.get(resTcirculate.getSecret())!=null){
                resTcirculate.setSecretLevel(tSysDictMap.get(resTcirculate.getSecret()));
            }
        }

        // 需要获取 自己的回复内容和附件
        long currentUserId = SecurityUtils.getCurrentUserId();
        TCirculateUser user  = itCirculateUserService.selectMyRecord(id,currentUserId);
        resTcirculate.setDocumentReply(user.getDocumentReply());
        resTcirculate.setUserReplyInfo(user);
        List<CscpEnclosureFile> replyFileList = enclosureFileService.getFormFiles(user.getId());
        resTcirculate.setReplyFileList(replyFileList);


        TUserFocusDTO entityDTO = userFocusService.getFocusInfoByBusyId(resTcirculate.getId());
        if(null != entityDTO){
            resTcirculate.setFocus("1");
        }

        return resTcirculate;
    }

    /**
     * 查询签收详情
     *
     * @param querysignForDTO
     * @param basePageForm
     * @return
     */
    @Override
    public DocumentDetailsDTO querysignFor(QuerysignForDTO querysignForDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TCirculateUser::getCirculateId, querysignForDTO.getCirculateId());
        // lambdaQueryWrapper.eq(TCirculateUser::getCreateBy, SecurityUtils.getCurrentUserId());
        //排序
        lambdaQueryWrapper.orderByDesc(TCirculateUser::getUpdateTime).orderByDesc(TCirculateUser::getId);
        //条件查询 姓名  传阅状态
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(querysignForDTO.getUserName()), TCirculateUser::getUserName, querysignForDTO.getUserName());
        // 3 查未确认
        lambdaQueryWrapper.ne(ObjectUtil.equal(querysignForDTO.getSignInStatus(), 3), TCirculateUser::getSignInStatus, 1);
        // 4 查全部
        if (ObjectUtil.notEqual(querysignForDTO.getSignInStatus(), 3)) {
            if (ObjectUtil.notEqual(querysignForDTO.getSignInStatus(), 4)) {
                if (ObjectUtil.equal(querysignForDTO.getSignInStatus(), 2)) {
                    // 已打开包含打开和已确认
                    lambdaQueryWrapper.in(TCirculateUser::getSignInStatus, Arrays.asList(1, 2));
                } else {
                    lambdaQueryWrapper.eq(TCirculateUser::getSignInStatus, querysignForDTO.getSignInStatus());
                }
            }
        }

        IPage<TCirculateUser> pageData =
                tCirculateUserMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                        lambdaQueryWrapper);

        //获取终止传阅时间
        TCirculate tCirculate = tCirculateMapper.selectOneNoAdd(new LambdaQueryWrapper<TCirculate>().select(TCirculate::getTerminationTime).eq(TCirculate::getId, querysignForDTO.getCirculateId()));

        //签收详情
        List<ResSignFor> collect = pageData.getRecords().stream().map(i -> {
            ResSignFor resSignFor = BeanConvertUtils.copyProperties(i, ResSignFor.class);
            if (!Objects.isNull(tCirculate)) {
                resSignFor.setTerminationTime(tCirculate.getTerminationTime());
            }
            if (StringUtils.isEmpty(i.getBranchName())){
                LambdaQueryWrapper<CscpUserOrg> orgWrapper=new LambdaQueryWrapper<>();
                orgWrapper.eq(CscpUserOrg::getUserId,i.getUserId());
                List<CscpUserOrg> cscpUserOrgList = cscpUserOrgService.selectListNoAdd(orgWrapper);
                CscpUserOrg cscpUserOrg = cscpUserOrgList.get(0);
                resSignFor.setBranchId(cscpUserOrg.getOrgId());
                resSignFor.setBranchName(cscpUserOrg.getOrgName());
            }
            Long id = i.getId();
            List<CscpEnclosureFile> fileList = enclosureFileService.getFormFiles(id);
            resSignFor.setFileList(fileList);
            return resSignFor;
        }).collect(Collectors.toList());


        // 查总数
        LambdaQueryWrapper<TCirculateUser> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper2.eq(TCirculateUser::getCirculateId, querysignForDTO.getCirculateId());
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(lambdaQueryWrapper2);
        int total = tCirculateUsers.size();
        // 2 查询未打开
        // 3 查询未确认
        //List<TCirculateUser> noClicks  =
        //        tCirculateUsers.stream().filter(i -> i.getSignInStatus() == 0).collect(Collectors.toList());
        //List<TCirculateUser>  noSigns = tCirculateUsers.stream().filter(i -> i.getSignInStatus() != 2).collect(Collectors.toList());

        DocumentDetailsDTO documentDetails = new DocumentDetailsDTO();
        documentDetails.setNoSign(0);
        documentDetails.setSign(0);
        documentDetails.setClick(0);
        documentDetails.setNoClick(0);
        documentDetails.setCollect(new PageResult(collect, pageData.getTotal(), pageData.getTotal()));
        documentDetails.setCirculatetotal(total);
        for (TCirculateUser cUser : tCirculateUsers) {
            if (ObjectUtil.equal(cUser.getSignInStatus(), 2) || ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                // 统计已打开人数
                documentDetails.setClick(documentDetails.getClick() + 1);
            }
            if (ObjectUtil.equal(cUser.getSignInStatus(), 0)) {
                // 统计未打开人数
                documentDetails.setNoClick(documentDetails.getNoClick() + 1);
            }
            if (ObjectUtil.equal(cUser.getSignInStatus(), 1)) {
                // 统计已确认人数
                documentDetails.setSign(documentDetails.getSign() + 1);
            }
        }

        // 统计未确认人数
        documentDetails.setNoSign(total - documentDetails.getSign());

        // documentDetails.setNoClickList(noClicks);
        // documentDetails.setNoSignList(noSigns);
        //返回
        return documentDetails;
    }

    /**
     * 撤销
     *
     * @param revokeIdList
     * @param id
     * @return
     */
    @Override
    public Integer revoke(List<Long> revokeIdList, Long id) {
        LambdaQueryWrapper<TCirculateUser> tcul = new LambdaQueryWrapper<>();
        tcul.in(TCirculateUser::getUserId, revokeIdList);
        tcul.eq(TCirculateUser::getCirculateId, id);
        return tCirculateUserMapper.delete(tcul);
    }

    /**
     * 补发
     *
     * @param
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String reissue(List<TCirculateUserDTO> tCirculateUserDTOList, Long id) {
        String userNames = "";
        // ### 检查分发用户是否重复,过滤掉重复的用户
        List<Long> userIds = tCirculateUserDTOList.stream().map(i -> i.getUserId()).collect(Collectors.toList());
        List<TCirculateUser> userExist = itCirculateUserService.selectListNoAdd(new LambdaQueryWrapper<TCirculateUser>()
                .eq(TCirculateUser::getCirculateId , id).in(TCirculateUser::getUserId , userIds)
        );
        List<Long> userExistIds = userExist.stream().map(i -> i.getUserId()).collect(Collectors.toList());
        // 过滤
        List<TCirculateUserDTO>  newUsers = tCirculateUserDTOList.stream().filter(item -> {
            return !userExistIds.contains(item.getUserId());
        }).collect(Collectors.toList());

        // 重复人员需给前端
        if(!userExist.isEmpty()){
            userNames = userExist.stream().map(i -> i.getUserName()).collect(Collectors.joining(","));
        }

        //获取现在已有传阅用户id信息排除重复
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(new LambdaQueryWrapper<TCirculateUser>()
                .select(TCirculateUser::getUserId)
                .eq(TCirculateUser::getCirculateId, id)
                // .eq(TCirculateUser::getSignInStatus, CirculateConstant.CIRCULATE_NOT_READ)
                )
                ;
        Map<Long, Object> filterMap = new HashMap<>();
        tCirculateUsers.stream().forEach(x -> {
            filterMap.put(x.getUserId(), null);
        });

        // 接收传阅人
        List<TCirculateUserDTO> cyrs = new ArrayList<>();
        //需要查询单位的人员信息
        List<Long> companyList = new ArrayList<>();
        for (TCirculateUserDTO cyr : newUsers) {
            if (null != cyr.getUserId() && !filterMap.containsKey(cyr.getUserId())) {
                // 不存在
                cyrs.add(cyr);
            } else if (ObjectUtil.isEmpty(cyr.getUserId()) && ObjectUtil.isNotEmpty(cyr.getUnitId())) {
                companyList.add(cyr.getUnitId());
            } else {
                // 不做处理
                continue;
            }
        }
        // TODO: 2023/3/28 wubin 外单位传阅，获取所有人信息插入子表
        if (null != companyList && ObjectUtil.isNotEmpty(companyList)) {
            CscpDistributeBaseUserDTO cscpDistributeBaseUserDTO = new CscpDistributeBaseUserDTO();
            cscpDistributeBaseUserDTO.setCompanyIdList(companyList);
            //通过用户名id集合和单位id集合，获取用户名分发信息
            List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserDTOList = cscpUserRepository.selectDistributeBaseUserInfo(cscpDistributeBaseUserDTO);
            for (CscpDistributeBaseUserDTO cdbus : cscpDistributeBaseUserDTOList) {
                if (!filterMap.containsKey(cdbus.getUserId())) {
                    TCirculateUserDTO tcud = new TCirculateUserDTO();
                    tcud.setUserId(cdbus.getUserId());
                    tcud.setUserName(cdbus.getUserName());
                    tcud.setBranchId(cdbus.getDepartmentId());
                    tcud.setBranchName(cdbus.getDepartmentName());
                    tcud.setUnitId(cdbus.getCompanyId());
                    tcud.setUnitName(cdbus.getCompanyName());
                    cyrs.add(tcud);
                }
            }
        }
        // 传阅人记录
        if (null != cyrs && cyrs.size() > 0) {
            List<TCirculateUser> copyCUsers = ListCopyUtil.copy(cyrs, TCirculateUser.class);

            //填充公文传阅id
            for (TCirculateUser copyCUser : copyCUsers) {
                copyCUser.setCirculateId(id);
                copyCUser.setSignInStatus(0);
            }

            //批量保存
            itCirculateUserService.saveBatch(copyCUsers);
            TCirculate newExistsCirculate = tCirculateMapper.selectOneOnlyAddTenantId(new LambdaQueryWrapper<TCirculate>()
                    .eq(TCirculate::getId, id));
            insertSmsNotifiRecords(copyCUsers , newExistsCirculate);
        }

//        //放回需要补发的公文
//        List<TCirculateUser> circulateUserList = userId.stream().map(i -> {
//            //查询这条公文是否有待阅,如果有直接返回空
//            List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(new LambdaQueryWrapper<TCirculateUser>()
//                    .select(TCirculateUser::getId)
//                    .eq(TCirculateUser::getUserId, i.getUserId())
//                    .eq(TCirculateUser::getCirculateId, id)
//                    .eq(TCirculateUser::getSignInStatus, CirculateConstant.CIRCULATE_NOT_READ));
//            if (tCirculateUsers.isEmpty()) {
//                TCirculateUser tCirculateUser = BeanConvertUtils.copyProperties(i, TCirculateUser.class);
//                tCirculateUser.setSignInStatus(0);
//                tCirculateUser.setCirculateId(id);
//                return tCirculateUser;
//            }
//            return null;
//        }).collect(Collectors.toList());
//        circulateUserList.removeAll(Collections.singleton(null));
//
//        //补发
//        if (!circulateUserList.isEmpty()) {
//            itCirculateUserService.saveBatch(circulateUserList);
//        }
        return userNames;
    }

    /**
     * 终止传阅
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Integer termination(Long id) {
        TCirculate tCirculate = new TCirculate();
        tCirculate.setId(id);
        //终止状态
        tCirculate.setTermination(1);
        //终止传阅时间
        tCirculate.setTerminationTime(LocalDateTime.now());


        // 删除 传阅短信通知记录
        smsNotificationRecordsMapper.updateStatusByBizId(id,1);

        // 更新状态
        bizService.updateFocusStatusByBizId(id,1);
        return tCirculateMapper.updateById(tCirculate);
    }

    /**
     * 获取公文传阅总人数, 已阅人数, 未阅人数
     *
     * @param querysignForDTO
     * @return
     */
    @Override
    public DocumentDetailsDTO queryPopulationDetails(QuerysignForDTO querysignForDTO) {
        LambdaQueryWrapper<TCirculateUser> ne = new LambdaQueryWrapper<TCirculateUser>()
                .select(TCirculateUser::getId, TCirculateUser::getSignInStatus)
                .eq(TCirculateUser::getCirculateId, querysignForDTO.getId())
                .eq(TCirculateUser::getCreateBy, SecurityUtils.getCurrentUserId());
        //查询本单位
        //.eq(0 == querysignForDTO.getQueryType(), TCirculateUser::getUnitId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId())
        //查询外单位
        //.ne(1 == querysignForDTO.getQueryType(), TCirculateUser::getUnitId, SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        List<TCirculateUser> tCirculateUsers = tCirculateUserMapper.selectListNoAdd(ne);


        List<TCirculateUser> collect = tCirculateUsers.stream().filter(i -> i.getSignInStatus() != 0).collect(Collectors.toList());

        DocumentDetailsDTO documentDetails = new DocumentDetailsDTO();
        //未阅人数
        documentDetails.setForReadingtotal(tCirculateUsers.size() - collect.size());
        //已阅人数
        documentDetails.setReadtotal(collect.size());
        //总条数
        documentDetails.setCirculatetotal(tCirculateUsers.size());
        documentDetails.setCollect(new PageResult(tCirculateUsers, tCirculateUsers.size(), 0));
        return documentDetails;
    }

    @Override
    public void resetData(Long id) {
        TCirculate tCirculate = new TCirculate();
        tCirculate.setId(id);
        //恢复状态
        tCirculate.setTermination(0);
        //传阅时间
        tCirculate.setTerminationTime(LocalDateTime.now());
        tCirculateMapper.updateById(tCirculate);
        smsNotificationRecordsMapper.updateStatusByBizId(id,0);

        // 更新状态
        bizService.updateFocusStatusByBizId(id,0);
    }

    @Override
    public PageResult<SwSmsNotificationRecordsBaseDTO> queryTCirculateForSmsPage(SwSmsNotificationRecordsBaseDTO entityDTO , BasePageForm basePageForm) {
        //设置条件
        Integer type = entityDTO.getType();
        LambdaQueryWrapper<SwSmsNotificationRecords> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCreateName()), SwSmsNotificationRecords::getCreateName,
                entityDTO.getCreateName())
                 .eq(type != null,SwSmsNotificationRecords::getType,type)
                .eq(entityDTO.getNotifiCount() != null, SwSmsNotificationRecords::getNotifiCount,
                        entityDTO.getNotifiCount())
                .ge(null != entityDTO.getQueryStartTime(), SwSmsNotificationRecords::getCreateTime,
                        entityDTO.getQueryStartTime())
                .le(null != entityDTO.getQueryEndTime(), SwSmsNotificationRecords::getCreateTime,
                        entityDTO.getQueryEndTime())
                .orderByDesc(SwSmsNotificationRecords::getCreateTime)
        ;

        if(type == null){ // 标识传阅数据 兼容老数据
            queryWrapper.eq(SwSmsNotificationRecords::getType,2);
        }

        IPage<SwSmsNotificationRecords> pageData = smsNotificationRecordsMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        IPage<SwSmsNotificationRecordsBaseDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity
                , SwSmsNotificationRecordsBaseDTO.class));
        // 需要过滤掉 已经打开过的传阅数据
        for (SwSmsNotificationRecordsBaseDTO record : data.getRecords()) {
            Long documentId = record.getDocumentId();
            Integer typeCy = record.getType();
            if(typeCy == 2){
                List<TCirculateUser> tCirculateUsers = itCirculateUserService.selectListByCyIdAndType(documentId , typeCy);
                List<Long> userids  = tCirculateUsers.stream().map(i -> i.getUserId()).collect(Collectors.toList());
                List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList = JSON.parseArray(record.getNoticePeople(), SwSmsNotificationRecords.NotifyPersonnel.class);
                notifyPersonnelList = notifyPersonnelList.stream().filter(i -> userids.contains(i.getUserID())).collect(Collectors.toList());

                for (SwSmsNotificationRecords.NotifyPersonnel notifyPersonnel : notifyPersonnelList) {
                    CscpUser user = cscpUserService.getById(notifyPersonnel.getUserID());
                    // 手机好没有问题设值
                    if(StringUtils.checkFormatMobiles(user.getBackupMobile())){
                        notifyPersonnel.setBackupMobile(user.getBackupMobile());
                    }
                }

                // record.setNotifyPersonnelList(notifyPersonnelList);
                record.setSendSmsUser(JSON.toJSONString(notifyPersonnelList));
            }
            if(typeCy == 3){
                List<Long> userids   = smsNotificationRecordsMapper.selectUserIdListNoticeId(documentId);
                List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList = JSON.parseArray(record.getNoticePeople(), SwSmsNotificationRecords.NotifyPersonnel.class);
                notifyPersonnelList = notifyPersonnelList.stream().filter(i -> userids.contains(i.getUserID())).collect(Collectors.toList());
                for (SwSmsNotificationRecords.NotifyPersonnel notifyPersonnel : notifyPersonnelList) {
                    CscpUser user = cscpUserService.getById(notifyPersonnel.getUserID());
                    // 手机好没有问题设值
                    if(StringUtils.checkFormatMobiles(user.getBackupMobile())){
                        notifyPersonnel.setBackupMobile(user.getBackupMobile());
                    }
                }
                record.setNotifyPersonnelList(notifyPersonnelList);
                record.setSendSmsUser(JSON.toJSONString(notifyPersonnelList));
            }


        }
        return new PageResult<SwSmsNotificationRecordsBaseDTO>(data.getRecords(),
                pageData.getTotal(), pageData.getCurrent());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCountPlus1(Long id) {
        SwSmsNotificationRecords swSmsNotificationRecords = smsNotificationRecordsMapper.selectById(id);
        swSmsNotificationRecords.setNotifiCount(swSmsNotificationRecords.getNotifiCount() + 1 );
        smsNotificationRecordsMapper.updateById(swSmsNotificationRecords);


        Long documentId = swSmsNotificationRecords.getDocumentId();
        Integer typeCy = swSmsNotificationRecords.getType();
        String noticePeople = swSmsNotificationRecords.getNoticePeople();
        if(typeCy == 2){
            List<TCirculateUser> tCirculateUsers = itCirculateUserService.selectListByCyIdAndType(documentId , typeCy);
            List<Long> userids = tCirculateUsers.stream().map(i -> i.getUserId()).collect(Collectors.toList());
            List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList = JSON.parseArray(noticePeople , SwSmsNotificationRecords.NotifyPersonnel.class);
            notifyPersonnelList = notifyPersonnelList.stream().filter(i -> userids.contains(i.getUserID())).collect(Collectors.toList());
            swSmsNotificationRecords.setNotifyPersonnelList(notifyPersonnelList);
            swSmsNotificationRecords.setNoticePeople(JSON.toJSONString(notifyPersonnelList));

        }
        if(typeCy == 3){
            List<Long> userids   = smsNotificationRecordsMapper.selectUserIdListNoticeId(documentId);
            List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList = JSON.parseArray(noticePeople , SwSmsNotificationRecords.NotifyPersonnel.class);
            notifyPersonnelList = notifyPersonnelList.stream().filter(i -> userids.contains(i.getUserID())).collect(Collectors.toList());
            swSmsNotificationRecords.setNotifyPersonnelList(notifyPersonnelList);
            swSmsNotificationRecords.setNoticePeople(JSON.toJSONString(notifyPersonnelList));
        }

        // 插入 通知历史记录
        NotifiCountHistory save = NotifiCountHistory.builder().pid(swSmsNotificationRecords.getId())
                .noticePeople(swSmsNotificationRecords.getNoticePeople()).build();
        notifiCountHistoryService.save(save);

    }

    @Override
    public TCirculateDTO getBizDataByBizId(Long bizId) {
        LambdaQueryWrapper<TCirculate>  q = new LambdaQueryWrapper();
        q.eq(TCirculate::getFromDataId,bizId)
                .eq(TCirculate::getCreateBy,SecurityUtils.getCurrentUserId())
        ;
        List<TCirculate> tCirculates = tCirculateMapper.selectListNoAdd(q);

        List<TCirculateDTO> copy = ListCopyUtil.copy(tCirculates , TCirculateDTO.class);
        TCirculateDTO tCirculateDTO = new TCirculateDTO();

        if(!copy.isEmpty() ){
            tCirculateDTO = copy.get(0);
            LambdaQueryWrapper<TCirculateUser> qw = new LambdaQueryWrapper();
            qw.eq(TCirculateUser::getCirculateId,tCirculateDTO.getId());
            List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectListNoAdd(qw);
            List<TCirculateUserDTO> userList = ListCopyUtil.copy(tCirculateUserList , TCirculateUserDTO.class);
            tCirculateDTO.setTCirculateUserDTOS(userList);
        }
        return tCirculateDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer forwardTCirculate(Long newTCirculateId, Long tCirculateId) {
        // 根据待转发传阅的 id 查出 fromDataId
        TCirculate tCirculate = tCirculateMapper.selectById(tCirculateId);

        //正文
        LambdaQueryWrapper<CscpDocumentFile> cdfl = new LambdaQueryWrapper<>();
        cdfl.eq(null != tCirculate.getFromDataId(), CscpDocumentFile::getFormDataId, tCirculate.getFromDataId());
        cdfl.eq(null == tCirculate.getFromDataId(), CscpDocumentFile::getFormDataId, tCirculate.getId());
        List<CscpDocumentFile> cscpDocumentFiles = cscpDocumentFileMapper.selectListNoAdd(cdfl);
        if (CollectionUtils.isNotEmpty(cscpDocumentFiles)) {
            // 将传阅的正文拷贝一份
            cscpDocumentFileService.documentCopyDocumentAndFile(tCirculateId,newTCirculateId,true,true);
        }

        //附件
        LambdaQueryWrapper<CscpEnclosureFile> cefl = new LambdaQueryWrapper();
        if (tCirculate.getCirculateStatus() == 0) {
            cefl.eq(CscpEnclosureFile::getFormDataId, tCirculate.getId());
        } else {
            cefl.and(qr -> qr.eq(CscpEnclosureFile::getFormDataId, tCirculate.getFromDataId()).or()
                    .eq(CscpEnclosureFile::getFormDataId, tCirculate.getId()));
        }
        List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(cefl);
        if (CollectionUtils.isNotEmpty(cscpEnclosureFiles)) {
            // 将传阅的附件拷贝一份
            cscpEnclosureFileService.enclosureChangeEnclosure(tCirculateId,newTCirculateId);
        }
        return 1;
    }

    @Override
    public List<QueryApprovalForwardRecordDTO> queryApprovalForwardRecord(Long approvalId, String processInstanceId) {
        List<BizApprovalForwardRecord> approvalForwardRecordList = approvalForwardRecordMapper.selectListNoAdd(Wrappers.<BizApprovalForwardRecord>lambdaQuery()
                .eq(BizApprovalForwardRecord::getFormDataId, approvalId)
                .isNull(BizApprovalForwardRecord::getProcessInstanceId)
                .orderByAsc(BizApprovalForwardRecord::getCreateTime));
        List<BizApprovalForwardRecord> approvalForwardApprovalList = approvalForwardRecordMapper.selectListNoAdd(Wrappers.<BizApprovalForwardRecord>lambdaQuery()
                .eq(StrUtil.isNotBlank(processInstanceId),BizApprovalForwardRecord::getForwardProcessInstanceId, processInstanceId)
                .eq(BizApprovalForwardRecord::getStageStatus,0)
                .orderByAsc(BizApprovalForwardRecord::getCreateTime));

        ArrayList<BizApprovalForwardRecord> forwardRecordList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(approvalForwardApprovalList)) {
            for (BizApprovalForwardRecord entity : approvalForwardApprovalList) {
                entity.setForwardUserName(entity.getCreateName());
                // 查询最新的转发子呈批件标题
                CscpProcBase cscpProcBase = cscpProcBaseRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpProcBase>()
                        .eq(CscpProcBase::getProcInstId, entity.getProcessInstanceId()));

                if (cscpProcBase != null && StrUtil.isNotBlank(cscpProcBase.getTitle())){
                    entity.setFormMainName(cscpProcBase.getTitle());
                }
                // 无业务标题的，自动拼接收文类型做formMainName
                if (StrUtil.isBlank(entity.getFormMainName())) {
                    entity.setFormMainName(entity.getProcTypeName().concat("(")
                            .concat(entity.getCreateName())
                            .concat(entity.getCreateTime().toString())
                            .concat(")"));
                }
                if (cscpProcBase!=null){
                    forwardRecordList.add(entity);
                }

            }
            approvalForwardRecordList.addAll(forwardRecordList);
        }

        List<QueryApprovalForwardRecordDTO> approvalForwardRecordDtoList = ListCopyUtil.copy(approvalForwardRecordList, QueryApprovalForwardRecordDTO.class);
        if (CollectionUtils.isEmpty(approvalForwardRecordList)) {
            return Collections.emptyList();
        }
        Map<String, List<QueryApprovalForwardRecordDTO>> approvalForwardRecordDtoMap = approvalForwardRecordDtoList.stream().collect(Collectors.groupingBy(QueryApprovalForwardRecordDTO::getForwardUserName));

        Map<Long, Boolean> repeatUpdateMap = new HashMap<>(16);
        for (Map.Entry<String, List<QueryApprovalForwardRecordDTO>> entry : approvalForwardRecordDtoMap.entrySet()) {
            List<QueryApprovalForwardRecordDTO> dtoList = entry.getValue();
            if (dtoList.size() == 1) {
                continue;
            }
            List<QueryApprovalForwardRecordDTO> recordDTOList = dtoList.stream().sorted(Comparator.comparing(QueryApprovalForwardRecordDTO::getCreateTime)).collect(Collectors.toList());
            for (int i = 0; i < recordDTOList.size(); i++) {
                QueryApprovalForwardRecordDTO dto = recordDTOList.get(i);
                if (i == 0) {
                    repeatUpdateMap.put(dto.getId(), false);
                } else {
                    repeatUpdateMap.put(dto.getId(), true);
                }
            }

        }
        approvalForwardRecordDtoList.forEach(approvalForwardRecord -> {
            approvalForwardRecord.setForwardTypeName(ApprovalForwardTypeEnums.obtainType(approvalForwardRecord.getForwardType()));
            // 转发人最后一次转发标红
            List<QueryApprovalForwardRecordDTO> forwardRecordDTOList = approvalForwardRecordDtoMap.get(approvalForwardRecord.getForwardUserName());
            List<QueryApprovalForwardRecordDTO> recordDTOList = forwardRecordDTOList.stream().sorted(Comparator.comparing(QueryApprovalForwardRecordDTO::getCreateTime).reversed()).limit(1).collect(Collectors.toList());
            if (Objects.equals(approvalForwardRecord.getId(), recordDTOList.get(0).getId())) {
                approvalForwardRecord.setRedFlag(true);
            } else {
                approvalForwardRecord.setRedFlag(false);
            }
            // 转发人多次修改标记
            approvalForwardRecord.setRepeatUpdateFlag(repeatUpdateMap.getOrDefault(approvalForwardRecord.getId(), false));
        });
        List<QueryApprovalForwardRecordDTO> sort = CollUtil.sort(approvalForwardRecordDtoList, Comparator.comparing(QueryApprovalForwardRecordDTO::getCreateTime).reversed());
        return sort;
    }


    /**
     * 收文转请示呈批
     * 转发和暂存，都会调这个接口
     *
     * @param queryApprovalForwardRecordDTO 起草的请示呈批件数据
     * @return 响应参数
     */
    @Override
    public void saveTransforApproval(QueryApprovalForwardRecordDTO queryApprovalForwardRecordDTO) {
        BizApprovalForwardRecord dto = approvalForwardRecordMapper.selectOneNoAdd(Wrappers.<BizApprovalForwardRecord>lambdaQuery()
                .eq(queryApprovalForwardRecordDTO.getFormDataId()!=null,BizApprovalForwardRecord::getFormDataId, queryApprovalForwardRecordDTO.getFormDataId())
                .eq(BizApprovalForwardRecord::getForwardType, ApprovalForwardTypeEnums.REQUEST_APPROVAL.getType()))
                //.eq(StrUtil.isNotBlank(queryApprovalForwardRecordDTO.getTaskId()),BizApprovalForwardRecord::getTaskId, queryApprovalForwardRecordDTO.getTaskId()))
                ;
        BizApprovalForwardRecord entity = BeanConvertUtils.copyProperties(queryApprovalForwardRecordDTO, BizApprovalForwardRecord.class);

        if (dto != null) {
            // 更新（收文转请示呈批件）暂存信息
            entity.setId(dto.getId());
            entity.setForwardUserName(SecurityUtils.getCurrentRealName());;
            entity.setUpdateBy(SecurityUtils.getCurrentUserId());;
            approvalForwardRecordMapper.updateById(entity);
        }else {
            // 保存，收文转请示呈批件
            if(StrUtil.isBlank(entity.getRootProcessInstanceId())){
                entity.setRootProcessInstanceId(entity.getProcessInstanceId());
            }

            entity.setCreateTime(LocalDateTime.now());
            entity.setForwardUserName(SecurityUtils.getCurrentRealName());
            entity.setForwardType(ApprovalForwardTypeEnums.REQUEST_APPROVAL.getType());
            entity.setForwardUserName(ApprovalForwardTypeEnums.REQUEST_APPROVAL.getName());
            approvalForwardRecordMapper.insert(entity);
        }
    }

    @Override
    public QueryApprovalForwardRecordDTO getParentApproval(String formDataId) {
        // 根据子实例id查询父实例id，然后查出父呈批件参数
        BizApprovalForwardRecord dto = approvalForwardRecordMapper.selectOneNoAdd(Wrappers.<BizApprovalForwardRecord>lambdaQuery()
                //.eq(BizApprovalForwardRecord::getProcessInstanceId, processInstanceId)
                .eq(BizApprovalForwardRecord::getFormDataId, formDataId)
                .in(BizApprovalForwardRecord::getStageStatus, Arrays.asList(0, 1)))
                ;

        if (dto==null){
            return null;
        }

        // 查询父呈批件是否已被删除，已删除则返回空
        if (dto.getForwardProcessInstanceId()!=null) {
            CscpProcBase cscpProcBase = cscpProcBaseRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpProcBase>()
                    .eq(CscpProcBase::getProcInstId, dto.getForwardProcessInstanceId()));

            if (cscpProcBase == null){
                return null;
            }
        }

        List<QueryApprovalForwardRecordDTO> result =  tCirculateMapper.selectParentApproval(dto.getForwardProcessInstanceId(), dto.getForwardTaskId());
        if(CollUtil.isEmpty(result)){
            return new QueryApprovalForwardRecordDTO();
        }

        return result.get(0);
    }

    @Override
    public void syncApprovalForwardRecord() {
        // 获取公文传阅
        List<TCirculate> tCirculateList = this.selectListNoAdd(Wrappers.<TCirculate>lambdaQuery().isNotNull(TCirculate::getFromDataId));
        Map<Long, TCirculate> tCirculateMap = tCirculateList.stream().collect(Collectors.toMap(TCirculate::getId, Function.identity()));

        // 获取公文传阅人
        List<TCirculateUser> tCirculateUserList = tCirculateUserMapper.selectListNoAdd(Wrappers.<TCirculateUser>lambdaQuery());
        Map<Long, List<TCirculateUser>> tCirculateUserMap = tCirculateUserList.stream().collect(Collectors.groupingBy(TCirculateUser::getCirculateId));
        for (Map.Entry<Long, List<TCirculateUser>> entry : tCirculateUserMap.entrySet()) {
            TCirculate tCirculate = tCirculateMap.get(entry.getKey());
            if (tCirculate == null) {
                continue;
            }
            List<TCirculateUser> circulateUserList = entry.getValue();
            Map<String, List<TCirculateUser>> map = circulateUserList.stream().collect(Collectors.groupingBy(user -> user.getCreateBy() + ":" + user.getCreateTime()));
            for (Map.Entry<String, List<TCirculateUser>> listEntry : map.entrySet()) {
                List<TCirculateUser> userList = listEntry.getValue();
                BizApprovalForwardRecord approvalForwardRecord = new BizApprovalForwardRecord();
                approvalForwardRecord.setFormDataId(entry.getKey());
                approvalForwardRecord.setForwardType(ApprovalForwardTypeEnums.CIRCULATE.getType());
                StringJoiner joiner = new StringJoiner(",");
                if (Objects.equals(tCirculate.getIsProcess(), 1)) {
                    joiner.add("处理单");
                }
                if (Objects.equals(tCirculate.getIsDoc(), 1)) {
                    joiner.add("正文");
                }
                if (Objects.equals(tCirculate.getIsAttach(), 1)) {
                    joiner.add("附件");
                }
                approvalForwardRecord.setForwardContent(joiner.toString());
                TCirculateUser tCirculateUser = userList.get(0);
                approvalForwardRecord.setForwardUserName(tCirculateUser.getCreateName());
                List<String> list = userList.stream().map(TCirculateUser::getUserName).distinct().collect(Collectors.toList());
                approvalForwardRecord.setReceiveUserName(String.join(",", list));
                approvalForwardRecord.setReceiveUserTotal(list.size());
                approvalForwardRecord.setCreateBy(tCirculateUser.getCreateBy());
                approvalForwardRecord.setCreateName(tCirculateUser.getCreateName());
                approvalForwardRecord.setCreateTime(tCirculateUser.getCreateTime());
                approvalForwardRecord.setUpdateBy(tCirculateUser.getUpdateBy());
                approvalForwardRecord.setUpdateName(tCirculateUser.getUpdateName());
                approvalForwardRecord.setUpdateTime(tCirculateUser.getUpdateTime());
                approvalForwardRecord.setCompanyId(tCirculateUser.getCompanyId());
                approvalForwardRecord.setDepartmentId(tCirculateUser.getDepartmentId());
                approvalForwardRecord.setTenantId(tCirculateUser.getTenantId());
                approvalForwardRecordMapper.insert(approvalForwardRecord);
            }

        }
    }



    /**
     * 查询密集期限数据字典
     * */
    private Map<String, String> getTSysDictMap(){
        List<TSysDictRecordDTO> dictRecordList = tSysDictRecordService.getDictRecordListByDictCode("mjqx", SecurityUtils.getCurrentCompanyId());
        Map<String, String> dictMap = dictRecordList.stream().collect(Collectors.toMap(TSysDictRecordDTO::getCode,TSysDictRecordDTO ::getName,(v1, v2) -> v1));
        return dictMap;
    }
}
