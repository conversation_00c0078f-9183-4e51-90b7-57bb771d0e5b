package com.ctsi.biaodan.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18
 * @apiNote 公文传阅类型
 */

@ApiModel(value="TCirculateTypeDTO对象", description="公文传阅类型")
public enum TCirculateTypeEnum {

    CIRCULATE("公文传阅", 0),
    CIRCULATE_TRAN("转公文传阅", 1);

    /**
     * 状态  0-新建传阅 1-转公文传阅
     */
    @ApiModelProperty(value = "状态: 0-新建传阅 1-转公文传阅")
    private String circulateType;

    /**
     * 状态  0-新建传阅 1-转公文传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-转公文传阅")
    private Integer circulateStatus;


    TCirculateTypeEnum(String circulateType, Integer circulateStatus) {
        this.circulateType = circulateType;
        this.circulateStatus = circulateStatus;
    }

    public String getCirculateType() {
        return circulateType;
    }

    public Integer getCirculateStatus() {
        return circulateStatus;
    }

    public static List<TCirculateTypeEnum> getAllCirculateTypes() {
        List<TCirculateTypeEnum> types = new ArrayList<>();
        types.add(CIRCULATE);
        types.add(CIRCULATE_TRAN);
        return types;
    }
}
