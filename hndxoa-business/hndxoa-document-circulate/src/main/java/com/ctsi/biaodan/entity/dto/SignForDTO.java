package com.ctsi.biaodan.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class SignForDTO {

    /**
     * 公文传阅id
     */
    @ApiModelProperty(value = "公文传阅id")
    private Long id;


    /**
     * 公文传阅回复内容
     */
    @ApiModelProperty(value = "公文传阅回复内容")
    private String replyData;

    @ApiModelProperty(value = "签收状态 0:未办 1:已阅 2:已读未签收")
    private Integer signInStatus;

    @ApiModelProperty(value = "签收和发起状态：1：未签收，2：我发起的")
    private Integer signOrSend;



    /**
     *  回复附件id
     */
    private Long fileid;
}
