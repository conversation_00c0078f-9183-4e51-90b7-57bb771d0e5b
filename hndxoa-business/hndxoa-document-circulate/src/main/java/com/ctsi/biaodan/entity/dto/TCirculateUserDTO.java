package com.ctsi.biaodan.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TCirculateUserDTO对象", description = "")
public class TCirculateUserDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long branchId;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;


    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;
}
