package com.ctsi.biaodan.mapper;

import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.dto.CscpUserRecentDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface TCirculateUserMapper extends MybatisBaseMapper<TCirculateUser> {


    /**
     * 查询本单位所有领导信息
     * @param limitNum  最近人数
     * @return
     */
    List<CscpUserRecentDTO> selectRecentPeople(Integer limitNum, @Param("userId")Long userId);

    /**
     * 查询本单位所有领导信息
     * @param userId  用户id
     * @return
     */
    List<CscpUserRecentDTO> selectUserCompanyDepart(@Param("userId") Long userId);
}
