package com.ctsi.biaodan.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TCirculateDTO对象", description = "")
public class TCirculateDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */

    @ApiModelProperty(value = "标题", required = true)
    @NotNull(message = "标题不能为空")
    private String title;

    /**
     * 阅读人集合id
     */

    @ApiModelProperty(value = "阅读人集合id", required = true)
    @NotNull(message = "阅读人集合id")
    private List<TCirculateUserDTO> tCirculateUserDTOS;


    /**
     * 传阅摘要
     */
    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long fromDataId;

    @ApiModelProperty(value = "业务id")
    private Long formDataId;

    /**
     * 短信提醒(0:不提醒 1：提醒)
     */
    @ApiModelProperty(value = "短信提醒(0:不提醒 1：提醒)")
    private Integer smsReminder;


    /**
     * 选人方式（0：本单位 1：自定义 2：外单位）
     */
    @ApiModelProperty(value = "选人方式（0：本单位 1：自定义 2：外单位）")
    private Integer selectionMethod;

    /**
     * 终止传阅（0：不终止 1：终止）
     */
    @ApiModelProperty(value = "终止传阅（0：不终止 1：终止）")
    private Integer termination;

    /**
     * 发送单位名称
     */
    @ApiModelProperty(value = "发送单位名称")
    private String companyName;


    /**
     * 流程定义
     */
    @ApiModelProperty(value = "流程定义")
    private String processDefinitionKey;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    /**
     * 子流程实例ID
     */
    @ApiModelProperty(value = "子流程实例ID")
    private String rootProcessInstanceId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    @ApiModelProperty("批阅意见")
    private String reviewComments;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevelName;


    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    /**
     * 是否显示附件  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示附件  0-否 1-是")
    private Integer isAttach;


    @ApiModelProperty(value = "是否显示正文  0-否 1-是")
    private Integer isDoc;
    /**
     * 是否显示处理单  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示处理单  0-否 1-是")
    private Integer isProcess;


    @ApiModelProperty(value = "打印/下载:  0-禁止打印和下载 1-能打印和下载")
    private String isPrint;


    /**
     * 转传阅 公文密级
     */
    private String secret;

    /**
     * 转发传阅，原来的id
     */
    private Long oldId;



    /**
     * 会议类型  1 是   ; 0/其他  否
     */
    private String meetingType;
    /**
     * 会议时间
     */
    private String meetingTime;
    /**
     * 会议地点
     */
    private String meetingPlace;

    // 呈批件转传阅 勾选的附件id列表
    private List<String> transferAnnexIdList;

}
