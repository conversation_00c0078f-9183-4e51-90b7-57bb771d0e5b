package com.ctsi.biaodan.service;

import com.ctsi.biaodan.entity.TCirculate;
import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.entity.dto.SwSmsNotificationRecordsBaseDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface ITCirculateService extends SysBaseServiceI<TCirculate> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<PageResTcirculateDTO> queryListPage(PageTcirculateDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TCirculateDTO> queryList(TCirculateDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TCirculateDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TCirculateDTO create(TCirculateDTO entity);

    /**
     * 收文转公文传阅数据
     *
     * @param entity
     * @return
     */
    void receiveCreate(TCirculateDTO entity);

    TCirculateDTO getReceiveDetails(Long formDataId);

    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TCirculateDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 查询消息传阅详情
     *
     * @param id
     * @return
     */
    ResTcirculate queryDetails(Long id);

    /**
     * 查询签收详情
     *
     * @param querysignForDTO
     * @param basePageForm
     * @return
     */
    DocumentDetailsDTO querysignFor(QuerysignForDTO querysignForDTO, BasePageForm basePageForm);

    /**
     * 撤销
     *
     * @param revokeIdList
     * @param id
     * @return
     */
    Integer revoke(List<Long> revokeIdList, Long id);

    /**
     * 补发
     *
     * @param userId
     * @param id
     */
    String reissue(List<TCirculateUserDTO> userId, Long id);

    /**
     * 终止传阅
     *
     * @param id
     * @return
     */
    Integer termination(Long id);

    /**
     * 获取公文传阅总人数, 已阅人数, 未阅人数
     *
     * @param querysignForDTO
     * @return
     */
    DocumentDetailsDTO queryPopulationDetails(QuerysignForDTO querysignForDTO);

    /**
     * 恢复传阅
     * @param id
     */
    void resetData(Long id);

    /**
     * 短信通知模块 公文传阅查询
     * @param tCirculateDTO
     * @param basePageForm
     * @return
     */
	PageResult<SwSmsNotificationRecordsBaseDTO> queryTCirculateForSmsPage(SwSmsNotificationRecordsBaseDTO tCirculateDTO , BasePageForm basePageForm);

    /**
     * 传阅短信发送次数 + 1
     * @param id
     */
    void updateCountPlus1(Long id);

    /**
     *  根据呈批件 业务id 查转传阅的数据
     * @param bizId
     * @return
     */
    TCirculateDTO  getBizDataByBizId(Long bizId);

    /**
     *  信息传阅转发，携带原有的附件
     * @param newTCirculateId   转发后的新业务id
     * @param tCirculateId  转发前的业务id
     * @return
     */
    Integer forwardTCirculate(Long newTCirculateId, Long tCirculateId);

    /**
     * 获取呈批件下转发记录
     *
     * @param approvalId 呈批件ID
     * @return 响应参数
     */
    List<QueryApprovalForwardRecordDTO> queryApprovalForwardRecord(Long approvalId, String processInstanceId);

    /**
     * 收文转请示呈批
     *
     * @param queryApprovalForwardRecordDTO 收文呈批件跳转新
     * @return 响应参数
     */
    void saveTransforApproval(QueryApprovalForwardRecordDTO queryApprovalForwardRecordDTO);

    /**
     * 子呈批件查询父呈批件
     *
     * @param formDataId 转发前的 formDataId
     * @return 响应参数
     */
    QueryApprovalForwardRecordDTO getParentApproval(String formDataId);

    void syncApprovalForwardRecord();
}
