package com.ctsi.biaodan.entity.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-01-13
 */
@Data
@ApiModel(value = "公文详细信息", description = "公文详细信息")
public class DocumentDetailsDTO {

    /**
     * 总条数
     */
    @ApiModelProperty(value = "总条数")
    private Integer circulatetotal;

    /**
     * 已阅人数
     */
    @ApiModelProperty(value = "已阅人数")
    private Integer readtotal;

    /**
     * 未阅人数
     */
    @ApiModelProperty(value = "未阅人数")
    private Integer forReadingtotal;


    // @ApiModelProperty(value = "0 未打开(未办)   1 打开过  2 已确认 ")
    // private Integer signInStatus;

    @ApiModelProperty(value = "未打开人数")
    private Integer noClick;

    @ApiModelProperty(value = "打开人数")
    private Integer click;

    @ApiModelProperty(value = "未确认人数")
    private Integer noSign;

    @ApiModelProperty(value = "确认人数")
    private Integer sign;

    /**
     * 公文列表
     */
    @ApiModelProperty(value = "公文列表")
    private PageResult collect;


    /**
     * 公文列表
     */
    @ApiModelProperty(value = "未打开公文列表")
    private List<ResSignFor> noClickList;
    @ApiModelProperty(value = "未确认公文列表")
    private List<ResSignFor> noSignList;

}
