package com.ctsi.biaodan.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PageResTcirculateDTO extends BaseDtoEntity {


    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "0:加急 1:平急 2:普通 3:特急 4:特提")
    private Integer degreeOfUrgency;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "催办时间")
    private LocalDateTime urgingTime;

    @ApiModelProperty(value = "终止传阅（0：不终止 1：终止）")
    private Integer termination;

    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    /**
     * 是否显示附件  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示附件  0-否 1-是")
    private Integer isAttach;

    /**
     * 是否显示处理单  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示处理单  0-否 1-是")
    private Integer isProcess;

    /**
     * 流程定义
     */
    @ApiModelProperty(value = "流程定义")
    private String processDefinitionKey;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    /**
     * 子流程实例ID
     */
    @ApiModelProperty(value = "子流程实例ID")
    private String rootProcessInstanceId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;


    /**
     * 关注  false 没有关注
     */
    @ApiModelProperty(value = "关注 ;false 没有关注; true 已关注")
    private Boolean isFocus =false;

    @ApiModelProperty(value = "发送范围/传阅人员信息")
    private List<String> userList;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     * 打开情况
     */
    @ApiModelProperty(value = "打开情况")
    private String openStatistics;

    /**
     * 打开颜色 : 1 红色 3/12 , 2 蓝色  12/12
     */
    private Integer openStatus;

    /**
     * 签收情况
     */
    @ApiModelProperty(value = "签收情况")
    private String signStatistics;
    /**
     * 签收颜色 : 1 红色 , 2 蓝色
     */
    private Integer signStatus;

    /**
     * 传阅摘要
     */
    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;


    /**
     * 类型id
     */
    @ApiModelProperty(value = "业务id")
    private Long fromDataId;


    /**
     * 会议类型  1 是   ; 0/其他  否
     */
    private String meetingType;
    /**
     * 会议时间
     */
    private String meetingTime;
    /**
     * 会议地点
     */
    private String meetingPlace;
    // 因原呈批件已作废，故转发后的本件内容丢失！
    private String messgeInfo;

}
