package com.ctsi.biaodan.entity.dto;

import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ResTcirculate {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;


    /**
     * 传阅摘要
     */
    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;

    /**
     * 阅读人
     */
    @ApiModelProperty(value = "阅读人")
    private String userName;

    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    private String createName;

    /**
     * 正文url
     */
    @ApiModelProperty(value = "正文在线预览url")
    private List<CscpDocumentFile> documentFiles;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<CscpEnclosureFile> enclosureFiles;

    /**
     * 公文回复
     */
    @ApiModelProperty(value = "公文回复")
    private String documentReply;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "用户签收时间")
    private LocalDateTime updateTime;


    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime createTime;

    /**
     * 是否需要短信提醒
     */
    @ApiModelProperty(value = "是否需要短信提醒（1：要 0：不要）")
    private Integer smsReminder;

    /**
     * ，对应数据字典：mjqx
     */
    @ApiModelProperty(value = "密级期限code，对应数据字典：mjqx")
    private String secret;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     *  自己的回复附件
     */
    private List<CscpEnclosureFile> replyFileList;

    /**
     * 回复信息
     */
    private TCirculateUser userReplyInfo;


    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    /**
     * 是否显示附件  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示附件  0-否 1-是")
    private Integer isAttach;


    @ApiModelProperty(value = "是否显示正文  0-否 1-是")
    private Integer isDoc;
    /**
     * 是否显示处理单  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示处理单  0-否 1-是")
    private Integer isProcess;


    @ApiModelProperty(value = "不能打印和下载  0-能打印和下载 1-不能打印和下载")
    private String isPrint;


    /**
     * 处理单标题
     */
    @ApiModelProperty(value = "处理单标题")
    private String titleCld;
    /**
     * 流程信息数据
     */
    private com.ctsi.activiti.core.vo.TaskVO taskVO;


    @ApiModelProperty(value = "1 已经关注, null 没关注")
    private String focus;


    /**
     * 会议类型  1 是   ; 0/其他  否
     */
    private String meetingType;
    /**
     * 会议时间
     */
    private String meetingTime;
    /**
     * 会议地点
     */
    private String meetingPlace;
}
