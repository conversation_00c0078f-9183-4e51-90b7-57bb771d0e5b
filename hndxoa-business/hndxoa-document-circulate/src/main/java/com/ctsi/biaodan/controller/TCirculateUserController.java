package com.ctsi.biaodan.controller;

import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.biaodan.entity.TCirculateUser;
import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.biaodan.service.ITCirculateUserService;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tCirculateUser")
@Api(value = "公文传阅签收接口", tags = "公文传阅签收接口")
public class TCirculateUserController extends BaseController {

    private static final String ENTITY_NAME = "tCirculateUser";

    @Autowired
    private ITCirculateUserService tCirculateUserService;
    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    /**
     * 【首页-信息传阅，未阅翻页查询】查询未读传阅分页条件查询
     */
    @GetMapping("/queryTCirculateUserPage")
    @ApiOperation(value = "查询未读传阅分页条件查询", notes = "传入参数")
    public ResultVO<PageResult<ResTcirculateUserDTO>> queryTCirculateUserPage(PageTcirculateDTO tCirculateUserDTO, BasePageForm basePageForm) {
        PageResult<ResTcirculateUserDTO> tCirculateUserDTOPageResult = tCirculateUserService.queryListPage(tCirculateUserDTO, basePageForm);
        List<ResTcirculateUserDTO> data = tCirculateUserDTOPageResult.getData();
        for (ResTcirculateUserDTO datum : data) {
            Long fromDataId = datum.getFromDataId();
            if(null != fromDataId){
                CscpProcBase base = cscpProcBaseService.getProcBaseByFormDataId(fromDataId.toString());
                if(null == base){
                    datum.setMessgeInfo("因原呈批件已作废，故转发后的本件内容丢失！");
                }
            }
        }
        return ResultVO.success(tCirculateUserDTOPageResult);
    }

    /**
     * 查询未读传阅条数
     */
    @GetMapping("/queryUnreadCirculationNumber")
    @ApiOperation(value = "查询未读传阅条数", notes = "传入参数")
    public ResultVO<Integer> queryUnreadCirculationNumber() {
        Integer count = tCirculateUserService.queryUnreadCirculationNumber();
        return ResultVO.success(Optional.ofNullable(count).orElse(0));
    }

    /**
     * 传阅签收确认签收
     */
    @PostMapping("/signFor")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "公文传阅确认签收")
    @ApiOperation(value = "传阅签收确认签收", notes = "传入参数")
    public ResultVO signFor(@RequestBody SignForDTO signForDTO) {
        Assert.notNull(signForDTO.getId(), "general.IdNotNull");
        Integer count = tCirculateUserService.signFor(signForDTO);
        if (count > 0) {
            return ResultVO.success("修改成功");
        }
        return ResultVO.error("修改失败");
    }

    /**
     * 查询已读传阅分页条件查询
     */
    @GetMapping("/queryReadPage")
    @ApiOperation(value = "查询已读传阅分页条件查询", notes = "传入参数")
    public ResultVO<PageResult<ResTcirculateUserDTO>> queryReadPage(PageTcirculateDTO tCirculateUserDTO, BasePageForm basePageForm) {
        tCirculateUserDTO.setUserId(SecurityUtils.getCurrentUserId());
        tCirculateUserDTO.setSignInStatus(1);
        PageResult<ResTcirculateUserDTO> tCirculateUserDTOPageResult = tCirculateUserService.queryReadPage(tCirculateUserDTO, basePageForm);

        List<ResTcirculateUserDTO> data = tCirculateUserDTOPageResult.getData();
        for (ResTcirculateUserDTO datum : data) {
            Long fromDataId = datum.getFromDataId();
            if(null != fromDataId){
                CscpProcBase base = cscpProcBaseService.getProcBaseByFormDataId(fromDataId.toString());
                if(null == base){
                    datum.setMessgeInfo("因原呈批件已作废，故转发后的本件内容丢失！");
                }
            }
        }

        return ResultVO.success(tCirculateUserDTOPageResult);
    }


    /**
     *
     * @param id 业务数据id
     * @return
     */
    @GetMapping("/getMySignDataById/{id}")
    @ApiOperation(value = "消息传阅分页查询多条数据", notes = "传入参数")
    public ResultVO<ResTcirculateUserDTO> getMySignDataById(@PathVariable Long id) {
        PageTcirculateDTO pageTcirculateDTO = new PageTcirculateDTO();
        pageTcirculateDTO.setId(id);
        pageTcirculateDTO.setUserId(SecurityUtils.getCurrentUserId());
        PageResult<ResTcirculateUserDTO> data =
                tCirculateUserService.queryReadPage(pageTcirculateDTO, new BasePageForm());
        List<ResTcirculateUserDTO> data1 = data.getData();
        return ResultVO.success(data1 != null && !data1.isEmpty() ?  data1.get(0) : null);
    }


    /**
     * 公文回复
     */
    @PostMapping("/documentReply")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "公文回复")
    @ApiOperation(value = "公文回复", notes = "传入参数")
    public ResultVO<PageResult<TCirculateUserDTO>> documentReply(@RequestBody DocumentReplyDTO documentReplyDTO) {
        Integer count = tCirculateUserService.documentReply(documentReplyDTO);
        if (count > 0) {
            return ResultVO.success("回复成功");
        }
        return ResultVO.error("回复失败");
    }

    /**
     * 查询已回复传阅分页条件查询(完成)
     */
    @GetMapping("/queryReplyPage")
    @ApiOperation(value = "查询已回复传阅分页条件查询", notes = "传入参数")
    public ResultVO<PageResult<TCirculateUserDTO>> queryReplyPage(PageTcirculateDTO pageTcirculateDTO, BasePageForm basePageForm) {
        PageResult<TCirculateUserDTO> tCirculateUserDTOPageResult = tCirculateUserService.queryReplyPage(pageTcirculateDTO, basePageForm);
        return ResultVO.success(tCirculateUserDTOPageResult);
    }


    /**
     * 一键催办
     */
    @PostMapping("/onekeyUrge")
    @ApiOperation(value = "一键催办", notes = "传入参数")
    public ResultVO<Integer> onekeyUrge(@RequestBody QuerysignForDTO querysignForDTO) {
        Integer urgeCount = tCirculateUserService.onekeyUrge(querysignForDTO);
        if (null != urgeCount) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 催办单个
     */
    @PostMapping("/urge")
    @ApiOperation(value = "催办单个", notes = "传入参数")
    public ResultVO<Integer> urge(@RequestBody @Valid UrgeDTO urge) {
        Integer urgeCount = tCirculateUserService.urge(urge);
        if (null != urgeCount) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * app查询角标（未看公文）
     *
     * @return
     */
    @GetMapping("/getAngleMark")
    @ApiOperation(value = "app查询角标（未看公告）")
    public ResultVO<Integer> getAngleMark() {
        return ResultVO.success(tCirculateUserService.getAngleMark());
    }

    /**
     * 查询列表
     *
     * @return
     */
    @GetMapping("/getListByFormDataId")
    @ApiOperation(value = "查询列表")
    public ResultVO<List<TCirculateUser>> getListByFormDataId(@RequestParam Long formDataId) {
        return ResultVO.success(tCirculateUserService.getListByFormDataId(formDataId));
    }

    /**
     * 查询当前用户最近发送的10个人
     */
    @GetMapping("/getRecentPeople")
    @ApiOperation(value = "查询最近当前用户最近发送的10个人", notes = "无传入参数")
    public ResultVO get() {
        return ResultVO.success(tCirculateUserService.getCirculateNoticeRecentPeople());
    }

    /**
     * 查询公文传阅类型
     */
    @GetMapping("/getCirculateType")
    @ApiOperation(value = "查询公文传阅类型", notes = "无传入参数")
    public ResultVO getCirculateType() {
        return ResultVO.success(tCirculateUserService.getCirculateType());
    }
}
