package com.ctsi.biaodan.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 呈批件转发记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@ApiModel(value = "BizApprovalForwardRecordDTO对象", description = "呈批件转发记录")
public class BizApprovalForwardRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long formDataId;

    @ApiModelProperty("转发类型 0:转公文传阅 1:转通知公告")
    private Integer forwardType;

    @ApiModelProperty("转发人名称")
    private String forwardUserName;

    @ApiModelProperty("转发内容")
    private String forwardContent;

    @ApiModelProperty("接收用户名称")
    private String receiveUserName;

    @ApiModelProperty("接收人总数")
    private Integer receiveUserTotal;


}
