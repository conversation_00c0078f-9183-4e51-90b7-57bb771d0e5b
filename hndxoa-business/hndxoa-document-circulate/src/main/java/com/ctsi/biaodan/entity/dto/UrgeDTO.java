package com.ctsi.biaodan.entity.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2021-08-13
 */
@Data
@ApiModel(value = "催办", description = "催办")
public class UrgeDTO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;


    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题", required = true)
    @NotNull(message = "公告标题不能为空")
    private String title;
}
