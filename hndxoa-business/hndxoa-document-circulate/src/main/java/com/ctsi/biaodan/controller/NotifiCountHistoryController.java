package com.ctsi.biaodan.controller;


import com.ctsi.biaodan.entity.dto.NotifiCountHistoryDTO;
import com.ctsi.biaodan.entity.dto.TCirculateDTO;
import com.ctsi.biaodan.service.INotifiCountHistoryService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.result.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 用户关注 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/notifiHistory")
@Api(value = "公文传阅通知历史记录", tags = "公文传阅通知历史记录 接口")
public class NotifiCountHistoryController extends BaseController {


	@Autowired
	private INotifiCountHistoryService notifiCountHistoryService;
	/**
	 * 查询多条数据.不分页
	 */
	@GetMapping("/queryList")
	@ApiOperation(value = "查询多条数据", notes = "传入参数")
	public ResultVO<List<NotifiCountHistoryDTO>> queryList(NotifiCountHistoryDTO dto) {
		List<NotifiCountHistoryDTO> list = notifiCountHistoryService.queryList(dto);
		return ResultVO.success(list);
	}
}
