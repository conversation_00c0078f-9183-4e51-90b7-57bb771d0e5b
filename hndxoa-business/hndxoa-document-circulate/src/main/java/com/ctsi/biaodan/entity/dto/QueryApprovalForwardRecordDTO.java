package com.ctsi.biaodan.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024-02-21 15:37
 */
@Data
public class QueryApprovalForwardRecordDTO {

    @ApiModelProperty(value = "转发ID")
    private Long id;

    @ApiModelProperty("转发类型 0:转公文传阅 1:转通知公告 2：转请示呈批件")
    private Integer forwardType;

    @ApiModelProperty("转发类型 0:转公文传阅 1:转通知公告 2：转请示呈批件")
    private String forwardTypeName;

    @ApiModelProperty("转发人名称")
    private String forwardUserName;

    @ApiModelProperty("转发内容")
    private String forwardContent;

    @ApiModelProperty("接收用户名称")
    private String receiveUserName;

    @ApiModelProperty("接收人总数")
    private Integer receiveUserTotal;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "是否重复修改  如果为ture展示更新")
    private boolean repeatUpdateFlag;

    @ApiModelProperty(value = "是否标红")
    private boolean redFlag;

    /**
     * 业务标题
     */
    @ApiModelProperty(value = "业务标题")
    private String formMainName;

    /**
     * 子呈批件业务标题
     */
    @ApiModelProperty(value = "子呈批件业务标题")
    private String title;

    /**
     * nodeKey
     */
    @ApiModelProperty(value = "nodeKey")
    private String nodeKey;

    /**
     * 流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants
     */
    @ApiModelProperty(value = "流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants")
    private Integer bpm_status;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    /**
     * 流程类型名
     */
    @ApiModelProperty(value = "流程类型名")
    private String procTypeName;

    @ApiModelProperty("表单业务ID")
    private Long formDataId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    /**
     * 流程父节id
     */
    @ApiModelProperty(value = "流程父节id")
    private String rootProcessInstanceId;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    /**
     * 转发前的process_instance_id
     */
    @ApiModelProperty(value = "转发前的process_instance_id")
    private String forwardProcessInstanceId;

    /**
     * 流程定义值
     */
    @ApiModelProperty(value = "流程定义值")
    private String processDefinitionKey;

    /**
     * 转发前父任务id
     */
    @ApiModelProperty(value = "转发前父任务id")
    private String forwardTaskId;

    /**
     * 父呈批件密级期限
     */
    @ApiModelProperty(value = "父呈批件密级期限")
    private String durationClassification;

    /**
     * 转发状态：1-暂存，0-提交转发
     */
    @ApiModelProperty(value = "转发状态：1-暂存，0-提交转发")
    private Integer stageStatus;
}
