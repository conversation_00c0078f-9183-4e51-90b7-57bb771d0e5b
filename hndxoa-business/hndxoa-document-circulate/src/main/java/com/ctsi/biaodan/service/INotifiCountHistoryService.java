package com.ctsi.biaodan.service;

import com.ctsi.biaodan.entity.NotifiCountHistory;
import com.ctsi.biaodan.entity.dto.NotifiCountHistoryDTO;
import com.ctsi.hndx.common.SysBaseServiceI;

import java.util.List;

/**
 * <p>
 * 用户关注 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
public interface INotifiCountHistoryService extends SysBaseServiceI<NotifiCountHistory> {

	/**
	 * 查询历史记录
	 * @param dto
	 * @return
	 */
	List<NotifiCountHistoryDTO> queryList(NotifiCountHistoryDTO dto);
}
