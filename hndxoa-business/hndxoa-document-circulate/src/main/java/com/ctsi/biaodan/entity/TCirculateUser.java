package com.ctsi.biaodan.entity;

import com.ctsi.hndx.common.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "t_circulate_user", description = "")
public class TCirculateUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @TableField("USER_ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField("USER_NAME")
    private String userName;

    /**
     * 传阅id
     */
    @ApiModelProperty(value = "传阅id")
    @TableField("CIRCULATE_ID")
    private Long circulateId;

    /**
     * 签收状态 0:未办 1:已阅
     * 0-未打开(未办)，1-已确认，2-已打开未确认  <未确认需要动态计算,总数-确认数量>
     */
    @ApiModelProperty(value = "0-未打开(未办)，1-已确认，2-已打开未确认 ")
    @TableField("SIGN_IN_STATUS")
    private Integer signInStatus;

    @ApiModelProperty(value = "公文回复")
    private String documentReply;

    @ApiModelProperty(value = "签收时间")
    private LocalDateTime signInTime;


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long branchId;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;


    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


    /**
     * 催办时间
     */
    @ApiModelProperty(value = "催办时间")
    private LocalDateTime urgingTime;


    @ApiModelProperty(value = "首次打开时间")
    private LocalDateTime firstOpenTime;




}
