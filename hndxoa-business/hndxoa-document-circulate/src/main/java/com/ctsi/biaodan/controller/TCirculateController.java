package com.ctsi.biaodan.controller;

import com.ctsi.biaodan.entity.dto.*;
import com.ctsi.biaodan.service.ITCirculateService;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.entity.dto.SwSmsNotificationRecordsBaseDTO;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tCirculate")
@Api(value = "公文传阅", tags = "公文传阅 接口")
public class TCirculateController extends BaseController {


    @Autowired
    private ITCirculateService tCirculateService;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    /**
     * 新增公文传阅数据
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "增加公文传阅")
    @ApiOperation(value = "新增公文传阅数据", notes = "传入参数")
    public ResultVO<TCirculateDTO> create(@RequestBody @Valid TCirculateDTO tCirculateDTO) throws URISyntaxException {
        TCirculateDTO dto = tCirculateService.create(tCirculateDTO);
        return ResultVO.success(dto);
    }

    /**
     * 更新公文传阅数据
     */
    @PutMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新公文传阅")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    public ResultVO update(@RequestBody TCirculateDTO tCirculateDTO) {
        tCirculateDTO.setId(tCirculateDTO.getId() == 0 ? null : tCirculateDTO.getId());
        Assert.notNull(tCirculateDTO.getId(), "general.IdNotNull");
        int count = tCirculateService.update(tCirculateDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.(完成)
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除公文传阅")
    public ResultVO delete(@PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        int count = tCirculateService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 终止传阅
     */
    @PostMapping("/termination/{id}")
    @ApiOperation(value = "终止传阅", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "终止传阅")
    public ResultVO termination(@PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        Integer count = tCirculateService.termination(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 恢复传阅
     */
    @PostMapping("/resetData/{id}")
    @ApiOperation(value = "恢复传阅", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "恢复传阅")
    public ResultVO resetData(@PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        tCirculateService.resetData(id);
          return ResultVO.success();

    }


    /**
     * 消息传阅分页查询多条数据.(完成) 我发布的
     */
    @GetMapping("/queryTCirculatePage")
    @ApiOperation(value = "消息传阅分页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<PageResTcirculateDTO>> queryTCirculatePage(PageTcirculateDTO tCirculateDTO, BasePageForm basePageForm) {
        PageResult<PageResTcirculateDTO> pageResTcirculateDTOPageResult = tCirculateService.queryListPage(tCirculateDTO, basePageForm);

        List<PageResTcirculateDTO> data = pageResTcirculateDTOPageResult.getData();
        for (PageResTcirculateDTO datum : data) {
            Long fromDataId = datum.getFromDataId();
            if(null != fromDataId){
                CscpProcBase base = cscpProcBaseService.getProcBaseByFormDataId(fromDataId.toString());
                if(null == base){
                    datum.setMessgeInfo("因原呈批件已作废，故转发后的本件内容丢失！");
                }
            }
        }
        return ResultVO.success(pageResTcirculateDTOPageResult);
    }




    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryTCirculate")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    public ResultVO<List<TCirculateDTO>> queryTCirculate(TCirculateDTO tCirculateDTO) {
        List<TCirculateDTO> list = tCirculateService.queryList(tCirculateDTO);
        return ResultVO.success(list);
    }

    /**
     * 查询传阅详情，(完成)
     */
    @GetMapping("/queryDetail/{id}")
    @ApiOperation(value = "查询传阅详情", notes = "传入参数")
    public ResultVO<ResTcirculate> queryDetails(@PathVariable Long id) {
        ResTcirculate resTcirculate = tCirculateService.queryDetails(id);
        return ResultVO.success(resTcirculate);
    }


    /**
     * 分页查询签收详情(完成)
     */
    @GetMapping("/queryDetails")
    @ApiOperation(value = "分页查询签收详情", notes = "传入参数")
    public ResultVO<DocumentDetailsDTO> querysignFor(QuerysignForDTO querysignForDTO, BasePageForm basePageForm) {
        DocumentDetailsDTO documentDetails = tCirculateService.querysignFor(querysignForDTO, basePageForm);
        return ResultVO.success(documentDetails);
    }


    /**
     * 获取公文传阅总人数, 已阅人数, 未阅人数
     */
    @GetMapping("/queryPopulationDetails")
    @ApiOperation(value = "获取公文传阅总人数, 已阅人数, 未阅人数", notes = "传入参数")
    public ResultVO<DocumentDetailsDTO> queryPopulationDetails(QuerysignForDTO querysignForDTO) {
        DocumentDetailsDTO documentDetails = tCirculateService.queryPopulationDetails(querysignForDTO);
        return ResultVO.success(documentDetails);
    }

    /**
     * 撤销指定消息传阅（撤销全部和撤销单个人）
     */
    @DeleteMapping("/revoke/{id}")
    @ApiOperation(value = "撤销指定消息传阅（撤销全部和撤销单个人）", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "撤销指定消息传阅（撤销全部和撤销单个人）")
    public ResultVO revoke(@RequestBody List<Long> revokeIdList, @PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        Assert.notNull(revokeIdList, "id不能为空");
        Integer count = tCirculateService.revoke(revokeIdList, id);
        return ResultVO.success(count);
    }

    /**
     * 消息传阅补发
     */
    @PostMapping("/reissue/{id}")
    @ApiOperation(value = "消息传阅补发", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "公文传阅消息补发")
    public ResultVO reissue(@RequestBody List<TCirculateUserDTO> user, @PathVariable Long id) {
        Assert.notNull(id, "id不能为空");
        String reissue = tCirculateService.reissue(user , id);
        return  ResultVO.success(reissue) ;
    }

    /**
     * 收文转公文传阅数据
     */
    @PostMapping("/receiveCreate")
    @OperationLog(dBOperation = DBOperation.ADD, message = "收文转公文传阅数据")
    @ApiOperation(value = "收文转公文传阅数据", notes = "传入参数")
    public ResultVO<String> receiveCreate(@RequestBody @Valid TCirculateDTO tCirculateDTO) throws URISyntaxException {
        tCirculateService.receiveCreate(tCirculateDTO);
        return ResultVO.success("");
    }

    /**
     * 查询传阅卡详情
     */
    @GetMapping("/getReceiveDetails")
    @ApiOperation(value = "查询传阅卡详情")
    public ResultVO<TCirculateDTO> getReceiveDetails(@RequestParam Long formDataId) {
        return ResultVO.success(tCirculateService.getReceiveDetails(formDataId));
    }

    //-------------------------公文传阅短信通知相关--------------------------start

    /**
     * 短信通知模块 公文传阅查询   type 需要增加该字段
     */
    @GetMapping("/queryTCirculateForSmsPage")
    @ApiOperation(value = "短信通知模块 公文传阅查询", notes = "传入参数")
    public ResultVO<PageResult<SwSmsNotificationRecordsBaseDTO>> queryTCirculateForSmsPage(SwSmsNotificationRecordsBaseDTO tCirculateDTO,
                                                                                           BasePageForm basePageForm) {
        PageResult<SwSmsNotificationRecordsBaseDTO> pageResTcirculateDTOPageResult = tCirculateService.queryTCirculateForSmsPage(tCirculateDTO, basePageForm);
        return ResultVO.success(pageResTcirculateDTOPageResult);
    }


    /**
     *  点击 打印通知单 次数加1
     */
    @PostMapping("/updateCount/{id}")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "点击 打印通知单 次数加1")
    public ResultVO updateCount(@PathVariable Long id) {
        Assert.notNull(id, "general.IdNotNull");
        // 点击一次插入一条记录 数字加1
        tCirculateService.updateCountPlus1(id);
        return ResultVO.success();

    }

    //-------------------------公文传阅短信通知相关--------------------------end


     // 呈批件业务id 查 上次呈批件 转传阅的数据,可能为空

    @GetMapping("/getBizDataByBizId/{id}")
    public ResultVO<TCirculateDTO> getBizDataByBizId(@PathVariable Long id) {
        Assert.notNull(id, "general.IdNotNull");
        TCirculateDTO data = tCirculateService.getBizDataByBizId(id);
        return ResultVO.success(data);

    }

    /**
     *  信息传阅转发，携带原有的附件
     */
    @PostMapping("/forwardTCirculate")
    @OperationLog(dBOperation = DBOperation.ADD,message = "信息传阅转发，携带原有的正文和附件")
    public ResultVO forwardTCirculate(@ApiParam(required = true, value = "转发后的新业务id")@RequestParam Long newTCirculateId,
                            @ApiParam(required = true, value = "转发前的业务id")@RequestParam Long tCirculateId) {
        tCirculateService.forwardTCirculate(newTCirculateId, tCirculateId);
        return ResultVO.success();

    }

    @GetMapping("/queryApprovalForwardRecord/{approvalId}/{processInstanceId}")
    @ApiOperation(value = "查询呈批件转发记录", notes = "传入参数")
    public ResultVO<List<QueryApprovalForwardRecordDTO>> queryApprovalForwardRecord(@PathVariable("approvalId") Long approvalId,
                                                                                    @PathVariable("processInstanceId") String processInstanceId) {
        return ResultVO.success(tCirculateService.queryApprovalForwardRecord(approvalId, processInstanceId));
    }


    @PostMapping("/saveTransforApproval")
    @ApiOperation(value = "收文转请示呈批", notes = "传入参数")
    public ResultVO saveTransforApproval(@RequestBody QueryApprovalForwardRecordDTO queryApprovalForwardRecordDTO) {
        tCirculateService.saveTransforApproval(queryApprovalForwardRecordDTO);
        return ResultVO.success(true);
    }

    @GetMapping("/getParentApproval/{formDataId}")
    @ApiOperation(value = "子呈批件查询父呈批件", notes = "传入参数")
    public ResultVO<QueryApprovalForwardRecordDTO>  getParentApproval(@PathVariable("formDataId") String formDataId) {
        return ResultVO.success( tCirculateService.getParentApproval(formDataId));
    }


    @GetMapping("/syncApprovalForwardRecord")
    @ApiOperation(value = "同步呈批件转传阅记录", notes = "传入参数")
    public ResultVO<Void> syncApprovalForwardRecord() {
        tCirculateService.syncApprovalForwardRecord();
        return ResultVO.success();
    }

}
