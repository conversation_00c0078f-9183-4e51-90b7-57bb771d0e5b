package com.ctsi.biaodan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 呈批件转发记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@TableName("biz_approval_forward_record")
@ApiModel(value = "BizApprovalForwardRecord对象", description = "呈批件转发记录")
public class BizApprovalForwardRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long formDataId;

    @ApiModelProperty("转发类型 0:转公文传阅 1:转通知公告")
    private Integer forwardType;

    @ApiModelProperty("转发人名称")
    private String forwardUserName;

    @ApiModelProperty("转发内容")
    private String forwardContent;

    @ApiModelProperty("接收用户名称")
    private String receiveUserName;

    @ApiModelProperty("接收人总数")
    private Integer receiveUserTotal;

    /**
     * 业务标题
     */
    @ApiModelProperty(value = "业务标题")
    private String formMainName;

    /**
     * 子呈批件业务标题
     */
    @ApiModelProperty(value = "子呈批件业务标题")
    private String title;

    /**
     * nodeKey
     */
    @ApiModelProperty(value = "nodeKey")
    private String nodeKey;

    /**
     * 流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants
     */
    @ApiModelProperty(value = "流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants")
    private Integer bpm_status;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    /**
     * 流程类型名
     */
    @ApiModelProperty(value = "流程类型名")
    private String procTypeName;


    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    /**
     * 流程父节id
     */
    @ApiModelProperty(value = "流程父节id")
    private String rootProcessInstanceId;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    /**
     * 转发前的process_instance_id
     */
    @ApiModelProperty(value = "转发前的process_instance_id")
    private String forwardProcessInstanceId;

    /**
     * 流程定义值
     */
    @ApiModelProperty(value = "流程定义值")
    private String processDefinitionKey;

    /**
     * 转发前父任务id
     */
    @ApiModelProperty(value = "转发前父任务id")
    private String forwardTaskId;

    /**
     * 转发状态：1-暂存，0-提交转发
     */
    @ApiModelProperty(value = "转发状态：1-暂存，0-提交转发")
    private Integer stageStatus;
}
