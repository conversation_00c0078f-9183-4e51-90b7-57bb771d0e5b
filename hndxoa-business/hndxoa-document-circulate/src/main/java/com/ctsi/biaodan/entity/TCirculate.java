package com.ctsi.biaodan.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "t_circulate", description = "公文传阅")
public class TCirculate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;


    /**
     * 传阅摘要
     */
    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer typeName;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private Long typeId;



    /**
     * 是否需要短信提醒
     */
    @ApiModelProperty(value = "是否需要短信提醒（1：要 0：不要）")
    private Integer smsReminder;

    /**
     * 选人方式（0：本单位 1：自定义 2：外单位）
     */
    @ApiModelProperty(value = "选人方式（0：本单位 1：自定义 2：外单位）")
    private Integer selectionMethod;

    /**
     * 终止传阅（0：不终止 1：终止）
     */
    @ApiModelProperty(value = "终止传阅（0：不终止 1：终止）")
    private Integer termination;

    /**
     * 终止传阅时间
     */
    @ApiModelProperty(value = "终止传阅时间")
    private LocalDateTime terminationTime;

    /**
     * 发送单位名称
     */
    @ApiModelProperty(value = "发送单位名称")
    private String companyName;
    /**
     * 发送部门名称
     */
    @ApiModelProperty(value = "发送部门名称")
    private String departmentName;


    /**
     * 流程定义
     */
    @ApiModelProperty(value = "流程定义")
    private String processDefinitionKey;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    /**
     * 子流程实例ID
     */
    @ApiModelProperty(value = "子流程实例ID")
    private String rootProcessInstanceId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevelName;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "业务id")
    private Long fromDataId;

    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    /**
     * 是否显示附件  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示附件  0-否 1-是")
    private Integer isAttach;


    @ApiModelProperty(value = "是否显示正文  0-否 1-是")
    private Integer isDoc;
    /**
     * 是否显示处理单  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示处理单  0-否 1-是")
    private Integer isProcess;


    @ApiModelProperty(value = "打印/下载:  0-禁止打印和下载 1-能打印和下载")
    private String isPrint;

    /**
     * 转传阅 公文密级
     */
    private String secret;


    /**
     * 会议类型  1 是   ; 0/其他  否
     */
    private String meetingType;
    /**
     * 会议时间
     */
    private String meetingTime;
    /**
     * 会议地点
     */
    private String meetingPlace;



}
