package com.ctsi.biaodan.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ResTcirculateUserDTO extends BaseDtoEntity {

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;


    /**
     * 标题
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    /**
     * 0:加急 1:平急 2:普通 3:特急 4:特提
     */
    @ApiModelProperty(value = "0:加急 1:平急 2:普通 3:特急 4:特提")
    private Integer degreeOfUrgency;

    /**
     * 签收状态 0:未办 1:已阅
     */
    @ApiModelProperty(value = "签收状态 0:未办 1:已阅")
    private Integer signInStatus;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "签收用户名称")
    private String userName;


    /**
     * 创建用户名
     */
    @ApiModelProperty(value = "创建用户名")
    private String createName;


    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long companyId;


    /**
     * 发送单位名称
     */
    @ApiModelProperty(value = "发送单位名称")
    private String companyName;

    /**
     * 发送部门名称
     */
    @ApiModelProperty(value = "发送部门名称")
    private String departmentName;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String documentReply;

    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    /**
     * 是否显示附件  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示附件  0-否 1-是")
    private Integer isAttach;

    /**
     * 是否显示处理单  0-否 1-是
     */
    @ApiModelProperty(value = "是否显示处理单  0-否 1-是")
    private Integer isProcess;

    /**
     * 流程定义
     */
    @ApiModelProperty(value = "流程定义")
    private String processDefinitionKey;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    /**
     * 子流程实例ID
     */
    @ApiModelProperty(value = "子流程实例ID")
    private String rootProcessInstanceId;

    /**
     * 表单id
     */
    @ApiModelProperty(value = "表单id")
    private String formId;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "业务id")
    private Long fromDataId;

    /**
     * 关注  false 没有关注
     */
    @ApiModelProperty(value = "关注 ;false 没有关注; true 已关注")
    private Boolean isFocus =false;

    @ApiModelProperty(value = "发送范围/传阅人员信息")
    private List<String> userList;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String secret;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevelName;

    /**
     * 密级期限Code
     */
    @ApiModelProperty(value = "密级期限Code")
    private String secretLevelCode;


    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     * 首次打开时间
     */
    @ApiModelProperty(value = "首次打开时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String firstOpenTime;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    private String signInTime;

    /**
     * 打开情况
     */
    @ApiModelProperty(value = "打开情况")
    private String openStatistics;

    /**
     * 签收情况
     */
    @ApiModelProperty(value = "签收情况")
    private String signStatistics;

    /**
     * 传阅摘要
     */
    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;

    /**
     * 会议类型  1 是   ; 0/其他  否
     */
    private String meetingType;


    @ApiModelProperty(value = "会议时间")
    private String meetingTime;
    @ApiModelProperty(value = "会议地点")
    private String meetingPlace;


    // 因原呈批件已作废，故转发后的本件内容丢失！
    private String messgeInfo;

}
