package com.ctsi.biaodan.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ResSignFor extends BaseDtoEntity {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "部门id")
    private Long branchId;

    @ApiModelProperty(value = "部门名称")
    private String branchName;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 签收状态 0:未办 1:已阅
     * 0 未打开(未办)   1 打开过  2 已确认  <未确认需要动态计算,总数-确认数量>
     */
    private Integer signInStatus;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "签收时间")
    private LocalDateTime signInTime;

    @ApiModelProperty(value = "回复内容")
    private String documentReply;

    @ApiModelProperty(value = "催办时间")
    private LocalDateTime urgingTime;

    @ApiModelProperty(value = "终止传阅时间")
    private LocalDateTime terminationTime;


    @ApiModelProperty(value = "首次打开时间")
    private LocalDateTime firstOpenTime;

    /**
     * 回复文件列表
     */
    List<CscpEnclosureFile> fileList;

}
