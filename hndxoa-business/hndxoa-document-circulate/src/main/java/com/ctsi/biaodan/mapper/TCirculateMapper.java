package com.ctsi.biaodan.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.biaodan.entity.TCirculate;
import com.ctsi.biaodan.entity.dto.PageTcirculateDTO;
import com.ctsi.biaodan.entity.dto.QueryApprovalForwardRecordDTO;
import com.ctsi.biaodan.entity.dto.ResTcirculateUserDTO;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface TCirculateMapper extends MybatisBaseMapper<TCirculate> {

    /**
     * 查询已阅或未阅的数据(分页)
     *
     * @param page
     * @param entityDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<ResTcirculateUserDTO> queryCirculateUserPage(IPage<ResTcirculateUserDTO> page, @Param("param") PageTcirculateDTO entityDTO);


    /**
     * 查询已阅或未阅的数据(不分页)
     *
     * @param entityDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ResTcirculateUserDTO> queryCirculateUserList(@Param("param") PageTcirculateDTO entityDTO);

    /**
     * 查询我发起的传阅
     *
     * @param entityDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<TCirculate> selectCirculateList(IPage<TCirculate> page, @Param("param") PageTcirculateDTO entityDTO);

    /**
     * 公文回复
     *
     * @param page
     * @param entityDTO
     * @return
     */
    IPage<ResTcirculateUserDTO> queryReplyPage(IPage<ResTcirculateUserDTO> page, @Param("param") PageTcirculateDTO entityDTO);

    /**
     * 查询角标
     *
     * @param entityDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Long> querUnreadCirculateCount(@Param("param") PageTcirculateDTO entityDTO);

    /**
     * 查呈批件数据
     * @param fromDataId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
	Map<String, Object> selectCpjById(@Param("id")Long fromDataId);

    /**
     * 子呈批件查询父呈批件
     * @param processInstanceId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<QueryApprovalForwardRecordDTO> selectParentApproval(@Param("processInstanceId") String processInstanceId,
                                                            @Param("taskId") String taskId);
}
