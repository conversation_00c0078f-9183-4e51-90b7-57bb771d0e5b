package com.ctsi.biaodan.entity.dto;


import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PageTcirculateDTO extends BaseDtoEntity {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "开始时间")
    private String endTime;

    @ApiModelProperty(value = "传阅摘要")
    private String circulationSummary;

    @ApiModelProperty(value = "用户id(无需传入)")
    private Long userId;

    // 2023年12月15日 前端未使用该字段，后端已改用signInStatus判断签收状态
    @ApiModelProperty(value = "已阅和未阅(无需传入)")
    private Integer whetherRead;

    @ApiModelProperty(value = "签收状态 0:未办 1:已阅 2:已读未签收")
    private Integer signInStatus;

    @ApiModelProperty(value = "终止传阅（0：不终止 1：终止）")
    private Integer termination;

    /**
     * 状态  0-新建传阅 1-收文转传阅
     */
    @ApiModelProperty(value = "状态  0-新建传阅 1-收文转传阅")
    private Integer circulateStatus;

    @ApiModelProperty(value = "涉密类别code")
    private String securityClassificationCode;
}
