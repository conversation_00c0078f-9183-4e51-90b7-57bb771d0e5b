package com.ctsi.biaodan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 用户关注
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("notifi_count_history")
@ApiModel(value = "NotifiCountHistory对象", description = "用户关注")
public class NotifiCountHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知人信息")
    private String noticePeople;

    @ApiModelProperty("短信通知记录SW_SMS_NOTIFICATION_RECORDS表主键id")
    private Long pid;


}
