package com.ctsi.biaodan.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户关注
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
@Getter
@Setter
@ApiModel(value = "NotifiCountHistoryDTO对象", description = "用户关注")
public class NotifiCountHistoryDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通知人信息")
    private String noticePeople;

    @ApiModelProperty("短信通知记录SW_SMS_NOTIFICATION_RECORDS表主键id")
    private Long pid;

    private Long createBy;

    private String createName;

    private LocalDateTime createTime;

}
