CREATE TABLE `biz_cwmeeting_inbox` (
                                       `id` bigint NOT NULL COMMENT '主键ID',
                                       `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                       `title_id` bigint DEFAULT NULL COMMENT '公文ID',
                                       `meeting_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类型',
                                       `send_time` datetime DEFAULT NULL COMMENT '创建日期',
                                       `send_department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送部门名称',
                                       `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                       `sender` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发送人名称',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                       `receiver_id` bigint DEFAULT NULL COMMENT '收件人ID',
                                       `receiver_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收件人名称',
                                       `receiver_department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收件人部门名称',
                                       `receiver_mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '收件人联系电话',
                                       `check_time` datetime DEFAULT NULL COMMENT '查阅时间',
                                       `has_read` int DEFAULT NULL COMMENT '是否已阅，0：未阅，1已阅',
                                       `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                       `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                       `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                       `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                       `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                       `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会会议收集箱';

CREATE TABLE `biz_cwmeeting_material` (
                                          `id` bigint NOT NULL COMMENT '主键ID',
                                          `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                          `topic_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '议题名称',
                                          `approval` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否审批，1表示是，0表示否',
                                          `meet_start_time` varchar(30) DEFAULT NULL COMMENT '会议开始时间（年月日时分）',
                                          `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿单位',
                                          `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人部门名称',
                                          `create_date` date DEFAULT NULL COMMENT '拟稿时间（年-月-日）',
                                          `urgency` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缓急',
                                          `contents` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '正文',
                                          `annex` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                          `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                          `bpm_status` int DEFAULT NULL COMMENT '流程状态',
                                          `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人',
                                          `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                          `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                          `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                          `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                          `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                          `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                          `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                          `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会会议材料';

CREATE TABLE `biz_cwmeeting_notic` (
                                       `id` bigint NOT NULL COMMENT '主键ID',
                                       `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议标题',
                                       `urgency` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缓急：0加急，1平急，2特急（数据字典配置）',
                                       `approval` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否审批：1是，0否',
                                       `meet_serial_number` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议序号，下拉可选择已生成议程的序号，可自定义',
                                       `meet_start_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议开始时间（年月日时分）',
                                       `meet_place` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议地点（可查看会议室预定情况，自定义文本输入）',
                                       `topic_count` int DEFAULT NULL COMMENT '议题数量',
                                       `contact_people` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系人',
                                       `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
                                       `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿单位',
                                       `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人部门',
                                       `attendance_department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿列席部门',
                                       `meet_compere` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议主持人',
                                       `attendance_people` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '列席人员',
                                       `present_people` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '出席人员',
                                       `contents` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '正文（已生成议程的基本信息，或新建/上传的正文）',
                                       `annex` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否有附件，1表示是，0表示否',
                                       `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                       `bpm_status` int DEFAULT NULL COMMENT '流程状态',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                       `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                       `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                       `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                       `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                       `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                       `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会会议通知';

CREATE TABLE `biz_cwmeeting_screening` (
                                           `id` bigint NOT NULL COMMENT '主键ID',
                                           `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '议程标题',
                                           `meeting_date` date DEFAULT NULL COMMENT '会议时间',
                                           `subjects` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '选择议题',
                                           `create_by` bigint DEFAULT NULL COMMENT '拟稿人ID',
                                           `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人',
                                           `company_id` bigint DEFAULT NULL COMMENT '拟稿单位ID',
                                           `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿单位名称',
                                           `notic_choose` int DEFAULT '0' COMMENT '是否会议通知，1表示选择，其他不选择',
                                           `report_date` date DEFAULT NULL COMMENT '申报日期',
                                           `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人电话',
                                           `meet_serial_number` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议序号（限4位数，0001-9999）',
                                           `document` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否有正文',
                                           `duty_people` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '负责人',
                                           `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                           `bpm_status` int DEFAULT NULL COMMENT '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停',
                                           `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                           `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间（拟稿时间）',
                                           `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                           `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                           `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                           `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                           `department_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿部门名称',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会议题筛选';

CREATE TABLE `biz_cwmeeting_summary` (
                                         `id` bigint NOT NULL COMMENT '主键ID',
                                         `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
                                         `approval` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否审批：1表示是，0表示否',
                                         `meet_serial_number` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '会议序号，下拉可选择已生成议程的序号，可自定义',
                                         `company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿单位',
                                         `department_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人部门名称',
                                         `create_date` date DEFAULT NULL COMMENT '拟稿时间（年月日）',
                                         `urgency` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缓急',
                                         `contents` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '正文',
                                         `annex` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
                                         `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                         `bpm_status` int DEFAULT NULL COMMENT '流程状态',
                                         `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿人',
                                         `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                         `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                         `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                         `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                         `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                         `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `deleted` int DEFAULT '0' COMMENT '逻辑删除 0：未删除 1：删除',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会会议纪要';

CREATE TABLE `biz_cwmeeting_topic` (
                                       `id` bigint NOT NULL COMMENT '主键ID',
                                       `subject_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '议题名称',
                                       `report_company_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '汇报单位名称',
                                       `has_meeting_material` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '1表示有会议材料，0表示没有',
                                       `raise_subject_leader` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提出议题常委领导',
                                       `report_people_name` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '汇报人员的姓名，多个逗号隔开',
                                       `report_people_duty` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '汇报人职务',
                                       `cost_time_minute` int DEFAULT NULL COMMENT '议题所需时间',
                                       `contact_people` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系人',
                                       `mobile` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '联系电话',
                                       `report_date` date DEFAULT NULL COMMENT '申报日期',
                                       `major_problem` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '提请解决的主要问题',
                                       `is_approval` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否审批，1表示是，0表示否（默认否，不走审批流程，提交后直接进入议题库)',
                                       `is_news_declare` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '是否进行新闻报道，1表示是，0表示否（默认否)',
                                       `attend_leader` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟列席市领导',
                                       `attend_deapart_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟列席部门',
                                       `contents` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '正文',
                                       `process_instance_id` bigint DEFAULT NULL COMMENT '流程实例',
                                       `bpm_status` int DEFAULT NULL COMMENT '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停',
                                       `up_meet` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '0' COMMENT '0没有提交议题库,1提交议题库没有上会，2已上会',
                                       `submit_time` varchar(30) DEFAULT NULL COMMENT '提交到议题库的时间',
                                       `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
                                       `create_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
                                       `department_id` bigint DEFAULT NULL COMMENT '部门ID',
                                       `department_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拟稿部门名称',
                                       `company_id` bigint DEFAULT NULL COMMENT '单位ID',
                                       `tenant_id` bigint DEFAULT NULL COMMENT '租户ID',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建日期',
                                       `deleted` int DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
                                       `update_by` bigint DEFAULT NULL COMMENT '修改用户ID',
                                       `update_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改用户姓名',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='常委会议题申报';

