CREATE TABLE "myapp"."biz_cwmeeting_inbox"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(128),
"title_id" BIGINT,
"meeting_type" VARCHAR(32),
"send_time" TIMESTAMP(0),
"send_department_name" VARCHAR(32),
"process_instance_id" BIGINT,
"sender" VARCHAR(32),
"create_by" BIGINT,
"receiver_id" BIGINT,
"receiver_name" VARCHAR(32),
"has_read" INT,
"create_name" VARCHAR(32),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"up, date_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"deleted" INT DEFAULT 0,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_inbox" IS '常委会会议收集箱';COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."title" IS '标题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."title_id" IS '公文ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."meeting_type" IS '类型';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."send_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."send_department_name" IS '发送部门名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."sender" IS '发送人名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."receiver_id" IS '收件人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."receiver_name" IS '收件人名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."has_read" IS '是否已阅，0：未阅，1已阅';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_inbox"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."biz_cwmeeting_material"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(128),
"topic_name" VARCHAR(128),
"approval" VARCHAR(32),
"meet_start_time" VARCHAR(30),
"company_name" VARCHAR(32),
"department_name" VARCHAR(32),
"create_date" DATE,
"urgency" VARCHAR(32),
"contents" VARCHAR(200),
"annex" VARCHAR(200),
"process_instance_id" BIGINT,
"bpm_status" INT,
"create_name" VARCHAR(32),
"create_by" BIGINT,
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT, ,
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"deleted" INT DEFAULT 0,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_material" IS '常委会会议材料';COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."title" IS '标题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."topic_name" IS '议题名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."approval" IS '是否审批';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."meet_start_time" IS '会议开始时间（年月日时分）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."create_date" IS '拟稿时间（年-月-日）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."urgency" IS '缓急';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."contents" IS '正文';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."create_name" IS '拟稿人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_material"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."biz_cwmeeting_notic"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(128),
"urgency" VARCHAR(32),
"approval" VARCHAR(32),
"meet_serial_number" VARCHAR(16),
"meet_start_time" VARCHAR(30),
"meet_place" VARCHAR(255),
"topic_count" INT,
"contact_people" VARCHAR(32),
"mobile" VARCHAR(32),
"company_name" VARCHAR(32),
"department_name" VARCHAR(32),
"attendance_department_name" VARCHAR(32),
"meet_compere" VARCHAR(255),
"attendance_people" TEXT,
"present_people" TEXT,
"con, tents" VARCHAR(200) DEFAULT '0',
"annex" VARCHAR(1) DEFAULT '0',
"process_instance_id" BIGINT,
"bpm_status" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"deleted" INT DEFAULT 0,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_notic" IS '常委会会议通知';COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."title" IS '会议标题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."urgency" IS '缓急（数据字典配置）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."approval" IS '是否审批：是，否';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."meet_serial_number" IS '会议序号，下拉可选择已生成议程的序号，可自定义';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."meet_start_time" IS '会议开始时间（年月日时分）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."meet_place" IS '会议地点（可查看会议室预定情况，自定义文本输入）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."topic_count" IS '议题数量';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."contact_people" IS '联系人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."mobile" IS '联系电话';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."department_name" IS '拟稿人部门';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."attendance_department_name" IS '拟稿列席部门';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."meet_compere" IS '会议主持人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."attendance_people" IS '列席人员';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."present_people" IS '出席人员';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."contents" IS '正文（已生成议程的基本信息，或新建/上传的正文）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."annex" IS '是否有附件';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_notic"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."biz_cwmeeting_screening"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(128),
"meeting_date" DATE,
"subjects" VARCHAR(1000),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"company_id" BIGINT,
"company_name" VARCHAR(32),
"notic_choose" INT DEFAULT 0,
"mobile" VARCHAR(32),
"create_date" DATE,
"meet_serial_number" VARCHAR(16),
"document" VARCHAR(1) DEFAULT '0',
"duty_people" VARCHAR(32),
"process_instance_id" BIGINT,
"bpm_status" INT,
"department_id" BIGINT,
"tena, nt_id" BIGINT,
"create_time" TIMESTAMP(0),
"deleted" INT DEFAULT 0,
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_screening" IS '常委会议题筛选';COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."title" IS '议程标题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."meeting_date" IS '会议时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."subjects" IS '选择议题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."create_name" IS '拟稿人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."company_id" IS '拟稿单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."notic_choose" IS '是否会议通知，1表示选择，其他不选择';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."mobile" IS '拟稿人电话';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."create_date" IS '拟稿日期（年-月-日）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."meet_serial_number" IS '会议序号（限4位数，0001-9999）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."document" IS '是否有正文';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."duty_people" IS '负责人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."bpm_status" IS '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_screening"."update_time" IS '修改时间';




CREATE TABLE "myapp"."biz_cwmeeting_summary"
(
"id" BIGINT NOT NULL,
"title" VARCHAR(128),
"approval" VARCHAR(32),
"meet_serial_number" VARCHAR(16),
"company_name" VARCHAR(32),
"department_name" VARCHAR(32),
"create_date" DATE,
"urgency" VARCHAR(32),
"contents" VARCHAR(200),
"annex" VARCHAR(200),
"process_instance_id" BIGINT,
"bpm_status" INT,
"create_name" VARCHAR(32),
"create_by" BIGINT,
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_time" TIMESTAMP, (0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"deleted" INT DEFAULT 0,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_summary" IS '常委会会议纪要';COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."title" IS '标题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."approval" IS '是否审批';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."meet_serial_number" IS '会议序号，下拉可选择已生成议程的序号，可自定义';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."company_name" IS '拟稿单位';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."department_name" IS '拟稿人部门名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."create_date" IS '拟稿时间（年月日）';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."urgency" IS '缓急';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."contents" IS '正文';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."annex" IS '附件';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."create_name" IS '拟稿人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_summary"."deleted" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."biz_cwmeeting_topic"
(
"id" BIGINT NOT NULL,
"subject_name" VARCHAR(300),
"report_company_name" VARCHAR(32),
"has_meeting_material" VARCHAR(1) DEFAULT '0',
"raise_subject_leader" VARCHAR(32),
"report_people_name" VARCHAR(1000),
"report_people_duty" VARCHAR(1000),
"cost_time_minute" INT,
"contact_people" VARCHAR(32),
"mobile" VARCHAR(32),
"major_problem" VARCHAR(1000),
"is_approval" VARCHAR(1) DEFAULT '0',
"is_news_declare" VARCHAR(1) DEFAULT '0',
"attend_lead, er" VARCHAR(50),
"attend_deapart_name" VARCHAR(50),
"contents" VARCHAR(200) DEFAULT '0',
"process_instance_id" BIGINT,
"bpm_status" INT,
"up_meet" VARCHAR(1) DEFAULT '0',
"submit_time" VARCHAR(30),
"create_by" BIGINT,
"create_name" VARCHAR(32),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"create_time" TIMESTAMP(0),
"deleted" INT,
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUST, ERBTR) ;

COMMENT ON TABLE "myapp"."biz_cwmeeting_topic" IS '常委会议题申报';COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."id" IS '主键ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."subject_name" IS '议题名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."report_company_name" IS '汇报单位名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."has_meeting_material" IS '1表示有会议材料，0表示没有';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."raise_subject_leader" IS '提出议题常委领导';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."report_people_name" IS '汇报人员的姓名，多个逗号隔开';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."report_people_duty" IS '汇报人职务';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."cost_time_minute" IS '议题所需时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."contact_people" IS '联系人';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."mobile" IS '联系电话';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."major_problem" IS '提请解决的主要问题';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."is_approval" IS '是否审批，1表示是，0表示否（默认否，不走审批流程，提交后直接进入议题库)';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."is_news_declare" IS '是否进行新闻报道，1表示是，0表示否（默认否)';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."attend_leader" IS '拟列席市领导';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."attend_deapart_name" IS '拟列席部门';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."contents" IS '正文';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."bpm_status" IS '流程状态，1业务待启动流程，2流程处理中，3流程结束，4流程暂停';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."up_meet" IS '0没有提交议题库,1提交议题库没有上会，2已上会';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."submit_time" IS '提交到议题库的时间';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."create_name" IS '创建人名称';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."create_time" IS '创建日期';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "myapp"."biz_cwmeeting_topic"."update_time" IS '修改时间';




