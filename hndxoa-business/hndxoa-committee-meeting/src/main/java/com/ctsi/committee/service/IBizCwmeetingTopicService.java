package com.ctsi.committee.service;

import com.ctsi.committee.entity.dto.BizCwmeetingTopicDTO;
import com.ctsi.committee.entity.BizCwmeetingTopic;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 常委会议-议题申报 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
public interface IBizCwmeetingTopicService extends SysBaseServiceI<BizCwmeetingTopic> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizCwmeetingTopicDTO> queryListPage(BizCwmeetingTopicDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizCwmeetingTopicDTO> queryList(BizCwmeetingTopicDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizCwmeetingTopicDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizCwmeetingTopicDTO create(BizCwmeetingTopicDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizCwmeetingTopicDTO entity);

    /**
     * 议题上报
     *
     * @param id
     * @return
     */
    int submitTopic(Long id);

    /**
     * 上会
     * @param id
     * @return
     */
    int topicUpMeet(Long id);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizCwmeetingTopicId
     * @param code
     * @return
     */
    boolean existByBizCwmeetingTopicId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizCwmeetingTopicDTO> dataList);


}
