package com.ctsi.hndxoa.issues.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndxoa.issues.entity.BizPaperlessIssues;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface BizPaperlessIssuesMapper extends MybatisBaseMapper<BizPaperlessIssues> {

    Integer deleteIssues(Long id);

    @InterceptorIgnore(tenantLine = "true")
    List<CscpUserDTO> getuser(@Param("meetingsId") Long meetingsId , @Param("issuesId") Long issuesId,
                              @Param("reportPeople")Integer reportPeople);

    @Delete("  DELETE FROM  biz_paperless_issues WHERE  meetings_id= #{meetingsId} ")
    @InterceptorIgnore(tenantLine = "true")
    void deleteByMeetingId(@Param("meetingsId") Long meetingsId);
}
