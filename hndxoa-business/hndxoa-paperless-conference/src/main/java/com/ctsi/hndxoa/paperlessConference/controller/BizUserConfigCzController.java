package com.ctsi.hndxoa.paperlessConference.controller;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndxoa.paperlessConference.entity.BizUserConfigCz;
import com.ctsi.hndxoa.paperlessConference.entity.dto.BizUserConfigCzDTO;
import com.ctsi.hndxoa.paperlessConference.service.IBizUserConfigCzService;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizUserConfigCz")
@Api(value = "郴州会议签批app用户信息配置", tags = "郴州会议签批app用户信息配置接口")
public class BizUserConfigCzController extends BaseController {

    private static final String ENTITY_NAME = "bizUserConfigCz";

    @Autowired
    private IBizUserConfigCzService bizUserConfigCzService;



    /**
     *  新增郴州会议签批app用户信息配置批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizUserConfigCz.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增郴州会议签批app用户信息配置批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserConfigCz.add')")
    public ResultVO createBatch(@RequestBody List<BizUserConfigCzDTO> bizUserConfigCzList) {
       Boolean  result = bizUserConfigCzService.insertBatch(bizUserConfigCzList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizUserConfigCz.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增郴州会议签批app用户信息配置数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserConfigCz.add')")
    public ResultVO<BizUserConfigCzDTO> create(@RequestBody BizUserConfigCzDTO bizUserConfigCzDTO)  {
        BizUserConfigCzDTO result = bizUserConfigCzService.create(bizUserConfigCzDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizUserConfigCz.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新郴州会议签批app用户信息配置数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserConfigCz.update')")
    public ResultVO update(@RequestBody BizUserConfigCzDTO bizUserConfigCzDTO) {
	    Assert.notNull(bizUserConfigCzDTO.getId(), "general.IdNotNull");
        int count = bizUserConfigCzService.update(bizUserConfigCzDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除郴州会议签批app用户信息配置数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizUserConfigCz.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizUserConfigCz.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizUserConfigCzService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizUserConfigCzDTO bizUserConfigCzDTO = bizUserConfigCzService.findOne(id);
        return ResultVO.success(bizUserConfigCzDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizUserConfigCzPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizUserConfigCzDTO>> queryBizUserConfigCzPage(BizUserConfigCzDTO bizUserConfigCzDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizUserConfigCzService.queryListPage(bizUserConfigCzDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizUserConfigCz")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizUserConfigCzDTO>> queryBizUserConfigCz(BizUserConfigCzDTO bizUserConfigCzDTO) {
       List<BizUserConfigCzDTO> list = bizUserConfigCzService.queryList(bizUserConfigCzDTO);
       return ResultVO.success(new ResResult<BizUserConfigCzDTO>(list));
   }

   // -----------------用户信息配置的信息--------------------
    @GetMapping("/getOne")
    @ApiOperation(value = "查询用户的配置信息<app签批>", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<BizUserConfigCzDTO> getOne() {
        long userid = SecurityUtils.getCurrentUserId();
        BizUserConfigCzDTO bizUserConfigCzDTO = bizUserConfigCzService.findOneByUserid(userid);
        return ResultVO.success(bizUserConfigCzDTO);
    }


    /**
     *  更新存在数据.
     */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新郴州会议签批app用户信息配置数据")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizUserConfigCz.update')")
    public ResultVO saveOrUpdate(@RequestBody BizUserConfigCzDTO bizUserConfigCzDTO) {
        BizUserConfigCz bizUserConfigCz = BeanConvertUtils.copyProperties(bizUserConfigCzDTO , BizUserConfigCz.class);
        long userid = SecurityUtils.getCurrentUserId();
        if(bizUserConfigCz.getUserid() == null){
            bizUserConfigCz.setUserid(userid);
        }
        BizUserConfigCzDTO dto = bizUserConfigCzService.findOneByUserid(userid);
        if(dto.getId() != null ){
            bizUserConfigCz.setId(dto.getId());
        }
        bizUserConfigCzService.saveOrUpdate( bizUserConfigCz );
        return ResultVO.success(bizUserConfigCz);
    }

}
