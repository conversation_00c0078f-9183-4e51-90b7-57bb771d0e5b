package com.ctsi.hndxoa.issues.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.util.List;

import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.entity.dto.BizUserMeetingDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 议题
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizPaperlessIssuesDTO对象", description="")
public class BizPaperlessIssuesDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    @ApiModelProperty(value = "会议ID")
    private Long meetingsId;

    /**
     * 议题标题
     */
    @ApiModelProperty(value = "议题标题")
    private String title;

    /**
     * 汇报内容
     */
    @ApiModelProperty(value = "汇报内容")
    private String reportContent;

    /**
     * 汇报人
     */
    @ApiModelProperty(value = "汇报人")
    private String conferenceHost;

    /**
     * 汇报时长
     */
    @ApiModelProperty(value = "汇报时长")
    private String duration;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String departmentName;

    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    @ApiModelProperty(value = "议题人员ID")
    private String userid;

    @ApiModelProperty(value = "参与议题人员 0")
    private List<BizUserMeetingDTO> cscpUser;


    @ApiModelProperty(value = "汇报议题人员 1")
    private List<BizUserMeetingDTO> reportCscpUser;

    @ApiModelProperty(value = "附件集合")
    private List<BizMeetingFile> annexList;


    @ApiModelProperty(value = " 1: 在会议中 看议题所有内容; 0 :不在会议中,只能看到议题标题 ")
    private Integer inReport;




}
