package com.ctsi.hndxoa.among.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.service.IBizMeetingsIssuesUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 无纸化会议议题人员关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizMeetingsIssuesUser")
@Api(value = "", tags = "无纸化会议议题人员关联表")
public class BizMeetingsIssuesUserController extends BaseController {

    private static final String ENTITY_NAME = "bizMeetingsIssuesUser";

    @Autowired
    private IBizMeetingsIssuesUserService bizMeetingsIssuesUserService;



    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizMeetingsIssuesUser.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizMeetingsIssuesUser.add')")
    public ResultVO createBatch(@RequestBody List<BizMeetingsIssuesUserDTO> bizMeetingsIssuesUserList) {
       Boolean  result = bizMeetingsIssuesUserService.insertBatch(bizMeetingsIssuesUserList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizMeetingsIssuesUser.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizMeetingsIssuesUser.add')")
    public ResultVO<BizMeetingsIssuesUserDTO> create(@RequestBody BizMeetingsIssuesUserDTO bizMeetingsIssuesUserDTO)  {
        BizMeetingsIssuesUserDTO result = bizMeetingsIssuesUserService.create(bizMeetingsIssuesUserDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizMeetingsIssuesUser.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizMeetingsIssuesUser.update')")
    public ResultVO update(@RequestBody BizMeetingsIssuesUserDTO bizMeetingsIssuesUserDTO) {
	    Assert.notNull(bizMeetingsIssuesUserDTO.getId(), "general.IdNotNull");
        int count = bizMeetingsIssuesUserService.update(bizMeetingsIssuesUserDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizMeetingsIssuesUser.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizMeetingsIssuesUser.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizMeetingsIssuesUserService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizMeetingsIssuesUserDTO bizMeetingsIssuesUserDTO = bizMeetingsIssuesUserService.findOne(id);
        return ResultVO.success(bizMeetingsIssuesUserDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizMeetingsIssuesUserPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizMeetingsIssuesUserDTO>> queryBizMeetingsIssuesUserPage(BizMeetingsIssuesUserDTO bizMeetingsIssuesUserDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizMeetingsIssuesUserService.queryListPage(bizMeetingsIssuesUserDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizMeetingsIssuesUser")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizMeetingsIssuesUserDTO>> queryBizMeetingsIssuesUser(BizMeetingsIssuesUserDTO bizMeetingsIssuesUserDTO) {
       List<BizMeetingsIssuesUserDTO> list = bizMeetingsIssuesUserService.queryList(bizMeetingsIssuesUserDTO);
       return ResultVO.success(new ResResult<BizMeetingsIssuesUserDTO>(list));
   }

}
