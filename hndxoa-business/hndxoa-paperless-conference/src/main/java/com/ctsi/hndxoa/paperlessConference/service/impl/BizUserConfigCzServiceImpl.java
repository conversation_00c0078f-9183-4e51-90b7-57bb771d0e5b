package com.ctsi.hndxoa.paperlessConference.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndxoa.paperlessConference.entity.BizUserConfigCz;
import com.ctsi.hndxoa.paperlessConference.entity.dto.BizUserConfigCzDTO;
import com.ctsi.hndxoa.paperlessConference.mapper.BizUserConfigCzMapper;
import com.ctsi.hndxoa.paperlessConference.service.IBizUserConfigCzService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 郴州会议签批app用户信息配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Slf4j
@Service
public class BizUserConfigCzServiceImpl extends SysBaseServiceImpl<BizUserConfigCzMapper, BizUserConfigCz> implements IBizUserConfigCzService {

    @Autowired
    private BizUserConfigCzMapper bizUserConfigCzMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizUserConfigCzDTO> queryListPage(BizUserConfigCzDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizUserConfigCz> queryWrapper = new LambdaQueryWrapper();

        IPage<BizUserConfigCz> pageData = bizUserConfigCzMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizUserConfigCzDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizUserConfigCzDTO.class));

        return new PageResult<BizUserConfigCzDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizUserConfigCzDTO> queryList(BizUserConfigCzDTO entityDTO) {
        LambdaQueryWrapper<BizUserConfigCz> queryWrapper = new LambdaQueryWrapper();
            List<BizUserConfigCz> listData = bizUserConfigCzMapper.selectList(queryWrapper);
            List<BizUserConfigCzDTO> BizUserConfigCzDTOList = ListCopyUtil.copy(listData, BizUserConfigCzDTO.class);
        return BizUserConfigCzDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizUserConfigCzDTO findOne(Long id) {
        BizUserConfigCz  bizUserConfigCz =  bizUserConfigCzMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizUserConfigCz,BizUserConfigCzDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUserConfigCzDTO create(BizUserConfigCzDTO entityDTO) {
       BizUserConfigCz bizUserConfigCz =  BeanConvertUtils.copyProperties(entityDTO,BizUserConfigCz.class);
        save(bizUserConfigCz);
        return  BeanConvertUtils.copyProperties(bizUserConfigCz,BizUserConfigCzDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizUserConfigCzDTO entity) {
        BizUserConfigCz bizUserConfigCz = BeanConvertUtils.copyProperties(entity,BizUserConfigCz.class);
        return bizUserConfigCzMapper.updateById(bizUserConfigCz);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizUserConfigCzMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizUserConfigCzId
     * @return
     */
    @Override
    public boolean existByBizUserConfigCzId(Long BizUserConfigCzId) {
        if (BizUserConfigCzId != null) {
            LambdaQueryWrapper<BizUserConfigCz> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizUserConfigCz::getId, BizUserConfigCzId);
            List<BizUserConfigCz> result = bizUserConfigCzMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizUserConfigCzDTO> dataList) {
        List<BizUserConfigCz> result = ListCopyUtil.copy(dataList, BizUserConfigCz.class);
        return saveBatch(result);
    }

    @Override
    public BizUserConfigCzDTO findOneByUserid(long userid) {
        BizUserConfigCzDTO data = new BizUserConfigCzDTO();
        LambdaQueryWrapper<BizUserConfigCz> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizUserConfigCz::getUserid,userid);
        List<BizUserConfigCz> res = bizUserConfigCzMapper.selectListNoAdd(wrapper);
        if(res != null && res.size()>0 ){
            data= BeanConvertUtils.copyProperties(res.get(0),BizUserConfigCzDTO.class);
        }
        return data;
    }


}
