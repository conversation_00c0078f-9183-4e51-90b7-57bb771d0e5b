package com.ctsi.hndxoa.paperlessConference.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 郴州会议签批app用户信息配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_user_config_cz")
@ApiModel(value="BizUserConfigCz对象", description="郴州会议签批app用户信息配置")
public class BizUserConfigCz extends BaseEntity {

    private static final long serialVersionUID = 1L;



    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userid;

    /**
     * 笔颜色
     */
    @ApiModelProperty(value = "笔颜色")
    private Integer penColor;

    /**
     * 阅读模式
     */
    @ApiModelProperty(value = "阅读模式")
    private Integer readMode;

    /**
     * 笔的粗细
     */
    @ApiModelProperty(value = "笔的粗细")
    private Float penThick;

    /**
     * 是否是电子笔
     */
    @ApiModelProperty(value = "是否是电子笔 0 1 值")
    private Integer isPenInput;


}
