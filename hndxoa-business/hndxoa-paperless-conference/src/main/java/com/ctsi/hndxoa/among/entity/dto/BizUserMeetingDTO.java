package com.ctsi.hndxoa.among.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * <AUTHOR> Generator
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户的实体DTO")
public class BizUserMeetingDTO extends BaseDtoEntity implements Serializable {
    /**
     * 用户的默认排序号
     */
    public static final Integer DEFAULT_USER_SORT = 999;

    private static final long serialVersionUID = 3169182298684310412L;

    /**
     * 单位id,登录时所用的单位的id
     */


    @ApiModelProperty(value = "租户ID", readOnly = true)
    private Long tenantId;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;
    private String realName;

    private Integer sort;
    private Integer orderBy;


    // ---------- 以下必填------------

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "张三")
    private String userName;

    @ApiModelProperty(value = "登录时所用的单位的ID", readOnly = true)
    private Long companyId;

    @ApiModelProperty(value = "登录时所使用的部门名称", readOnly = true)
    private String departmentName;
    @ApiModelProperty(value = "登录时所使用的部门ID", readOnly = true)
    private Long departmentId;

    @ApiModelProperty(value = "用户手机号码")
    // @Pattern(regexp = SysRegEx.MOBILE_PATTERN, message = "手机号格式有误")
    private String mobile;

    @ApiModelProperty("职务")
    private String post;

    // ---------- 必填------------




}