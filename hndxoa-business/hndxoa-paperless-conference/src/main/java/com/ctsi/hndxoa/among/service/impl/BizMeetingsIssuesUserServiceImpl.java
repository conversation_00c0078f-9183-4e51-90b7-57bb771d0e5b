package com.ctsi.hndxoa.among.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.mapper.BizMeetingsIssuesUserMapper;
import com.ctsi.hndxoa.among.service.IBizMeetingsIssuesUserService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Slf4j
@Service
public class BizMeetingsIssuesUserServiceImpl extends SysBaseServiceImpl<BizMeetingsIssuesUserMapper, BizMeetingsIssuesUser> implements IBizMeetingsIssuesUserService {

    @Autowired
    private BizMeetingsIssuesUserMapper bizMeetingsIssuesUserMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizMeetingsIssuesUserDTO> queryListPage(BizMeetingsIssuesUserDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapper = new LambdaQueryWrapper();

        IPage<BizMeetingsIssuesUser> pageData = bizMeetingsIssuesUserMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizMeetingsIssuesUserDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizMeetingsIssuesUserDTO.class));

        return new PageResult<BizMeetingsIssuesUserDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizMeetingsIssuesUserDTO> queryList(BizMeetingsIssuesUserDTO entityDTO) {
        LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapper = new LambdaQueryWrapper();
            List<BizMeetingsIssuesUser> listData = bizMeetingsIssuesUserMapper.selectList(queryWrapper);
            List<BizMeetingsIssuesUserDTO> BizMeetingsIssuesUserDTOList = ListCopyUtil.copy(listData, BizMeetingsIssuesUserDTO.class);
        return BizMeetingsIssuesUserDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizMeetingsIssuesUserDTO findOne(Long id) {
        BizMeetingsIssuesUser  bizMeetingsIssuesUser =  bizMeetingsIssuesUserMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizMeetingsIssuesUser,BizMeetingsIssuesUserDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizMeetingsIssuesUserDTO create(BizMeetingsIssuesUserDTO entityDTO) {
       BizMeetingsIssuesUser bizMeetingsIssuesUser =  BeanConvertUtils.copyProperties(entityDTO,BizMeetingsIssuesUser.class);
        save(bizMeetingsIssuesUser);
        return  BeanConvertUtils.copyProperties(bizMeetingsIssuesUser,BizMeetingsIssuesUserDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizMeetingsIssuesUserDTO entity) {
        BizMeetingsIssuesUser bizMeetingsIssuesUser = BeanConvertUtils.copyProperties(entity,BizMeetingsIssuesUser.class);
        return bizMeetingsIssuesUserMapper.updateById(bizMeetingsIssuesUser);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizMeetingsIssuesUserMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizMeetingsIssuesUserId
     * @return
     */
    @Override
    public boolean existByBizMeetingsIssuesUserId(Long BizMeetingsIssuesUserId) {
        if (BizMeetingsIssuesUserId != null) {
            LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizMeetingsIssuesUser::getId, BizMeetingsIssuesUserId);
            List<BizMeetingsIssuesUser> result = bizMeetingsIssuesUserMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizMeetingsIssuesUserDTO> dataList) {
        List<BizMeetingsIssuesUser> result = ListCopyUtil.copy(dataList, BizMeetingsIssuesUser.class);
        return saveBatch(result);
    }


}
