package com.ctsi.hndxoa.issues.entity.dto;

import com.ctsi.hndx.filestore.CscpFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BizMeetingFile extends CscpFile implements Serializable {

    /**
     * 表单项id
     */
    @ApiModelProperty(value = "表单项id")
    private String formItemId;


    @ApiModelProperty(value = "用来区分不同的业务")
    private String formBusiness;


    /**
     * 表单数据id
     */
    @ApiModelProperty(value = "表单数据id业务关联业务id")
    private Long formDataId;


    @ApiModelProperty(value = "流程办理附件时候的任务id")
    private Long taskId;

    @ApiModelProperty(value = "好签id,上传好签签批文件后的id")
    private String signid;
}