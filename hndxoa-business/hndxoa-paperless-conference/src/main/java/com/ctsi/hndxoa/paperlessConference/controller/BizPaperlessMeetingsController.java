package com.ctsi.hndxoa.paperlessConference.controller;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ctsi.hndx.annotations.LimitSubmit;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndxoa.paperlessConference.entity.BizPaperlessMeetings;
import com.ctsi.hndxoa.paperlessConference.entity.dto.UserImportDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ctsi.hndxoa.paperlessConference.entity.dto.BizPaperlessMeetingsDTO;
import com.ctsi.hndxoa.paperlessConference.service.IBizPaperlessMeetingsService;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.RedisUtil;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;
import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 无纸化会议
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizPaperlessMeetings")
@Api(value = "", tags = "会议接口")
public class BizPaperlessMeetingsController extends BaseController {

    private static final String ENTITY_NAME = "bizPaperlessMeetings";

    @Autowired
    private IBizPaperlessMeetingsService bizPaperlessMeetingsService;
    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private RedisUtil redisUtil;




    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizPaperlessMeetings.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")

    public ResultVO createBatch(@RequestBody List<BizPaperlessMeetingsDTO> bizPaperlessMeetingsList) {
       Boolean  result = bizPaperlessMeetingsService.insertBatch(bizPaperlessMeetingsList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizPaperlessMeetings.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
    @LimitSubmit(key = "bizPaperlessMeetingsControllerCreate:%s",limit = 5,needAllWait = true)
    public ResultVO<BizPaperlessMeetingsDTO> create(@RequestBody BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO)  {
        BizPaperlessMeetingsDTO result = bizPaperlessMeetingsService.create(bizPaperlessMeetingsDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改会议", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    public ResultVO update(@RequestBody BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO) {
	    Assert.notNull(bizPaperlessMeetingsDTO.getId(), "general.IdNotNull");
        int count = bizPaperlessMeetingsService.update(bizPaperlessMeetingsDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @GetMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizPaperlessMeetings.delete)", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizPaperlessMeetingsService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO = bizPaperlessMeetingsService.findOne(id);
        return ResultVO.success(bizPaperlessMeetingsDTO);
    }



   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizPaperlessMeetings")
   @ApiOperation(value = "查询列表数据", notes = "传入参数")
   public ResultVO<ResResult<BizPaperlessMeetingsDTO>> queryBizPaperlessMeetings(BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO) {
       List<BizPaperlessMeetingsDTO> list = bizPaperlessMeetingsService.queryList(bizPaperlessMeetingsDTO);
       return ResultVO.success(new ResResult<BizPaperlessMeetingsDTO>(list));
   }

    /**
     * id 为会议议程的主键的id ，由前端雪花算法生产
     * @param id
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadUser/{id}")
    @ApiOperation(value = "用户导入接口", notes = "传入参数单位ID")
    @OperationLog(dBOperation = DBOperation.ADD,message = "用户导入")
    public ResultVO uploadUser(@PathVariable Long id, MultipartFile file) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        EasyExcel.read(file.getInputStream(), UserImportDTO.class, new PageReadListener<UserImportDTO>( dataList -> {
            bizPaperlessMeetingsService.saveUsers(id, dataList);
        })).sheet().doRead();
        String jsonStr = (String) redisUtil.get(id.toString());
        List<UserImportDTO> userImportDTOS = JSON.parseArray(jsonStr , UserImportDTO.class);
        return ResultVO.success(userImportDTOS);
    }

    @GetMapping("/selectUser/{id}")
    @ApiOperation(value = "查看导入人员", notes = "传入参数登录用户ID")
    @OperationLog(dBOperation = DBOperation.ADD,message = "用户导入")
    public ResultVO selectUser(@PathVariable Long id) throws IOException {
        String getre = String.valueOf(redisUtil.get(id.toString()));
        List<UserImportDTO> resultList = JSONArray.parseArray(getre, UserImportDTO.class);
        return ResultVO.success(resultList);
    }

    /**
     * app查询
     *  分页查询多条数据.
     *  会议状态 0:未开始 1:进行中 2:已结束
     */
    @GetMapping("/appQueryBizPaperlessMeetingsPage/{meetingStatus}")
    @ApiOperation(value = "查看会议列表分页", notes = "会议状态<meetingStatus> 0:未开始 1:进行中 2:已结束")
    public ResultVO<PageResult<BizPaperlessMeetingsDTO>> appQueryBizPaperlessMeetingsPage(@PathVariable Integer meetingStatus,String searchKey, BasePageForm basePageForm) {
        return ResultVO.success(bizPaperlessMeetingsService.appQueryListPage(meetingStatus,searchKey, basePageForm));
    }


    /**
     *  分页查询多条数据.PC
     *  根据创建人查询
     */
    @GetMapping("/queryBizPaperlessMeetingsPage")
    @ApiOperation(value = "分页查询多条数据.PC查看会议列表分页", notes = "传入参数")
    public ResultVO<PageResult<BizPaperlessMeetingsDTO>> queryBizPaperlessMeetingsPage(BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO, BasePageForm basePageForm) {
        // pc不做创建人过滤 默认可以看所有人创建的 会议
        // bizPaperlessMeetingsDTO.setCreateBy(SecurityUtils.getCurrentUserId());
        return ResultVO.success(bizPaperlessMeetingsService.queryListPage(bizPaperlessMeetingsDTO, basePageForm));
    }

    // 1 查会议议程 根据会议id  带附件信息

    // 2 汇报材料需要带会议标题等基本信息
    /**
     *  结束会议.
     */
    @PostMapping("/finishMeeting")
    @ApiOperation(value = "结束会议", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    public ResultVO finishMeeting(@RequestBody BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO) {
        Assert.notNull(bizPaperlessMeetingsDTO.getId(), "general.IdNotNull");
        BizPaperlessMeetings bizPaperlessMeetings = new BizPaperlessMeetings();
        bizPaperlessMeetings.setId(bizPaperlessMeetingsDTO.getId());
        bizPaperlessMeetings.setMeetingStatus(2);
        if(bizPaperlessMeetingsService.updateById(bizPaperlessMeetings) ){
            // 删除缓存数据
            redisUtil.del(bizPaperlessMeetingsDTO.getId().toString());
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    @GetMapping("/autoFinishMeeting")
    @ApiOperation(value = "定时 结束任务", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO autoFinishMeeting() {
         bizPaperlessMeetingsService.autoFinishMeeting();
        return ResultVO.success();
    }

    @GetMapping("/searchUser/{id}")
    @ApiOperation(value = "选择用户搜索接口", notes = "id当前生产的雪花id,搜索字段是realName")
    public ResultVO searchUser(@PathVariable Long id  ,UserImportDTO dto ) throws IOException {
        String getre = String.valueOf(redisUtil.get(id.toString()));
        List<UserImportDTO> resultList = JSONArray.parseArray(getre, UserImportDTO.class);
        // 从缓存中搜索
        String realName = dto.getRealName(); // 模糊匹配人
        if(StrUtil.isNotEmpty(realName) && null != resultList ){
            resultList = resultList.stream().filter(i -> {
                if (i.getRealName().contains(realName)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        return ResultVO.success(resultList);
    }


}
