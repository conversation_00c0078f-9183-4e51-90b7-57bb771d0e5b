package com.ctsi.hndxoa.paperlessConference.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.entity.dto.BizUserMeetingDTO;
import com.ctsi.hndxoa.issues.entity.dto.BizPaperlessIssuesDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 会议
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizPaperlessMeetingsDTO对象", description="")
public class BizPaperlessMeetingsDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "会议标题")
    private String title;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String departmentName;

    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    private String meetingVenue;

    /**
     * 会议开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "会议开始时间")
    private LocalDate meetStartTimeQuery;

    /**
     * 会议结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "会议结束时间")
    private LocalDate meetEndTimeQuery;

    /**
     * 列席人员
     */
    @ApiModelProperty(value = "列席人员")
    private String attendees;


    /**
     * 主持人
     */
    @ApiModelProperty(value = "会议主持人姓名")
    private String conferenceHostName;


    /**
     * 主持人
     */
    @ApiModelProperty(value = "会议主持人的id")
    private Long conferenceHostId;

    @ApiModelProperty(value = "会议主持人员的基础信息集合")
    private BizUserMeetingDTO conferenceHostDTO;

    /**
     * 会议状态 0:未开始 1:进行中 2:已结束
     */
    @ApiModelProperty(value = "会议状态 0:未开始 1:进行中 2:已结束")
    private Integer meetingStatus;

    @ApiModelProperty(value = "议题内容")
    private List<BizPaperlessIssuesDTO> bizPaperlessIssuesDTO;

    @ApiModelProperty(value = "会议参会人员")
    private List<BizUserMeetingDTO> cscpUserDTO;

    //议题数
    @TableField(exist = false)
    private Integer ytsum;

    //参会人数
    @ApiModelProperty(value = "参会人数")
    private Integer chsum;


    // 查询条件
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;
    @ApiModelProperty(value = "创建人")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime meetEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetStartTime;

}
