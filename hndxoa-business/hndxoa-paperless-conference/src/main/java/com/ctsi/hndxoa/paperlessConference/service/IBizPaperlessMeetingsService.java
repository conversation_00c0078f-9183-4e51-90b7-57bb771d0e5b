package com.ctsi.hndxoa.paperlessConference.service;

import com.ctsi.hndxoa.paperlessConference.entity.dto.BizPaperlessMeetingsDTO;
import com.ctsi.hndxoa.paperlessConference.entity.BizPaperlessMeetings;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndxoa.paperlessConference.entity.dto.UserImportDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface IBizPaperlessMeetingsService extends SysBaseServiceI<BizPaperlessMeetings> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizPaperlessMeetingsDTO> queryListPage(BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizPaperlessMeetingsDTO> queryList(BizPaperlessMeetingsDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizPaperlessMeetingsDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizPaperlessMeetingsDTO create(BizPaperlessMeetingsDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizPaperlessMeetingsDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizPaperlessMeetingsId
     * @param code
     * @return
     */
    boolean existByBizPaperlessMeetingsId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizPaperlessMeetingsDTO> dataList);

    /**
     * 保存导入的用户数据
     * @param id
     * @param dataList
     * @return
     */
    List<UserImportDTO> saveUsers(Long id, List<UserImportDTO> dataList);

    /**
     * app 查询 会议列表
     * @param meetingStatus
     * @param basePageForm
     * @return
     */

    PageResult<BizPaperlessMeetingsDTO> appQueryListPage(Integer meetingStatus ,String searchKey,
                                                         BasePageForm basePageForm);

    /**
     * 定时任务自动结束会议
     */
    void autoFinishMeeting();
}
