package com.ctsi.hndxoa.among.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  无纸化会议议题人员关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizMeetingsIssuesUserDTO对象", description="")
public class BizMeetingsIssuesUserDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    @ApiModelProperty(value = "会议ID")
    private Long meetingsId;

    /**
     * 议题ID
     */
    @ApiModelProperty(value = "议题ID")
    private Long issuesId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户导入进去的单位名称，预留，职务表里面已经字段了")
    // private String realCompanyName;
    private String departmentName;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户导入进去的单位名称")
    private String post;

    @ApiModelProperty(value = " 0 表示参会人员 ;1表示汇报人员 ; 2 表示会议主持人")
    private int reportPeople;


}
