package com.ctsi.hndxoa.issues.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndxoa.among.mapper.BizMeetingsIssuesUserMapper;
import com.ctsi.hndxoa.issues.entity.dto.BizMeetingFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndxoa.issues.entity.BizPaperlessIssues;
import com.ctsi.hndxoa.issues.entity.dto.BizPaperlessIssuesDTO;
import com.ctsi.hndxoa.issues.mapper.BizPaperlessIssuesMapper;
import com.ctsi.hndxoa.issues.service.IBizPaperlessIssuesService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Slf4j
@Service
public class BizPaperlessIssuesServiceImpl extends SysBaseServiceImpl<BizPaperlessIssuesMapper, BizPaperlessIssues> implements IBizPaperlessIssuesService {

    @Autowired
    private BizPaperlessIssuesMapper bizPaperlessIssuesMapper;

    @Autowired
    private com.ctsi.operation.mapper.CscpEnclosureFileMapper cscpEnclosureFileMapper;

    @Autowired
    private BizMeetingsIssuesUserMapper bizMeetingsIssuesUserMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPaperlessIssuesDTO> queryListPage(BizPaperlessIssuesDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizPaperlessIssues> queryWrapper = new LambdaQueryWrapper();

        IPage<BizPaperlessIssues> pageData = bizPaperlessIssuesMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizPaperlessIssuesDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPaperlessIssuesDTO.class));

        return new PageResult<BizPaperlessIssuesDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *  需要组装附件信息
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizPaperlessIssuesDTO> queryList(BizPaperlessIssuesDTO entityDTO) {
        LambdaQueryWrapper<BizPaperlessIssues> queryWrapper = new LambdaQueryWrapper();
            List<BizPaperlessIssues> listData = bizPaperlessIssuesMapper.selectList(queryWrapper);
            List<BizPaperlessIssuesDTO> BizPaperlessIssuesDTOList = ListCopyUtil.copy(listData, BizPaperlessIssuesDTO.class);
        return BizPaperlessIssuesDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizPaperlessIssuesDTO findOne(Long id) {
        BizPaperlessIssues  bizPaperlessIssues =  bizPaperlessIssuesMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizPaperlessIssues,BizPaperlessIssuesDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizPaperlessIssuesDTO create(BizPaperlessIssuesDTO entityDTO) {
       BizPaperlessIssues bizPaperlessIssues =  BeanConvertUtils.copyProperties(entityDTO,BizPaperlessIssues.class);
        save(bizPaperlessIssues);
        return  BeanConvertUtils.copyProperties(bizPaperlessIssues,BizPaperlessIssuesDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizPaperlessIssuesDTO entity) {
        BizPaperlessIssues bizPaperlessIssues = BeanConvertUtils.copyProperties(entity,BizPaperlessIssues.class);
        return bizPaperlessIssuesMapper.updateById(bizPaperlessIssues);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizPaperlessIssuesMapper.deleteById(id);
    }


    @Override
    public int deleteByMeetingId(Long id) {
        //删除 用户表信息
        LambdaQueryWrapper<BizMeetingsIssuesUser> meetingsIssuesUserQW = new LambdaQueryWrapper();
        meetingsIssuesUserQW.eq(BizMeetingsIssuesUser :: getMeetingsId, id);
        List<Long> bizMeetingsIssuesUsers =
                bizMeetingsIssuesUserMapper.selectListNoAdd(meetingsIssuesUserQW).stream().map(i -> i.getId()).collect(Collectors.toList());
        bizMeetingsIssuesUserMapper.deleteBatchIds(bizMeetingsIssuesUsers);

        // 删除附件表信息
        LambdaQueryWrapper<CscpEnclosureFile> wrapper  = new LambdaQueryWrapper<>();
        wrapper.in(CscpEnclosureFile::getFormDataId,bizMeetingsIssuesUsers);
        List<Long> fileIds =
                cscpEnclosureFileMapper.selectListNoAdd(wrapper).stream().map(i -> i.getId()).collect(Collectors.toList());
        if( fileIds!=null && !fileIds.isEmpty()){
            cscpEnclosureFileMapper.deleteBatchIds(fileIds);
        }

        // 删除 议题表 <需要物理删除>
        bizPaperlessIssuesMapper.deleteByMeetingId(id); // 删除议题
        return 0;
    }

    /**
     * 验证是否存在
     *
     * @param BizPaperlessIssuesId
     * @return
     */
    @Override
    public boolean existByBizPaperlessIssuesId(Long BizPaperlessIssuesId) {
        if (BizPaperlessIssuesId != null) {
            LambdaQueryWrapper<BizPaperlessIssues> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizPaperlessIssues::getId, BizPaperlessIssuesId);
            List<BizPaperlessIssues> result = bizPaperlessIssuesMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizPaperlessIssuesDTO> dataList) {
        List<BizPaperlessIssues> result = ListCopyUtil.copy(dataList, BizPaperlessIssues.class);
        return saveBatch(result);
    }

    @Override
    public List<BizPaperlessIssuesDTO> queryListAndAnnex(BizPaperlessIssuesDTO bizPaperlessIssuesDTO) {
        Long meetingsId = bizPaperlessIssuesDTO.getMeetingsId();
        LambdaQueryWrapper<BizPaperlessIssues> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizPaperlessIssues::getMeetingsId,meetingsId).orderByAsc(BizPaperlessIssues::getSort);
        List<BizPaperlessIssues> listData = bizPaperlessIssuesMapper.selectListNoAdd(queryWrapper);
        List<BizPaperlessIssuesDTO> BizPaperlessIssuesDTOList = ListCopyUtil.copy(listData, BizPaperlessIssuesDTO.class);

        // 过滤掉 不能看到的 议题 ; 增加标记 只能看到标题
        long currentUserId = SecurityUtils.getCurrentUserId();
        // 获取能看到的 议题id
        LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapper2 = new LambdaQueryWrapper();
        queryWrapper2.eq(BizMeetingsIssuesUser::getUserId,currentUserId)
                .eq(BizMeetingsIssuesUser::getMeetingsId,meetingsId);

        List<Long> issuesId =
                bizMeetingsIssuesUserMapper.selectListNoAdd(queryWrapper2).stream().map(i -> i.getIssuesId()).collect(Collectors.toList());
        BizPaperlessIssuesDTOList.forEach(i ->{
            Long id = i.getId();
            if(issuesId.contains(id)){  // 在会议中 能看的
                i.setInReport(1);
                // 增加附件信息
                // 由议题-用户表数据id 获得附件信息
                LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapper3 = new LambdaQueryWrapper<>();
                queryWrapper3.eq(BizMeetingsIssuesUser::getIssuesId,id).eq(BizMeetingsIssuesUser::getUserId,currentUserId);
                List<BizMeetingsIssuesUser> bizMeetingsIssuesUsers = bizMeetingsIssuesUserMapper.selectListNoAdd(queryWrapper3);

                if(!bizMeetingsIssuesUsers.isEmpty()){
                    BizMeetingsIssuesUser bizMeetingsIssuesUser = bizMeetingsIssuesUsers.get(0);
                    LambdaQueryWrapper<CscpEnclosureFile> wrapper =
                            new LambdaQueryWrapper();
                    wrapper.eq(CscpEnclosureFile::getFormDataId,bizMeetingsIssuesUser.getId());
                    List<CscpEnclosureFile> cscpEnclosureFiles = cscpEnclosureFileMapper.selectListNoAdd(wrapper);
                    i.setAnnexList(ListCopyUtil.copy(cscpEnclosureFiles , BizMeetingFile.class));

                }

                LambdaQueryWrapper<BizMeetingsIssuesUser> queryWrapperHbr = new LambdaQueryWrapper<>();
                queryWrapperHbr.eq(BizMeetingsIssuesUser::getIssuesId,id).eq(BizMeetingsIssuesUser::getReportPeople,1);
                List<BizMeetingsIssuesUser> hbrUser = bizMeetingsIssuesUserMapper.selectListNoAdd(queryWrapperHbr);
                String hbr =
                        hbrUser.stream().map(k -> k.getDepartmentName() + k.getPost() + k.getUserName()).collect(Collectors.joining(","));
                i.setConferenceHost(hbr);
                // 单位 + 职务 + 姓名
                // i.setConferenceHost();
            }else {
                i.setInReport(0);
                // 汇报内容置空
                i.setReportContent("");
            }
        });
        // 不能看到的 过滤掉
        List<BizPaperlessIssuesDTO> result =
                BizPaperlessIssuesDTOList.stream().filter(i -> i.getInReport() == 1).collect(Collectors.toList());
        return result;
    }



}
