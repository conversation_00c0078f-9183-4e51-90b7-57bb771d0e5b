package com.ctsi.hndxoa.among.service;

import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface IBizMeetingsIssuesUserService extends SysBaseServiceI<BizMeetingsIssuesUser> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizMeetingsIssuesUserDTO> queryListPage(BizMeetingsIssuesUserDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizMeetingsIssuesUserDTO> queryList(BizMeetingsIssuesUserDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizMeetingsIssuesUserDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizMeetingsIssuesUserDTO create(BizMeetingsIssuesUserDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizMeetingsIssuesUserDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizMeetingsIssuesUserId
     * @param code
     * @return
     */
    boolean existByBizMeetingsIssuesUserId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizMeetingsIssuesUserDTO> dataList);


}
