package com.ctsi.hndxoa.paperlessConference.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndxoa.paperlessConference.entity.BizPaperlessMeetings;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndxoa.paperlessConference.entity.dto.BizPaperlessMeetingsDTO;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface BizPaperlessMeetingsMapper extends MybatisBaseMapper<BizPaperlessMeetings> {

    /**
     * app查询会议
     * @param meetingStatus
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BizPaperlessMeetingsDTO> appQueryListPage(@Param("meetingStatus") Integer meetingStatus ,
                                                   @Param("searchKey") String searchKey,
                                                   @Param("userId") Long userId);
}
