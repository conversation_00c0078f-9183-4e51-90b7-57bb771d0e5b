package com.ctsi.hndxoa.paperlessConference.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ctsi.hndx.excel.ExcelCustomStringTrimConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname UserDTO
 * @Description
 * @Date 2021/12/29/0029 15:25
 */
@Data
@ApiModel(value="用户导入模板", description="用户导入模板")
public class UserImportDTO implements Serializable {

    private static final long serialVersionUID = 6834936645058754443L;

    @ApiModelProperty(value = "id")
    //@ExcelProperty("用户名")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "用户名")
    //@ExcelProperty("用户名")
    @ExcelIgnore
    private String userName;

    @ApiModelProperty(value = "列席人员姓名（包括所有参会人员、汇报人、会议主持）")
    @ExcelProperty(value = "列席人员姓名（包括所有参会人员、汇报人、会议主持）",converter = ExcelCustomStringTrimConverter.class)
    private String realName;

    @ApiModelProperty(value = "手机号码")
    @ExcelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位", converter = ExcelCustomStringTrimConverter.class)
    private String departmentName;

    @ApiModelProperty(value = "职务")
    @ExcelProperty(value = "职务", converter = ExcelCustomStringTrimConverter.class)
    private String post;

    @ApiModelProperty(value = "用户部门中间表排序号")
    @ExcelProperty(value = "部门排序(多部门使用,隔开)")
    private String sort;

    @ApiModelProperty(value = "用户表排序号")
    @ExcelProperty(value = "全局排序")
    private String orderBy;

    @ApiModelProperty("是否为部门领导")
    @ExcelProperty(value = "是否为部门领导", converter = ExcelCustomStringTrimConverter.class)
    private String departmentHead;

    @ApiModelProperty(value = "办公电话")
    @ExcelProperty(value = "办公电话")
    private String officePhone;


    @ApiModelProperty(value = "部门ID")
    @ExcelIgnore
    private Long departmentId;

    @ApiModelProperty(value = "单位ID")
    @ExcelIgnore
    private Long companyId;

    @ApiModelProperty(value = "租户ID")
    @ExcelIgnore
    private Long tenantId;

    @ApiModelProperty(value = "是否统计")
    @ExcelProperty(value = "是否统计", converter = ExcelCustomStringTrimConverter.class)
    private String statistics;

    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    private String failedReason;

}
