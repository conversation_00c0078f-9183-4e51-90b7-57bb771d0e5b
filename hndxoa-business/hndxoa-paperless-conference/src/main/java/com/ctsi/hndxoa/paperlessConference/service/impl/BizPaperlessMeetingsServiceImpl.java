package com.ctsi.hndxoa.paperlessConference.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.dto.QueryListPageDTO;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndxoa.among.entity.BizMeetingsIssuesUser;
import com.ctsi.hndxoa.among.entity.dto.BizMeetingsIssuesUserDTO;
import com.ctsi.hndxoa.among.entity.dto.BizUserMeetingDTO;
import com.ctsi.hndxoa.among.mapper.BizMeetingsIssuesUserMapper;
import com.ctsi.hndxoa.among.service.IBizMeetingsIssuesUserService;
import com.ctsi.hndxoa.issues.entity.BizPaperlessIssues;
import com.ctsi.hndxoa.issues.entity.dto.BizPaperlessIssuesDTO;
import com.ctsi.hndxoa.issues.mapper.BizPaperlessIssuesMapper;
import com.ctsi.hndxoa.issues.service.IBizPaperlessIssuesService;
import com.ctsi.hndxoa.issues.service.impl.BizPaperlessIssuesServiceImpl;
import com.ctsi.hndxoa.paperlessConference.entity.dto.UserImportDTO;
import com.ctsi.hndxoa.util.BizMeetingUtil;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.operation.service.CscpEnclosureFileService;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgNameIdListDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.admin.service.OrgImportAndExportService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndxoa.paperlessConference.entity.BizPaperlessMeetings;
import com.ctsi.hndxoa.paperlessConference.entity.dto.BizPaperlessMeetingsDTO;
import com.ctsi.hndxoa.paperlessConference.mapper.BizPaperlessMeetingsMapper;
import com.ctsi.hndxoa.paperlessConference.service.IBizPaperlessMeetingsService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import com.google.gson.JsonArray;
import io.netty.util.internal.ResourcesUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Slf4j
@Service
public class BizPaperlessMeetingsServiceImpl extends SysBaseServiceImpl<BizPaperlessMeetingsMapper, BizPaperlessMeetings> implements IBizPaperlessMeetingsService {

    @Autowired
    private BizPaperlessMeetingsMapper bizPaperlessMeetingsMapper;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private ISysImportService iSysImportService;

    @Autowired
    private OrgImportAndExportService orgImportAndExportService;

    @Autowired
    private ExportToExcelService exportToExcelService;

    @Autowired
    private IBizPaperlessIssuesService bizPaperlessIssuesService;

    @Autowired
    private IBizMeetingsIssuesUserService bizMeetingsIssuesUserService;

    @Autowired
    private BizMeetingsIssuesUserMapper bizMeetingsIssuesUserMapper;

    @Autowired
    private BizPaperlessIssuesMapper bizPaperlessIssuesMapper;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpEnclosureFileService cscpEnclosureFileService;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private HaoQianUtils haoQianUtils;

    /**
     * 翻页
     *
     * @param
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPaperlessMeetingsDTO> queryListPage(BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizPaperlessMeetings> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(bizPaperlessMeetingsDTO.getCreateBy() != null ,BizPaperlessMeetings::getCreateBy,bizPaperlessMeetingsDTO.getCreateBy())
                .like(StrUtil.isNotEmpty(bizPaperlessMeetingsDTO.getTitle())  ,BizPaperlessMeetings::getTitle,
                        bizPaperlessMeetingsDTO.getTitle())
                .like(StrUtil.isNotEmpty(bizPaperlessMeetingsDTO.getMeetingVenue()),BizPaperlessMeetings::getMeetingVenue,
                        bizPaperlessMeetingsDTO.getMeetingVenue() )
                .ge(bizPaperlessMeetingsDTO.getMeetStartTimeQuery() != null,BizPaperlessMeetings::getMeetStartTime ,
        bizPaperlessMeetingsDTO.getMeetStartTimeQuery())


               .orderByDesc(BizPaperlessMeetings::getCreateTime);

        if(bizPaperlessMeetingsDTO.getMeetEndTimeQuery() != null){ // 结束时间加一天作为查询条件
            queryWrapper.le(BizPaperlessMeetings::getMeetEndTime, bizPaperlessMeetingsDTO.getMeetEndTimeQuery().plusDays(1));
        }

        IPage<BizPaperlessMeetings> pageData = bizPaperlessMeetingsMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        //1  找会议
        IPage<BizPaperlessMeetingsDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizPaperlessMeetingsDTO.class));
        List<BizPaperlessMeetingsDTO> entDTO = data.getRecords();



        entDTO.forEach(a-> {
            Long meetingsId = a.getId();
            // 1.1 会议绑定 列席人员
            // 1.2 会议绑定 会议主持
            // 会议绑定人员
          //  List<CscpUserDTO> getuser = bizPaperlessIssuesMapper.getuser(meetingsId , null , null);

            //2 绑定 议题
            LambdaQueryWrapper<BizPaperlessIssues> bizPaperlessIssuesQW = new LambdaQueryWrapper();
            bizPaperlessIssuesQW.eq(BizPaperlessIssues :: getMeetingsId, meetingsId);
            List<BizPaperlessIssues> paperlessIssuesList = bizPaperlessIssuesMapper.selectListNoAdd(bizPaperlessIssuesQW);

            List<BizPaperlessIssuesDTO> bizPaperlessIssuesDTO = ListCopyUtil.copy(paperlessIssuesList,BizPaperlessIssuesDTO.class);
            a.setBizPaperlessIssuesDTO(bizPaperlessIssuesDTO);
            a.setYtsum(bizPaperlessIssuesDTO.size());
            if (StringUtils.isNotEmpty(a.getAttendees())){
                JSONArray jsonArray = JSONArray.fromObject(a.getAttendees());
                if (jsonArray != null){
                    a.setChsum(jsonArray.size());
                }
            }

            // **给 进行中,未开始, 已结束会议增加标记
            Integer meetingStatus = a.getMeetingStatus();
            LocalDateTime meetStartTime = a.getMeetStartTime();
            LocalDateTime now = LocalDateTime.now();
            if(meetingStatus != 2){ // 不为已经结束
                if(meetStartTime.isBefore(now)){
                    a.setMeetingStatus(1);
                }else {
                    a.setMeetingStatus(0);
                }
            }
        });

        return new PageResult<BizPaperlessMeetingsDTO>(entDTO,
            data.getTotal(), data.getCurrent());
    }

    /**
     * 查看会议列表
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizPaperlessMeetingsDTO> queryList(BizPaperlessMeetingsDTO entityDTO) {
        LambdaQueryWrapper<BizPaperlessMeetings> queryWrapper = new LambdaQueryWrapper();
            List<BizPaperlessMeetings> listData = bizPaperlessMeetingsMapper.selectList(queryWrapper);
            List<BizPaperlessMeetingsDTO> BizPaperlessMeetingsDTOList = ListCopyUtil.copy(listData, BizPaperlessMeetingsDTO.class);
        return BizPaperlessMeetingsDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizPaperlessMeetingsDTO findOne(Long id) {
        BizPaperlessMeetings  bizPaperlessMeetings =  bizPaperlessMeetingsMapper.selectById(id);
        // 1.1 会议绑定 列席人员
        BizPaperlessMeetingsDTO  bizPaperlessMeetingsDTO  = BeanConvertUtils.copyProperties(bizPaperlessMeetings,BizPaperlessMeetingsDTO.class);

        // 1.2 会议绑定 会议主持b
        LambdaQueryWrapper<BizMeetingsIssuesUser> userLambdaQueryWrapper = new LambdaQueryWrapper();
        userLambdaQueryWrapper.eq(BizMeetingsIssuesUser :: getMeetingsId, id)
                .eq(BizMeetingsIssuesUser :: getReportPeople, 2);
        List<BizMeetingsIssuesUser> userList =
                bizMeetingsIssuesUserMapper.selectListNoAdd(userLambdaQueryWrapper);
        if(!userList.isEmpty()){
            BizUserMeetingDTO bizUserMeetingDTO = BeanConvertUtils.copyProperties(userList.get(0) , BizUserMeetingDTO.class);
            bizPaperlessMeetingsDTO.setConferenceHostDTO(bizUserMeetingDTO);
        }


        //2 绑定 议题
        LambdaQueryWrapper<BizPaperlessIssues> bizPaperlessIssuesQW = new LambdaQueryWrapper();
        bizPaperlessIssuesQW.eq(BizPaperlessIssues :: getMeetingsId, id);
        List<BizPaperlessIssues> paperlessIssuesList = bizPaperlessIssuesMapper.selectListNoAdd(bizPaperlessIssuesQW);

        List<BizPaperlessIssuesDTO> bizPaperlessIssuesDTO = ListCopyUtil.copy(paperlessIssuesList,BizPaperlessIssuesDTO.class);


        bizPaperlessIssuesDTO.forEach(b -> {
            // BizPaperlessIssuesDTO newDto =BeanConvertUtils.copyProperties(b,BizPaperlessIssuesDTO.class);

            // 2.1 议题  绑定参与议题人员 reportPeople=0
            LambdaQueryWrapper<BizMeetingsIssuesUser> bizMeetingsIssuesUserQW = new LambdaQueryWrapper();
            bizMeetingsIssuesUserQW.eq(BizMeetingsIssuesUser :: getMeetingsId, id);
            bizMeetingsIssuesUserQW.eq(BizMeetingsIssuesUser :: getIssuesId, b.getId());

            List<BizMeetingsIssuesUser> meetingsIssuesUserList =
                    bizMeetingsIssuesUserMapper.selectListNoAdd(bizMeetingsIssuesUserQW);

            List<BizMeetingsIssuesUser> user0 =
                    meetingsIssuesUserList.stream().filter(i -> i.getReportPeople() == 0).collect(Collectors.toList());
            List<BizUserMeetingDTO> user0copy = ListCopyUtil.copy(user0 , BizUserMeetingDTO.class);
            b.setCscpUser(user0copy);

            // 2.1 议题   绑定 汇报人 reportPeople=1
            List<BizMeetingsIssuesUser> user1 =
                    meetingsIssuesUserList.stream().filter(i -> i.getReportPeople() == 1).collect(Collectors.toList());
            List<BizUserMeetingDTO> user1copy = ListCopyUtil.copy(user1 , BizUserMeetingDTO.class);
            b.setReportCscpUser(user1copy);
        });
        bizPaperlessMeetingsDTO.setBizPaperlessIssuesDTO(bizPaperlessIssuesDTO);
        return  bizPaperlessMeetingsDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizPaperlessMeetingsDTO create(BizPaperlessMeetingsDTO entityDTO) {
        //保存会议信息
        // 会议参会人员信息
        List<BizUserMeetingDTO> bizUserMeetingDTOList =  entityDTO.getCscpUserDTO();
        if (CollectionUtil.isNotEmpty(bizUserMeetingDTOList)){
            entityDTO.setAttendees(JsonUtils.objectToJson(bizUserMeetingDTOList));
        }

       BizPaperlessMeetings bizPaperlessMeetings =  BeanConvertUtils.copyProperties(entityDTO,BizPaperlessMeetings.class);
       bizPaperlessMeetings.setMeetingStatus(0);
       save(bizPaperlessMeetings);
        cscpEnclosureFileService.sysFileUpload(bizPaperlessMeetings.getId());
       // 上传文件给好签
       BizPaperlessMeetingsDTO bizPaperlessMeetingsDTO = BeanConvertUtils.copyProperties(bizPaperlessMeetings,BizPaperlessMeetingsDTO.class);

       // 设置排序号
       List<BizPaperlessIssuesDTO> listbizPaperlessIssuesDTO = entityDTO.getBizPaperlessIssuesDTO();
        for (int i = 0 ; i < listbizPaperlessIssuesDTO.size() ; i++) {
            listbizPaperlessIssuesDTO.get(i).setSort(i+1);
        }
        // 保存议题信息
        listbizPaperlessIssuesDTO.forEach(a ->  {
            a.setMeetingsId(bizPaperlessMeetingsDTO.getId());
            BizPaperlessIssues bizPaperlessIssues = BeanConvertUtils.copyProperties(a,BizPaperlessIssues.class);
            bizPaperlessIssuesService.save(bizPaperlessIssues);
            BizPaperlessIssuesDTO newBizPaperlessIssuesDTO = BeanConvertUtils.copyProperties(bizPaperlessIssues,BizPaperlessIssuesDTO.class);
            a.setId(newBizPaperlessIssuesDTO.getId());
            List<BizMeetingsIssuesUserDTO> bizMeetingsIssuesUserList = new ArrayList<>();
            // Set<Long> reportUserList = new HashSet<>();

            // 添加会议主持人员信息
            if (entityDTO.getConferenceHostId() != null ) {//&& entityDTO.getConferenceHostDTO() != null){
                BizMeetingsIssuesUserDTO bizMeetingsHostUser = new BizMeetingsIssuesUserDTO();
                bizMeetingsHostUser.setMeetingsId(bizPaperlessMeetingsDTO.getId());
                bizMeetingsHostUser.setIssuesId(a.getId());
                bizMeetingsHostUser.setUserId(entityDTO.getConferenceHostId());
                bizMeetingsHostUser.setReportPeople(2);
                bizMeetingsHostUser.setPost( entityDTO.getConferenceHostDTO().getPost());
                bizMeetingsHostUser.setUserName( entityDTO.getConferenceHostName());
                bizMeetingsHostUser.setDepartmentName(entityDTO.getConferenceHostDTO().getDepartmentName());
                bizMeetingsHostUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(),bizMeetingsHostUser.getId());
                bizMeetingsIssuesUserList.add(bizMeetingsHostUser);
                // reportUserList.add(entityDTO.getConferenceHostId());
            }

            // 获取所有汇报人员；
            List<BizUserMeetingDTO> reportCscpUser = a.getReportCscpUser();
            if (CollectionUtil.isNotEmpty(reportCscpUser)) {
                reportCscpUser.forEach(b -> {
                    BizMeetingsIssuesUserDTO bizMeetingsIssuesUser = new BizMeetingsIssuesUserDTO();
                    bizMeetingsIssuesUser.setMeetingsId(bizPaperlessMeetingsDTO.getId());
                    bizMeetingsIssuesUser.setIssuesId(a.getId());
                    bizMeetingsIssuesUser.setUserId(b.getId());
                    bizMeetingsIssuesUser.setReportPeople(1);
                    bizMeetingsIssuesUser.setPost(b.getPost());
                    bizMeetingsIssuesUser.setUserName(b.getUserName());
                    bizMeetingsIssuesUser.setDepartmentName(b.getDepartmentName());
                    bizMeetingsIssuesUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                    cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(), bizMeetingsIssuesUser.getId());
                    bizMeetingsIssuesUserList.add(bizMeetingsIssuesUser);
                });
            }
            // 获取所有参会人员；
            List<BizUserMeetingDTO> userList = a.getCscpUser();
            if (CollectionUtil.isNotEmpty(userList)){
                userList.forEach(b ->  {
                    BizMeetingsIssuesUserDTO bizMeetingsIssuesUser = new BizMeetingsIssuesUserDTO();
                    bizMeetingsIssuesUser.setMeetingsId(bizPaperlessMeetingsDTO.getId());
                    bizMeetingsIssuesUser.setIssuesId(a.getId());
                    bizMeetingsIssuesUser.setUserId(b.getId());
                    bizMeetingsIssuesUser.setReportPeople(0);
                    bizMeetingsIssuesUser.setPost(b.getPost());
                    bizMeetingsIssuesUser.setUserName(b.getUserName());
                    bizMeetingsIssuesUser.setDepartmentName(b.getDepartmentName());
                    bizMeetingsIssuesUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                    cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(),bizMeetingsIssuesUser.getId());
                    bizMeetingsIssuesUserList.add(bizMeetingsIssuesUser);
                });
            }
            bizMeetingsIssuesUserService.insertBatch(bizMeetingsIssuesUserList);
        });

       return  bizPaperlessMeetingsDTO;
    }

    /**
     *  // 1 更新会议 附件前端已经更新   上传文件给好签
     *  // 2 删议题 删人员  删附件
     *  // 3 保存 议题  人员 附件
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizPaperlessMeetingsDTO entityDTO) {
        BizPaperlessMeetings bizPaperlessMeetings = BeanConvertUtils.copyProperties(entityDTO,BizPaperlessMeetings.class);
        // 1 更新会议 附件前端已经更新
        int k = bizPaperlessMeetingsMapper.updateById(bizPaperlessMeetings);
        // 上传文件给好签
        cscpEnclosureFileService.sysFileUpload(bizPaperlessMeetings.getId());
        // 2 删议题 删人员  删附件
        bizPaperlessIssuesService.deleteByMeetingId(bizPaperlessMeetings.getId());

        // 3 保存 议题  人员 附件
        // 设置排序号
        List<BizPaperlessIssuesDTO> listbizPaperlessIssuesDTO = entityDTO.getBizPaperlessIssuesDTO();
        for (int i = 0 ; i < listbizPaperlessIssuesDTO.size() ; i++) {
            listbizPaperlessIssuesDTO.get(i).setSort(i+1);
        }
        listbizPaperlessIssuesDTO.forEach(a ->  {
            a.setMeetingsId(entityDTO.getId());
            BizPaperlessIssues bizPaperlessIssues = BeanConvertUtils.copyProperties(a,BizPaperlessIssues.class);
            // 3.1 更新议题 附件已经上传
            bizPaperlessIssuesService.save(bizPaperlessIssues);
            BizPaperlessIssuesDTO newBizPaperlessIssuesDTO = BeanConvertUtils.copyProperties(bizPaperlessIssues,BizPaperlessIssuesDTO.class);
            a.setId(newBizPaperlessIssuesDTO.getId());

            List<BizMeetingsIssuesUserDTO> bizMeetingsIssuesUserList = new ArrayList<>();
            // Set<Long> reportUserList = new HashSet<>();

            // 添加会议主持人员信息
            if (entityDTO.getConferenceHostId() != null ) {//&& entityDTO.getConferenceHostDTO() != null){
                BizMeetingsIssuesUserDTO bizMeetingsHostUser = new BizMeetingsIssuesUserDTO();
                bizMeetingsHostUser.setMeetingsId(entityDTO.getId());
                bizMeetingsHostUser.setIssuesId(a.getId());
                bizMeetingsHostUser.setUserId(entityDTO.getConferenceHostId());
                bizMeetingsHostUser.setReportPeople(2);
                 bizMeetingsHostUser.setPost( entityDTO.getConferenceHostDTO().getPost());
                String userName = entityDTO.getConferenceHostDTO().getUserName();
                String realName = entityDTO.getConferenceHostDTO().getRealName();
                if(StrUtil.isNotEmpty(userName)){
                    bizMeetingsHostUser.setUserName(userName);
                }
                if(StrUtil.isNotEmpty(realName)){
                    bizMeetingsHostUser.setUserName(realName);
                }
                bizMeetingsHostUser.setUserName( entityDTO.getConferenceHostDTO().getUserName());
                bizMeetingsHostUser.setDepartmentName(entityDTO.getDepartmentName());
                bizMeetingsHostUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(),bizMeetingsHostUser.getId());
                bizMeetingsIssuesUserList.add(bizMeetingsHostUser);
                // reportUserList.add(entityDTO.getConferenceHostId());
            }

            // 获取所有汇报人员；
            List<BizUserMeetingDTO> reportCscpUser = a.getReportCscpUser();
            if (CollectionUtil.isNotEmpty(reportCscpUser)) {
                reportCscpUser.forEach(b -> {
                    BizMeetingsIssuesUserDTO bizMeetingsIssuesUser = new BizMeetingsIssuesUserDTO();
                    bizMeetingsIssuesUser.setMeetingsId(entityDTO.getId());
                    bizMeetingsIssuesUser.setIssuesId(a.getId());
                    bizMeetingsIssuesUser.setUserId(b.getId());
                    bizMeetingsIssuesUser.setReportPeople(1);
                    bizMeetingsIssuesUser.setPost(b.getPost());
                    bizMeetingsIssuesUser.setUserName(b.getUserName());
                    bizMeetingsIssuesUser.setDepartmentName(entityDTO.getDepartmentName());
                    bizMeetingsIssuesUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                    cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(), bizMeetingsIssuesUser.getId());
                    bizMeetingsIssuesUserList.add(bizMeetingsIssuesUser);
                });
            }
            // 获取所有参会人员；
            List<BizUserMeetingDTO> userList = a.getCscpUser();
            if (CollectionUtil.isNotEmpty(userList)){
                userList.forEach(b ->  {
                    BizMeetingsIssuesUserDTO bizMeetingsIssuesUser = new BizMeetingsIssuesUserDTO();
                    bizMeetingsIssuesUser.setMeetingsId(entityDTO.getId());
                    bizMeetingsIssuesUser.setIssuesId(a.getId());
                    bizMeetingsIssuesUser.setUserId(b.getId());
                    bizMeetingsIssuesUser.setReportPeople(0);
                    bizMeetingsIssuesUser.setPost(b.getPost());
                    bizMeetingsIssuesUser.setUserName(b.getUserName());
                    bizMeetingsIssuesUser.setDepartmentName(entityDTO.getDepartmentName());

                    bizMeetingsIssuesUser.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                    cscpEnclosureFileService.enclosureChangeEnclosureAndUpdateSign(a.getId(),bizMeetingsIssuesUser.getId());
                    bizMeetingsIssuesUserList.add(bizMeetingsIssuesUser);
                });
            }
            bizMeetingsIssuesUserService.insertBatch(bizMeetingsIssuesUserList);
        });
        return k;
    }

    /**
     * 删除会议, 删除 议题, 删除人员
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        //删除关联表信息
        LambdaQueryWrapper<BizMeetingsIssuesUser> meetingsIssuesUserQW = new LambdaQueryWrapper();
        meetingsIssuesUserQW.eq(BizMeetingsIssuesUser :: getMeetingsId, id);
        List<Long> bizMeetingsIssuesUsers =
                bizMeetingsIssuesUserMapper.selectListNoAdd(meetingsIssuesUserQW).stream().map(i -> i.getId()).collect(Collectors.toList());
        bizMeetingsIssuesUserMapper.deleteBatchIds(bizMeetingsIssuesUsers);

        //删除议题表
        LambdaQueryWrapper<BizPaperlessIssues> paperlessIssuesQW = new LambdaQueryWrapper();
        paperlessIssuesQW.eq(BizPaperlessIssues :: getMeetingsId , id);
        List<Long> bizPaperlessIssues =
                bizPaperlessIssuesMapper.selectListNoAdd(paperlessIssuesQW).stream().map(i -> i.getId()).collect(Collectors.toList());
        bizPaperlessIssuesMapper.deleteBatchIds(bizPaperlessIssues);
        // 删除缓存数据
        redisUtil.del(id.toString());
        return bizPaperlessMeetingsMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizPaperlessMeetingsId
     * @return
     */
    @Override
    public boolean existByBizPaperlessMeetingsId(Long BizPaperlessMeetingsId) {
        if (BizPaperlessMeetingsId != null) {
            LambdaQueryWrapper<BizPaperlessMeetings> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizPaperlessMeetings::getId, BizPaperlessMeetingsId);
            List<BizPaperlessMeetings> result = bizPaperlessMeetingsMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizPaperlessMeetingsDTO> dataList) {
        List<BizPaperlessMeetings> result = ListCopyUtil.copy(dataList, BizPaperlessMeetings.class);
        return saveBatch(result);
    }

    @Override
    public List<UserImportDTO> saveUsers(Long cacheId, List<UserImportDTO> dataList) {
        List<UserImportDTO> resultList = new ArrayList<>();
        //获取当前单位所有用户
        Long id = SecurityUtils.getCurrentCompanyId();
        CscpUserDTO dto = new CscpUserDTO();
        dto.setCompanyId(id);
        List<CscpUserDTO> userDTOList = cscpUserService.selectUserList(dto);
        Iterator iterator = dataList.iterator();
        // 去掉空的数据
        dataList = dataList.stream().filter(i -> StringUtils.isNotEmpty(i.getRealName()) && StringUtils.isNotEmpty(i.getMobile()))
                .collect(Collectors.toList());
        // 新建一个保存失败用户记录的集合
        List<UserImportDTO> failedList = new ArrayList<>();

        for (UserImportDTO userImportDTO : dataList) {
            log.info("UserImportAndExportServiceImpl.saveUsers 读取到一条数据: {}", userImportDTO);
            try {
                // 组装数据
                userImportDTO.setCompanyId(id);

                CscpUserDTO userDTO = this.assembledUserDTO(userImportDTO);
                CscpUserDTO insertUser = cscpUserService.insert(userDTO);
                userImportDTO.setId(insertUser.getId());
                userImportDTO.setDepartmentId(insertUser.getDefaultDepart( ));
                String post = userImportDTO.getPost();
                if(post != null){
                    String[] split = post.split("-");
                    if(split.length >=2 ){
                        userImportDTO.setDepartmentName(split[0]);
                        userImportDTO.setPost(split[1]);
                    }

                }
                resultList.add(userImportDTO);
            } catch (BusinessException e) {
                log.error(e.getMessage());
                userImportDTO.setFailedReason(e.getMessage());
                failedList.add(userImportDTO);
            }
        }

        TSysImportDTO sysUserImportDTO = new TSysImportDTO();
        sysUserImportDTO.setTotalNo(dataList.size());
        sysUserImportDTO.setFailedNo(failedList.size());
        sysUserImportDTO.setSuccessNo(dataList.size() - failedList.size());
        // 导入数据类型
        sysUserImportDTO.setType( SysImportTypeUtils.getImportType( FileBasePathName.CHENZHOU_MEETING_USER_IMPORT));

        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            iSysImportService.create(sysUserImportDTO);
            Long userId = SecurityUtils.getCurrentUserId();
            //JSON存redis
            JSONArray jsonarray = JSONArray.fromObject(dataList);
            String resultJson = jsonarray.toString();
            redisUtil.set(cacheId.toString(),resultJson);
//            String getre = String.valueOf(redisUtil.get(userId.toString()));
//            resultList = com.alibaba.fastjson.JSONArray.parseArray( getre, UserImportDTO.class);
            return resultList;
        }

        // 保存导入记录，并上传Excel失败文件
        iSysImportService.saveAndUploadFile(sysUserImportDTO, failedList, UserImportDTO.class, FileBasePathName.CHENZHOU_MEETING_USER_IMPORT);
        if (CollectionUtils.isNotEmpty(failedList)){
            StringBuffer sb =new StringBuffer();
            for (UserImportDTO userImportDTO : failedList) {
                sb.append(userImportDTO.getFailedReason() + "; ");
            }
            throw new BusinessException(sb.toString());
        }
        return resultList;
    }


    /**
     * 组装导入用户入参
     * @param userImportDTO
     * @return
     */
    private CscpUserDTO assembledUserDTO(UserImportDTO userImportDTO) {
        if (StringUtils.isBlank(userImportDTO.getRealName())) {
            throw new BusinessException("用户名称不能为空");
        }
        // 设置用户名
        String name = PinyinUtil.getPinyin(userImportDTO.getRealName().trim(),"");
        String userNameMaxNumber = cscpUserService.getUserNameMaxNumber(name);
        userImportDTO.setUserName(userNameMaxNumber);
        userImportDTO.setRealName(userImportDTO.getRealName().trim());

        // 设置手机号码
        String mobile = userImportDTO.getMobile();
        if (!com.ctsi.hndx.utils.StringUtils.isMobile(mobile)){
            throw new BusinessException("手机号码格式错误");
        }

        // boolean existByMobile = cscpUserService.existByMobile(mobile);
        // if (existByMobile){
        //     throw new BusinessException("手机号码已经在系统存在，请不要重复导入");
        // }

        // 查询部门
        CscpOrgDTO dto = new CscpOrgDTO();
        String departmentName = "参会人员";
        String sort = "1";
        String post = userImportDTO.getDepartmentName()+"-"+userImportDTO.getPost();
        String[] departmentNames = {departmentName};
        String[] sorts = {sort};
        String[] posts = {post};
        String departMentSplit = ",";
        if (departmentName.indexOf(departMentSplit) > 0){
            departmentNames = departmentName.split(departMentSplit);
            if (sort.indexOf(departMentSplit) > 0 ){
                sorts = sort.split(departMentSplit);
            }else {
                throw new BusinessException("此用户有多个部门，请输入多个例如  1,2");
            }
            //  处理职务，职务可能为空，如果有多个部门，每个部门的职务以逗号隔开
            if (StringUtils.isNotEmpty(post) && post.indexOf(departMentSplit) > 0){
                posts = post.split(departMentSplit);
            }

        }else {
            if (!StringUtils.isNumeric(sort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
        }

        CscpUserDTO userDTO = new CscpUserDTO();
        userImportDTO.setDepartmentName(departmentName);
        userImportDTO.setPost(post);
        BeanUtils.copyProperties(userImportDTO, userDTO);
        userDTO.setLoginName(userImportDTO.getUserName());
        List<CscpOrgNameIdListDTO> orgNameList = new ArrayList<>();
        for (int i = 0; i< departmentNames.length; i++){
            dto.setOrgName(departmentNames[i]);
            dto.setCompanyId(userImportDTO.getCompanyId());
            List<CscpOrgDTO> orgDTOList = cscpOrgService.criteriaQueryOrgDTO(dto);
            if (CollectionUtils.isEmpty(orgDTOList)) {
                log.info("在该单位没有找到该部门：{} <自动创建该部门>", userImportDTO.getDepartmentName());
                // throw new BusinessException("在该单位没有找到该部门名称：" + userImportDTO.getDepartmentName());
                CscpOrgDTO build = CscpOrgDTO.builder()
                        .parentId(SecurityUtils.getCurrentCompanyId())
                        .type(3)
                        .orgName("参会人员")
                        .orderBy(99)
                        .orgAbbreviation("参会人员")
                        .build();
                cscpOrgService.save(build);
                orgDTOList = cscpOrgService.criteriaQueryOrgDTO(dto);
            }

            if (orgDTOList.size() > 1) {
                log.error("在该单位下存在多个相同的部门名称：{}", userImportDTO.getDepartmentName());
                throw new BusinessException("在该单位下存在多个相同的部门名称：" + userImportDTO.getDepartmentName());
            }
            if (orgDTOList.get(0).getType() != 3) {
                log.error("用户只允许挂在部门下面：{}", userImportDTO.getDepartmentName());
                throw new BusinessException("用户只允许挂在部门下面：" + userImportDTO.getDepartmentName());
            }

            // 加部门信息
            CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
            cscpOrgNameIdListDTO.setId(orgDTOList.get(0).getId());
            cscpOrgNameIdListDTO.setTitle(orgDTOList.get(0).getOrgName());
            String orgSort = sorts[i];
            if (!StringUtils.isNumeric(orgSort)){
                throw new BusinessException("部门排序号只能输入数字，数据之间以，隔开");
            }
            cscpOrgNameIdListDTO.setUserOrgSort(Integer.valueOf(orgSort));
            cscpOrgNameIdListDTO.setPost(userImportDTO.getDepartmentName()+"-"+userImportDTO.getPost());
            Integer departmengHead = LanguageConvertUtil.stringToInteger(userImportDTO.getDepartmentHead());
            cscpOrgNameIdListDTO.setDepartmentHead(departmengHead == null ? 0:departmengHead);
            // 设置职务
            if (posts.length > i){
                if (StringUtils.isNotEmpty(posts[i])){
                    cscpOrgNameIdListDTO.setPost(posts[i]);
                }
            }else {
                if (StringUtils.isNotEmpty(posts[0])){
                    cscpOrgNameIdListDTO.setPost(posts[0]);
                }
            }
            orgNameList.add(cscpOrgNameIdListDTO);
            userDTO.setOrgNameList(orgNameList);

            if (i == 0){
                // 设置默认部门
                userDTO.setDefaultDepart(orgDTOList.get(0).getId());
            }

        }
        // 加默认角色信息
        List<Long> roleIdList = new ArrayList<>();
        roleIdList.add(Long.parseLong( SystemRole.GENERAL_NAME.getId()));
        userDTO.setRoleIds(roleIdList);
        // 是否显示和是否统计
        userDTO.setDisplay(true);
        userDTO.setWhetherShow(1);
        userDTO.setStatistics(true);
        return userDTO;
    }


    /**
     * 参会人员在会议中可以查看
     * 会议状态<meetingStatus> 0:未开始 1:进行中 2:已结束
     * @param meetingStatus
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizPaperlessMeetingsDTO> appQueryListPage(Integer meetingStatus ,String searchKey,
                                                                BasePageForm basePageForm) {
        // 先查数据 再分页
        List<BizPaperlessMeetingsDTO> dtos =  bizPaperlessMeetingsMapper.appQueryListPage(meetingStatus,searchKey,
                SecurityUtils.getCurrentUserId());


        Integer pageSize = basePageForm.getPageSize();
        Integer currentPage = basePageForm.getCurrentPage();
        // 手动分页
        List<BizPaperlessMeetingsDTO> data =
                dtos.stream().skip((currentPage - 1) * pageSize).limit(pageSize)
                        .collect(Collectors.toList());

        return new PageResult<BizPaperlessMeetingsDTO>(data,
                dtos.size(), dtos.size());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoFinishMeeting() {
        LocalDateTime now = LocalDateTimeUtil.now();
        LambdaQueryWrapper<BizPaperlessMeetings> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BizPaperlessMeetings::getId
        ).ne(BizPaperlessMeetings::getMeetingStatus,2).lt(BizPaperlessMeetings::getMeetEndTime,
                now);
        List<BizPaperlessMeetings> bizPaperlessMeetings = bizPaperlessMeetingsMapper.selectListNoAdd(queryWrapper);
        // 1 删除缓存数据
        for (BizPaperlessMeetings bizPaperlessMeeting : bizPaperlessMeetings) {
            redisUtil.del(bizPaperlessMeeting.getId( ).toString());
        }

        // 2 结束会议<条件是 没有结束的会议,并且 结束时间在现在之后>
        LambdaUpdateWrapper<BizPaperlessMeetings> updateQuery = new LambdaUpdateWrapper<BizPaperlessMeetings>();
        updateQuery.set(BizPaperlessMeetings::getMeetingStatus,2);
        updateQuery.ne(BizPaperlessMeetings::getMeetingStatus,2).lt(BizPaperlessMeetings::getMeetEndTime,
                now);
        bizPaperlessMeetingsMapper.update(null,updateQuery);

    }

}
