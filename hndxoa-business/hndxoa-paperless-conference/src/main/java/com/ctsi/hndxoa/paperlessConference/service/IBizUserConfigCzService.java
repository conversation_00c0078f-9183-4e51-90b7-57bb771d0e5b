package com.ctsi.hndxoa.paperlessConference.service;

import com.ctsi.hndxoa.paperlessConference.entity.dto.BizUserConfigCzDTO;
import com.ctsi.hndxoa.paperlessConference.entity.BizUserConfigCz;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 郴州会议签批app用户信息配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
public interface IBizUserConfigCzService extends SysBaseServiceI<BizUserConfigCz> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizUserConfigCzDTO> queryListPage(BizUserConfigCzDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizUserConfigCzDTO> queryList(BizUserConfigCzDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizUserConfigCzDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizUserConfigCzDTO create(BizUserConfigCzDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizUserConfigCzDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizUserConfigCzId
     * @param code
     * @return
     */
    boolean existByBizUserConfigCzId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizUserConfigCzDTO> dataList);


    /**
     * 更具userid 获取
     * @param userid
     * @return
     */
    BizUserConfigCzDTO findOneByUserid(long userid);
}
