package com.ctsi.hndxoa.issues.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 议题表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_paperless_issues")
@ApiModel(value="BizPaperlessIssues对象", description="")
public class BizPaperlessIssues extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    @ApiModelProperty(value = "会议ID")
    private Long meetingsId;
    /**
     * 议题标题
     */
    @ApiModelProperty(value = "议题标题")
    private String title;

    /**
     * 汇报内容
     */
    @ApiModelProperty(value = "汇报内容")
    private String reportContent;

    /**
     * 汇报人
     */
    @ApiModelProperty(value = "汇报人")
    private String conferenceHost;

    /**
     * 汇报时长
     */
    @ApiModelProperty(value = "汇报时长")
    private String duration;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String departmentName;

    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;


}
