package com.ctsi.hndxoa.util;

import com.ctsi.hndxoa.paperlessConference.service.IBizPaperlessMeetingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @description
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2023-04-24
 **/
@Component
public class MeetingTask {

    @Autowired
    private IBizPaperlessMeetingsService bizPaperlessMeetingsService;

    /**
     * 自动结束会议
     */
    // @Scheduled(cron ="0 50 18 * * ?") // 测试下午6点50 执行
   @Scheduled(cron ="0 50 23 * * ?") // 晚上11点50 执行
    public void autoFinishMeeting() {
        System.out.println("-----------自动结束会议---------start");
       bizPaperlessMeetingsService.autoFinishMeeting();
        System.out.println("-----------自动结束会议---------end");
    }

}
