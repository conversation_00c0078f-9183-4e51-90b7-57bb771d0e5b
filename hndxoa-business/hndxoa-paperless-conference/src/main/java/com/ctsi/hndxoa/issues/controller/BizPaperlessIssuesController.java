package com.ctsi.hndxoa.issues.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndxoa.issues.entity.BizPaperlessIssues;
import com.ctsi.hndxoa.issues.entity.dto.BizPaperlessIssuesDTO;
import com.ctsi.hndxoa.issues.service.IBizPaperlessIssuesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 无纸化会议议题
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizPaperlessIssues")
@Api(value = "", tags = "议题接口")
public class BizPaperlessIssuesController extends BaseController {

    private static final String ENTITY_NAME = "bizPaperlessIssues";

    @Autowired
    private IBizPaperlessIssuesService bizPaperlessIssuesService;



    /**
     *  新增批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizPaperlessIssues.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增批量数据")
    
    public ResultVO createBatch(@RequestBody List<BizPaperlessIssuesDTO> bizPaperlessIssuesList) {
       Boolean  result = bizPaperlessIssuesService.insertBatch(bizPaperlessIssuesList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizPaperlessIssues.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增数据")
    
    public ResultVO<BizPaperlessIssuesDTO> create(@RequestBody BizPaperlessIssuesDTO bizPaperlessIssuesDTO)  {
        BizPaperlessIssuesDTO result = bizPaperlessIssuesService.create(bizPaperlessIssuesDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizPaperlessIssues.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizPaperlessIssues.update')")
    public ResultVO update(@RequestBody BizPaperlessIssuesDTO bizPaperlessIssuesDTO) {
	    Assert.notNull(bizPaperlessIssuesDTO.getId(), "general.IdNotNull");
        int count = bizPaperlessIssuesService.update(bizPaperlessIssuesDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizPaperlessIssues.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.bizPaperlessIssues.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizPaperlessIssuesService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizPaperlessIssuesDTO bizPaperlessIssuesDTO = bizPaperlessIssuesService.findOne(id);
        return ResultVO.success(bizPaperlessIssuesDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizPaperlessIssuesPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizPaperlessIssuesDTO>> queryBizPaperlessIssuesPage(BizPaperlessIssuesDTO bizPaperlessIssuesDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizPaperlessIssuesService.queryListPage(bizPaperlessIssuesDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizPaperlessIssues")
   @ApiOperation(value = "查询多条数据 不分页", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizPaperlessIssuesDTO>> queryBizPaperlessIssues(BizPaperlessIssuesDTO bizPaperlessIssuesDTO) {
       List<BizPaperlessIssuesDTO> list = bizPaperlessIssuesService.queryList(bizPaperlessIssuesDTO);
       return ResultVO.success(new ResResult<BizPaperlessIssuesDTO>(list));
   }


    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryIssuesAndAnnex")
    @ApiOperation(value = "查询会议的议程数据,带附件", notes = "传入参数 会议id: meetingsId")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizPaperlessIssuesDTO>> queryIssuesAndAnnex(BizPaperlessIssuesDTO bizPaperlessIssuesDTO) {
        Assert.notNull(bizPaperlessIssuesDTO.getMeetingsId(), "general.IdNotNull");
        List<BizPaperlessIssuesDTO> list = bizPaperlessIssuesService.queryListAndAnnex(bizPaperlessIssuesDTO);
        return ResultVO.success(new ResResult<BizPaperlessIssuesDTO>(list));
    }


}
