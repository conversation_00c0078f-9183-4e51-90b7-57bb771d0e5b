package com.ctsi.hndxoa.paperlessConference.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 无纸化会议表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_paperless_meetings")
@ApiModel(value="BizPaperlessMeetings对象", description="")
public class BizPaperlessMeetings extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "会议标题")
    private String title;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String departmentName;

    /**
     * 是否有附件
     */
    @ApiModelProperty(value = "是否有附件")
    private String annex;

    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    private String meetingVenue;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetStartTime;

    /**
     * 会议结束时间
     */
    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime meetEndTime;

    /**
     * 列席人员
     */
    @ApiModelProperty(value = "列席人员")
    private String attendees;

    /**
     * 主持人
     */
    @ApiModelProperty(value = "会议主持人姓名")
    private String conferenceHostName;


    /**
     * 主持人
     */
    @ApiModelProperty(value = "会议主持人的id")
    private Long conferenceHostId;
    /**
     * 会议状态 0:未开始 1:进行中 2:已结束
     */
    @ApiModelProperty(value = "会议状态 0:未开始 1:进行中 2:已结束")
    private Integer meetingStatus;


}
