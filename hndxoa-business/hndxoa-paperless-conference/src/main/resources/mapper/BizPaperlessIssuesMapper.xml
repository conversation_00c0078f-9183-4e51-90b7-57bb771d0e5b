<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.issues.mapper.BizPaperlessIssuesMapper">


    <delete id="deleteIssues" parameterType="java.lang.Long">
        delete from biz_paperless_issues where meetings_id = #{id}
    </delete>
    <select id="getuser" resultType="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO">
        SELECT a.id ,a.real_name,b.JOB_TITLE as post
        FROM cscp_user a LEFT JOIN t_address_book b on a.id = b.USER_ID
        where a.id in (select user_id from biz_meetings_issues_user where meetings_id = #{meetingsId} and issues_id = #{issuesId}
            and report_people=#{reportPeople}
            )
    </select>

<!--    <select id="getuser" resultMap="com.ctsi.ssdc.admin.domain.dto.CscpUserDTO" parameterType="java.lang.Long">-->
<!--        SELECT a.id ,a.real_name,b.JOB_TITLE as post-->
<!--        FROM cscp_user a LEFT JOIN t_address_book b on a.id = b.USER_ID-->
<!--        where a.id in (select user_id from biz_meetings_issues_user where meetings_id = #{meetingsId} and issues_id = #{issuesId})-->
<!--    </select>-->

</mapper>
